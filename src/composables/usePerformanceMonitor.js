import { ref, computed } from 'vue'
import { retryRequest } from '../utils/axios'

export function usePerformanceMonitor() {
  // 성능 메트릭 상태
  const performanceMetrics = ref({
    responseTime: 0,
    dataSize: 0,
    dataCount: 0,
    timestamp: Date.now(),
    apiEndpoint: '',
    method: '',
    success: false
  })
  
  // 성능 히스토리
  const performanceHistory = ref([])
  
  // 실시간 모니터링 상태
  const isMonitoring = ref(false)
  const monitoringInterval = ref(null)
  
  // 성능 임계값 설정
  const thresholds = ref({
    slowResponseTime: 3000, // 3초 이상을 느린 응답으로 간주
    largeDataSize: 1024 * 1024, // 1MB 이상을 큰 데이터로 간주
    maxHistorySize: 100 // 최대 히스토리 개수
  })
  
  // 계산된 속성
  const averageResponseTime = computed(() => {
    if (performanceHistory.value.length === 0) return 0
    
    const total = performanceHistory.value.reduce((sum, metric) => sum + metric.responseTime, 0)
    return total / performanceHistory.value.length
  })
  
  const totalDataProcessed = computed(() => {
    return performanceHistory.value.reduce((sum, metric) => sum + metric.dataSize, 0)
  })
  
  const successRate = computed(() => {
    if (performanceHistory.value.length === 0) return 0
    
    const successCount = performanceHistory.value.filter(metric => metric.success).length
    return (successCount / performanceHistory.value.length) * 100
  })
  
  const slowRequests = computed(() => {
    return performanceHistory.value.filter(metric => 
      metric.responseTime > thresholds.value.slowResponseTime
    )
  })
  
  const largeDataRequests = computed(() => {
    return performanceHistory.value.filter(metric => 
      metric.dataSize > thresholds.value.largeDataSize
    )
  })
  
  // 성능 측정 함수
  const measurePerformance = async (apiCall, endpoint = '', method = '') => {
    const startTime = performance.now()
    const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0
    
    try {
      const result = await apiCall()
      const endTime = performance.now()
      const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0
      
      const responseTime = endTime - startTime
      const dataSize = JSON.stringify(result).length
      const memoryUsed = endMemory - startMemory
      
      const metric = {
        responseTime,
        dataSize,
        dataCount: result?.data?.length || 0,
        timestamp: Date.now(),
        apiEndpoint: endpoint,
        method: method,
        success: true,
        memoryUsed,
        result
      }
      
      // 메트릭 저장
      performanceMetrics.value = metric
      addToHistory(metric)
      
      console.log(`🚀 성능 지표: ${responseTime.toFixed(2)}ms, ${(dataSize / 1024).toFixed(2)}KB, ${result?.data?.length || 0}건`)
      
      return result
    } catch (error) {
      const endTime = performance.now()
      const metric = {
        responseTime: endTime - startTime,
        dataSize: 0,
        dataCount: 0,
        timestamp: Date.now(),
        apiEndpoint: endpoint,
        method: method,
        success: false,
        error: error.message,
        errorType: error.type || 'UNKNOWN'
      }
      
      performanceMetrics.value = metric
      addToHistory(metric)
      
      throw error
    }
  }
  
  // 재시도 로직이 포함된 성능 측정
  const measurePerformanceWithRetry = async (apiCall, endpoint = '', method = '', maxRetries = 3) => {
    return await retryRequest(async () => {
      return await measurePerformance(apiCall, endpoint, method)
    }, maxRetries)
  }
  
  // 히스토리에 메트릭 추가
  const addToHistory = (metric) => {
    performanceHistory.value.push(metric)
    
    // 히스토리 크기 제한
    if (performanceHistory.value.length > thresholds.value.maxHistorySize) {
      performanceHistory.value.shift()
    }
  }
  
  // 실시간 모니터링 시작
  const startMonitoring = (intervalMs = 5000) => {
    if (isMonitoring.value) return
    
    isMonitoring.value = true
    monitoringInterval.value = setInterval(() => {
      // 시스템 성능 체크
      checkSystemPerformance()
    }, intervalMs)
    
    console.log('📊 실시간 성능 모니터링 시작')
  }
  
  // 실시간 모니터링 중지
  const stopMonitoring = () => {
    if (monitoringInterval.value) {
      clearInterval(monitoringInterval.value)
      monitoringInterval.value = null
    }
    isMonitoring.value = false
    console.log('📊 실시간 성능 모니터링 중지')
  }
  
  // 시스템 성능 체크
  const checkSystemPerformance = () => {
    const memoryInfo = performance.memory ? {
      used: performance.memory.usedJSHeapSize,
      total: performance.memory.totalJSHeapSize,
      limit: performance.memory.jsHeapSizeLimit
    } : null
    
    const navigationInfo = performance.getEntriesByType('navigation')[0]
    
    console.log('📊 시스템 성능 체크:', {
      memory: memoryInfo,
      navigation: navigationInfo ? {
        loadTime: navigationInfo.loadEventEnd - navigationInfo.loadEventStart,
        domContentLoaded: navigationInfo.domContentLoadedEventEnd - navigationInfo.domContentLoadedEventStart
      } : null,
      timestamp: Date.now()
    })
  }
  
  // 성능 리포트 생성
  const generatePerformanceReport = () => {
    const report = {
      summary: {
        totalRequests: performanceHistory.value.length,
        averageResponseTime: averageResponseTime.value,
        totalDataProcessed: totalDataProcessed.value,
        successRate: successRate.value,
        slowRequests: slowRequests.value.length,
        largeDataRequests: largeDataRequests.value.length
      },
      details: {
        slowestRequest: performanceHistory.value.reduce((slowest, current) => 
          current.responseTime > slowest.responseTime ? current : slowest, 
          { responseTime: 0 }
        ),
        largestDataRequest: performanceHistory.value.reduce((largest, current) => 
          current.dataSize > largest.dataSize ? current : largest, 
          { dataSize: 0 }
        ),
        recentRequests: performanceHistory.value.slice(-10)
      },
      recommendations: []
    }
    
    // 성능 개선 권장사항 생성
    if (averageResponseTime.value > thresholds.value.slowResponseTime) {
      report.recommendations.push('평균 응답 시간이 느립니다. API 최적화를 고려해보세요.')
    }
    
    if (successRate.value < 95) {
      report.recommendations.push('API 성공률이 낮습니다. 오류 처리를 개선해보세요.')
    }
    
    if (slowRequests.value.length > 0) {
      report.recommendations.push(`${slowRequests.value.length}개의 느린 요청이 있습니다.`)
    }
    
    if (largeDataRequests.value.length > 0) {
      report.recommendations.push(`${largeDataRequests.value.length}개의 큰 데이터 요청이 있습니다. 페이징을 고려해보세요.`)
    }
    
    return report
  }
  
  // 성능 데이터 내보내기
  const exportPerformanceData = () => {
    const data = {
      metrics: performanceHistory.value,
      report: generatePerformanceReport(),
      exportTime: new Date().toISOString()
    }
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `performance-report-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
    
    console.log('📊 성능 데이터 내보내기 완료')
  }
  
  // 히스토리 초기화
  const clearHistory = () => {
    performanceHistory.value = []
    performanceMetrics.value = {
      responseTime: 0,
      dataSize: 0,
      dataCount: 0,
      timestamp: Date.now(),
      apiEndpoint: '',
      method: '',
      success: false
    }
    console.log('📊 성능 히스토리 초기화')
  }
  
  // 임계값 설정
  const updateThresholds = (newThresholds) => {
    thresholds.value = { ...thresholds.value, ...newThresholds }
    console.log('📊 성능 임계값 업데이트:', thresholds.value)
  }
  
  return {
    // 상태
    performanceMetrics,
    performanceHistory,
    isMonitoring,
    thresholds,
    
    // 계산된 속성
    averageResponseTime,
    totalDataProcessed,
    successRate,
    slowRequests,
    largeDataRequests,
    
    // 메서드
    measurePerformance,
    measurePerformanceWithRetry,
    startMonitoring,
    stopMonitoring,
    generatePerformanceReport,
    exportPerformanceData,
    clearHistory,
    updateThresholds
  }
} 