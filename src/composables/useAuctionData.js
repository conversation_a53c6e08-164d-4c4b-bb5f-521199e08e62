<<<<<<< HEAD
import { ref, computed } from 'vue'
import apiClient from '../utils/axios'
=======
import { ref, computed } from 'vue';
import apiClient from '@/utils/axios';

// 날짜를 YYYY-MM-DD 형식의 문자열로 변환하는 헬퍼 함수
const formatDateForApi = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// YYYYMMDD 형식으로 변환하는 헬퍼 함수 추가
const formatDateForCacheApi = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
}
>>>>>>> 5b06a882a15f1cb63f5d3a4fe55d04c6df04d2ea

export function useAuctionData() {
  const auctionData = ref([]);
  const loading = ref(false);
  const error = ref('');
  const caching = ref(false);

<<<<<<< HEAD
  // 성능 측정
  const performanceMetrics = ref({
    responseTime: 0,
    dataSize: 0,
    dataCount: 0,
    timestamp: Date.now()
  })

  // 경매 데이터 가져오기 (최적화된 GET 방식)
  const fetchAuctionData = async (append = false, searchParams = {}) => {
    if (!append) {
      loading.value = true
      error.value = ''
    }

    const startTime = performance.now()

    try {
      // 최적화된 GET 방식 API 호출
      let url = '/auction/garak'
      const params = new URLSearchParams()
      
      // 검색 조건이 있을 때만 추가
      if (searchParams.pumName) {
        params.append('pumName', searchParams.pumName)
      }
      if (searchParams.startDate) {
        params.append('startDate', searchParams.startDate)
      }
      if (searchParams.endDate) {
        params.append('endDate', searchParams.endDate)
      }
      
      // 페이징 파라미터
      params.append('startIndex', searchParams.startIndex || 1)
      params.append('endIndex', searchParams.endIndex || 100)
      
      if (params.toString()) {
        url += '?' + params.toString()
      }
      
      console.log('🚀 최적화된 경매 API 요청:', url)
      
      const response = await apiClient.get(url)
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      const dataSize = JSON.stringify(response.data).length
      
      // 성능 지표 업데이트
      performanceMetrics.value = {
        responseTime: responseTime,
        dataSize: dataSize,
        dataCount: response.data.data?.data?.length || 0,
        timestamp: Date.now()
      }
      
      console.log('🚀 성능 지표:', {
        responseTime: responseTime.toFixed(2) + 'ms',
        dataSize: (dataSize / 1024).toFixed(2) + 'KB',
        dataCount: response.data.data?.data?.length || 0
      })
=======
  const totalCount = computed(() => auctionData.value.length);

  /**
   * 경매 데이터를 조회합니다. (개선된 API 기준)
   * @param {object} params - { productName, date, startDate, endDate }
   */
  const fetchAuctionData = async (params = {}) => {
    loading.value = true;
    error.value = '';
    
    try {
      let url = '/auction/data';
      if (params.productName) {
        url += `/${params.productName}`;
      }
      
      const queryParams = new URLSearchParams();
      if (params.date) queryParams.append('date', params.date);
      if (params.startDate) queryParams.append('startDate', params.startDate);
      if (params.endDate) queryParams.append('endDate', params.endDate);
      
      const fullUrl = url + (queryParams.toString() ? `?${queryParams.toString()}` : '');
      const response = await apiClient.get(fullUrl);
>>>>>>> 5b06a882a15f1cb63f5d3a4fe55d04c6df04d2ea
      
      if (response.data.success) {
        // [수정] 실제 API 응답이 이미 camelCase이므로, 아무 변환 없이 데이터를 직접 할당합니다.
        const rawData = response.data.data || [];
        auctionData.value = rawData;
      } else {
        const innerMessage = response.data.message || '데이터를 불러오는데 실패했습니다.';
        throw new Error(innerMessage);
      }
    } catch (err) {
      console.error('경매 데이터 로드 실패:', err);
      error.value = err.message;
      auctionData.value = [];
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 특정 날짜의 경매 데이터를 수동으로 캐싱(새로고침)합니다.
   * @param {Date} date - 캐싱할 날짜 객체
   */
  const cacheDataForDate = async (date) => {
    caching.value = true;
    error.value = null;
    const dateString = formatDateForCacheApi(date);
    
    try {
      // [수정] API 요청 경로에서 맨 앞의 슬래시(/)를 제거하여 올바른 주소 생성
      const response = await apiClient.post(
        `auction/cache-day/${dateString}`,
        null, // No request body
        { timeout: 300000 } // 5분 타임아웃
      );
      if(response.data.success) {
          return response.data.data;
      } else {
          throw new Error(response.data.message || '데이터 새로고침에 실패했습니다.');
      }
    } catch (err) {
      console.error('데이터 캐싱 실패:', err);
      // [수정] 타임아웃 오류를 명확하게 구분하여 처리
      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        error.value = {
          type: 'TIMEOUT_ERROR',
          message: '요청 시간이 초과되었습니다. 서버가 데이터를 처리하는 데 시간이 오래 걸릴 수 있습니다. 잠시 후 다시 시도해주세요.'
        };
      } else {
        error.value = {
          type: 'NETWORK_ERROR',
          message: err.message || '알 수 없는 네트워크 오류가 발생했습니다.'
        };
      }
      return null;
    } finally {
      caching.value = false;
    }
  };

  const resetState = () => {
    auctionData.value = [];
    loading.value = false;
    error.value = '';
  };

  return {
    auctionData,
    loading,
    caching,
    error,
    totalCount,
<<<<<<< HEAD
    currentPage,
    pageSize,
    totalPages,
    performanceMetrics,
    
    // 메서드
=======
>>>>>>> 5b06a882a15f1cb63f5d3a4fe55d04c6df04d2ea
    fetchAuctionData,
    cacheDataForDate,
    resetState,
    formatDateForApi,
  };
} 