import { ref, computed } from 'vue';
import apiClient from '@/utils/axios';

// YYYYMMDD 형식으로 변환하는 헬퍼 함수 (API 가이드 문서 기준)
const formatDateForApi = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

// YYYY-MM-DD 형식으로 변환하는 헬퍼 함수 (일부 API에서 사용)
const formatDateForDisplay = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

export function useAuctionData() {
  const auctionData = ref([]);
  const loading = ref(false);
  const error = ref('');
  const caching = ref(false);

  const totalCount = computed(() => auctionData.value.length);

  /**
   * 경매 데이터를 조회합니다. (API 가이드 문서 기준)
   * 엔드포인트: GET /api/auction/garak (쿼리 파라미터 사용)
   * @param {object} params - { productName, date, startDate, endDate }
   */
  const fetchAuctionData = async (params = {}) => {
    loading.value = true;
    error.value = '';

    try {
      // API 가이드 문서에 따른 엔드포인트: /auction/garak
      const queryParams = new URLSearchParams();

      if (params.productName) queryParams.append('productName', params.productName);
      if (params.date) queryParams.append('date', params.date);
      if (params.startDate) queryParams.append('startDate', params.startDate);
      if (params.endDate) queryParams.append('endDate', params.endDate);

      const url = '/auction/garak' + (queryParams.toString() ? `?${queryParams.toString()}` : '');
      const response = await apiClient.get(url);

      if (response.data.success) {
        const rawData = response.data.data || [];
        auctionData.value = rawData;
      } else {
        const innerMessage = response.data.message || '데이터를 불러오는데 실패했습니다.';
        throw new Error(innerMessage);
      }
    } catch (err) {
      console.error('경매 데이터 로드 실패:', err);
      error.value = err.message;
      auctionData.value = [];
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 특정 날짜의 경매 데이터를 수동으로 캐싱(새로고침)합니다.
   * API 가이드 문서에 따라 SSE 방식 사용: GET /api/auction/cache-stream/{date}
   * @param {Date} date - 캐싱할 날짜 객체
   * @param {Function} onProgress - 진행률 콜백 함수 (선택적)
   * @param {Function} onLog - 로그 메시지 콜백 함수 (선택적)
   */
  const cacheDataForDate = async (date, onProgress = null, onLog = null) => {
    caching.value = true;
    error.value = null;
    const dateString = formatDateForApi(date);

    return new Promise((resolve, reject) => {
      try {
        // API 가이드 문서에 따른 SSE 엔드포인트 사용
        const eventSource = new EventSource(`${apiClient.defaults.baseURL}/auction/cache-stream/${dateString}`);

        let result = null;

        eventSource.onmessage = function(event) {
          try {
            const data = JSON.parse(event.data);

            // 로그 메시지 처리
            if (data.log && onLog) {
              onLog(data.log);
            }

            // 진행률 처리
            if (data.progress && onProgress) {
              onProgress(data.progress);
            }

            // 완료 데이터 처리 (가정)
            if (data.complete || data.saved !== undefined) {
              result = data;
            }
          } catch (parseError) {
            console.error('SSE 데이터 파싱 오류:', parseError);
          }
        };

        eventSource.onerror = function(error) {
          console.error('SSE 연결 오류:', error);
          eventSource.close();
          caching.value = false;

          error.value = {
            type: 'SSE_ERROR',
            message: 'SSE 연결 중 오류가 발생했습니다.'
          };
          reject(error);
        };

        eventSource.onopen = function() {
          console.log('SSE 연결 시작:', dateString);
        };

        // 타임아웃 설정 (5분)
        setTimeout(() => {
          if (eventSource.readyState !== EventSource.CLOSED) {
            eventSource.close();
            caching.value = false;
            resolve(result);
          }
        }, 300000);

      } catch (err) {
        console.error('데이터 캐싱 실패:', err);
        caching.value = false;

        error.value = {
          type: 'NETWORK_ERROR',
          message: err.message || '알 수 없는 네트워크 오류가 발생했습니다.'
        };
        reject(err);
      }
    });
  };

  const resetState = () => {
    auctionData.value = [];
    loading.value = false;
    error.value = '';
  };

  return {
    auctionData,
    loading,
    caching,
    error,
    totalCount,
    fetchAuctionData,
    cacheDataForDate,
    resetState,
    formatDateForApi,
    formatDateForDisplay,
  };
} 