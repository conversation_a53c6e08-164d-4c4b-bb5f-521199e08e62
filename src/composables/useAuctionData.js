import { ref, computed } from 'vue';
import apiClient from '@/utils/axios';

// 날짜를 YYYY-MM-DD 형식의 문자열로 변환하는 헬퍼 함수
const formatDateForApi = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// YYYYMMDD 형식으로 변환하는 헬퍼 함수 추가
const formatDateForCacheApi = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
}

export function useAuctionData() {
  const auctionData = ref([]);
  const loading = ref(false);
  const error = ref('');
  const caching = ref(false);

  const totalCount = computed(() => auctionData.value.length);

  /**
   * 경매 데이터를 조회합니다. (API 가이드 문서 기준)
   * @param {object} params - { productName, date, startDate, endDate }
   */
  const fetchAuctionData = async (params = {}) => {
    loading.value = true;
    error.value = '';

    try {
      // API 가이드 문서에 따른 엔드포인트 사용
      let url = '/auction/data';
      if (params.productName) {
        url += `/${params.productName}`;
      }

      const queryParams = new URLSearchParams();
      if (params.date) queryParams.append('date', params.date);
      if (params.startDate) queryParams.append('startDate', params.startDate);
      if (params.endDate) queryParams.append('endDate', params.endDate);

      const fullUrl = url + (queryParams.toString() ? `?${queryParams.toString()}` : '');
      const response = await apiClient.get(fullUrl);

      if (response.data.success) {
        const rawData = response.data.data || [];
        auctionData.value = rawData;
      } else {
        const innerMessage = response.data.message || '데이터를 불러오는데 실패했습니다.';
        throw new Error(innerMessage);
      }
    } catch (err) {
      console.error('경매 데이터 로드 실패:', err);
      error.value = err.message;
      auctionData.value = [];
    } finally {
      loading.value = false;
    }
  };
  
  /**
   * 특정 날짜의 경매 데이터를 수동으로 캐싱(새로고침)합니다.
   * API 가이드 문서에 따라 POST /api/auction/garak/load-date-data 사용
   * @param {Date} date - 캐싱할 날짜 객체
   */
  const cacheDataForDate = async (date) => {
    caching.value = true;
    error.value = null;
    const dateString = formatDateForCacheApi(date);

    try {
      // API 가이드 문서에 따른 엔드포인트 사용
      const response = await apiClient.post(
        '/auction/garak/load-date-data',
        { targetDate: dateString }, // API 가이드에 따른 요청 본문
        { timeout: 300000 } // 5분 타임아웃
      );

      if(response.data.success) {
          return response.data.data;
      } else {
          throw new Error(response.data.message || '데이터 새로고침에 실패했습니다.');
      }
    } catch (err) {
      console.error('데이터 캐싱 실패:', err);
      // 타임아웃 오류를 명확하게 구분하여 처리
      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        error.value = {
          type: 'TIMEOUT_ERROR',
          message: '요청 시간이 초과되었습니다. 서버가 데이터를 처리하는 데 시간이 오래 걸릴 수 있습니다. 잠시 후 다시 시도해주세요.'
        };
      } else {
        error.value = {
          type: 'NETWORK_ERROR',
          message: err.message || '알 수 없는 네트워크 오류가 발생했습니다.'
        };
      }
      return null;
    } finally {
      caching.value = false;
    }
  };

  const resetState = () => {
    auctionData.value = [];
    loading.value = false;
    error.value = '';
  };

  return {
    auctionData,
    loading,
    caching,
    error,
    totalCount,
    fetchAuctionData,
    cacheDataForDate,
    resetState,
    formatDateForApi,
  };
} 