import { ref, computed } from 'vue'
import apiClient from '../utils/axios'

export function useAuctionData() {
  // 상태 관리
  const auctionData = ref([])
  const allAuctionData = ref([]) // 전체 데이터 저장
  const loading = ref(false)
  const error = ref('')
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = 100

  // 성능 측정
  const performanceMetrics = ref({
    responseTime: 0,
    dataSize: 0,
    dataCount: 0,
    timestamp: Date.now()
  })

  // 경매 데이터 가져오기 (최적화된 GET 방식)
  const fetchAuctionData = async (append = false, searchParams = {}) => {
    if (!append) {
      loading.value = true
      error.value = ''
    }

    const startTime = performance.now()

    try {
      // 최적화된 GET 방식 API 호출
      let url = '/auction/garak'
      const params = new URLSearchParams()
      
      // 검색 조건이 있을 때만 추가
      if (searchParams.pumName) {
        params.append('pumName', searchParams.pumName)
      }
      if (searchParams.startDate) {
        params.append('startDate', searchParams.startDate)
      }
      if (searchParams.endDate) {
        params.append('endDate', searchParams.endDate)
      }
      
      // 페이징 파라미터
      params.append('startIndex', searchParams.startIndex || 1)
      params.append('endIndex', searchParams.endIndex || 100)
      
      if (params.toString()) {
        url += '?' + params.toString()
      }
      
      console.log('🚀 최적화된 경매 API 요청:', url)
      
      const response = await apiClient.get(url)
      
      const endTime = performance.now()
      const responseTime = endTime - startTime
      const dataSize = JSON.stringify(response.data).length
      
      // 성능 지표 업데이트
      performanceMetrics.value = {
        responseTime: responseTime,
        dataSize: dataSize,
        dataCount: response.data.data?.data?.length || 0,
        timestamp: Date.now()
      }
      
      console.log('🚀 성능 지표:', {
        responseTime: responseTime.toFixed(2) + 'ms',
        dataSize: (dataSize / 1024).toFixed(2) + 'KB',
        dataCount: response.data.data?.data?.length || 0
      })
      
      console.log('경매 API 응답:', response.data)
      
      // 통합 API 가이드에 따른 응답 처리
      if (response.data.success) {
        // ApiResponse<T> 형식: response.data.data로 실제 데이터 접근
        const responseData = response.data.data
        
        // 통합 가이드에 따른 구조: data.data (배열) + data.dataCount
        const auctionDataFromResponse = responseData.data || []
        const totalCountFromResponse = responseData.dataCount || 0
        
        console.log('통합 API 구조에서 데이터 추출:', {
          dataLength: auctionDataFromResponse.length,
          totalCount: totalCountFromResponse,
          firstItem: auctionDataFromResponse[0]
        })
        
        if (append) {
          // 더 보기: 전체 데이터에서 다음 페이지 추가
          const startIndex = (currentPage.value - 1) * pageSize
          const endIndex = currentPage.value * pageSize
          const nextPageData = allAuctionData.value.slice(startIndex, endIndex)
          auctionData.value = [...auctionData.value, ...nextPageData]
        } else {
          // 새 검색: 전체 데이터 저장하고 첫 페이지만 표시
          allAuctionData.value = auctionDataFromResponse
          currentPage.value = 1
          const firstPageData = auctionDataFromResponse.slice(0, pageSize)
          auctionData.value = firstPageData
        }
        
        totalCount.value = totalCountFromResponse
      } else {
        // ApiResponse 형식의 오류 처리
        error.value = response.data.message || '데이터를 불러오는데 실패했습니다.'
      }
    } catch (err) {
      console.error('경매 데이터 로드 실패:', err)
      error.value = '서버 오류가 발생했습니다.'
    } finally {
      loading.value = false
    }
  }

  // 검색 실행
  const searchAuction = (searchParams) => {
    currentPage.value = 1
    auctionData.value = []
    fetchAuctionData(false, searchParams)
  }

  // 더 보기 (무한 스크롤)
  const loadMore = () => {
    console.log('🔄 loadMore 호출됨:', {
      currentPage: currentPage.value,
      totalPages: totalPages.value,
      allDataLength: allAuctionData.value.length,
      currentDataLength: auctionData.value.length,
      pageSize: pageSize
    })
    
    if (currentPage.value < totalPages.value) {
      currentPage.value++
      // API 호출 없이 로컬 데이터에서 다음 페이지 로드
      const startIndex = (currentPage.value - 1) * pageSize
      const endIndex = currentPage.value * pageSize
      const nextPageData = allAuctionData.value.slice(startIndex, endIndex)
      
      console.log('📥 다음 페이지 데이터 로드:', {
        startIndex,
        endIndex,
        nextPageDataLength: nextPageData.length,
        newCurrentPage: currentPage.value
      })
      
      auctionData.value = [...auctionData.value, ...nextPageData]
      
      console.log('✅ 데이터 업데이트 완료:', {
        totalDisplayed: auctionData.value.length,
        totalAvailable: allAuctionData.value.length
      })
    } else {
      console.log('🚫 더 이상 로드할 페이지 없음:', {
        currentPage: currentPage.value,
        totalPages: totalPages.value
      })
    }
  }

  // 페이지 계산
  const totalPages = computed(() => {
    return Math.ceil(totalCount.value / pageSize)
  })

  // 상태 초기화
  const resetState = () => {
    auctionData.value = []
    allAuctionData.value = []
    loading.value = false
    error.value = ''
    totalCount.value = 0
    currentPage.value = 1
  }

  return {
    // 상태
    auctionData,
    loading,
    error,
    totalCount,
    currentPage,
    pageSize,
    totalPages,
    performanceMetrics,
    
    // 메서드
    fetchAuctionData,
    searchAuction,
    loadMore,
    resetState
  }
} 