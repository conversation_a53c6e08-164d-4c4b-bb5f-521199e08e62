import { ref, computed } from 'vue'
import apiClient from '../utils/axios'

export function useDataQuality() {
  // 상태 관리
  const loading = ref(false)
  const error = ref('')
  const success = ref('')
  
  // 데이터 품질 상태
  const qualityStatus = ref({
    totalCount: 0,
    emptyPumNmCount: 0,
    nullProductIdCount: 0,
    invalidDateCount: 0,
    emptyPumNmRatio: 0,
    nullProductIdRatio: 0,
    invalidDateRatio: 0,
    qualityStatus: 'UNKNOWN'
  })
  
  // 정리 결과
  const cleanupResult = ref({
    testDataDeleted: 0,
    invalidLengthDeleted: 0,
    patternTestDeleted: 0,
    totalDeleted: 0
  })
  
  // 계산된 속성
  const qualityScore = computed(() => {
    const { emptyPumNmRatio, nullProductIdRatio, invalidDateRatio } = qualityStatus.value
    
    // 품질 점수 계산 (100점 만점)
    let score = 100
    
    // 각 비율에 따른 점수 차감
    score -= emptyPumNmRatio * 10
    score -= nullProductIdRatio * 5
    score -= invalidDateRatio * 10
    
    return Math.max(0, Math.round(score))
  })
  
  const qualityLevel = computed(() => {
    const score = qualityScore.value
    
    if (score >= 90) return { level: 'EXCELLENT', color: '#27ae60', icon: '🟢' }
    if (score >= 80) return { level: 'GOOD', color: '#2ecc71', icon: '🟢' }
    if (score >= 70) return { level: 'WARNING', color: '#f39c12', icon: '🟡' }
    if (score >= 60) return { level: 'POOR', color: '#e67e22', icon: '🟠' }
    return { level: 'CRITICAL', color: '#e74c3c', icon: '🔴' }
  })
  
  // 1. 데이터 품질 상태 확인
  const checkQualityStatus = async () => {
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      const response = await apiClient.get('/auction/garak/quality-status')
      
      if (response.data.success) {
        qualityStatus.value = response.data.data
        success.value = '데이터 품질 상태 확인 완료'
        
        console.log('📊 데이터 품질 상태:', {
          totalCount: qualityStatus.value.totalCount,
          qualityScore: qualityScore.value,
          qualityLevel: qualityLevel.value.level,
          emptyPumNmRatio: qualityStatus.value.emptyPumNmRatio.toFixed(2) + '%',
          nullProductIdRatio: qualityStatus.value.nullProductIdRatio.toFixed(2) + '%'
        })
        
        return response.data.data
      } else {
        throw new Error(response.data.message || '품질 상태 확인 실패')
      }
    } catch (err) {
      handleError(err)
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 2. 테스트 데이터 정리
  const cleanupTestData = async () => {
    if (!confirm('테스트 데이터를 정리하시겠습니까? 이 작업은 되돌릴 수 없습니다.')) {
      return false
    }
    
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      const response = await apiClient.post('/auction/garak/cleanup-test')
      
      if (response.data.success) {
        cleanupResult.value = response.data.data
        success.value = `테스트 데이터 정리 완료! (${cleanupResult.value.totalDeleted}건 삭제)`
        
        console.log('🧹 테스트 데이터 정리 결과:', cleanupResult.value)
        
        // 정리 후 품질 상태 다시 확인
        await checkQualityStatus()
        
        return response.data.data
      } else {
        throw new Error(response.data.message || '테스트 데이터 정리 실패')
      }
    } catch (err) {
      handleError(err)
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 3. 공백 데이터 정리
  const cleanupEmptyData = async () => {
    if (!confirm('공백 데이터를 정리하시겠습니까? 이 작업은 되돌릴 수 없습니다.')) {
      return false
    }
    
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      const response = await apiClient.post('/auction/garak/cleanup')
      
      if (response.data.success) {
        success.value = '공백 데이터 정리 완료!'
        
        console.log('🧹 공백 데이터 정리 완료')
        
        // 정리 후 품질 상태 다시 확인
        await checkQualityStatus()
        
        return response.data.data
      } else {
        throw new Error(response.data.message || '공백 데이터 정리 실패')
      }
    } catch (err) {
      handleError(err)
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 4. Product ID 연동
  const syncProductIds = async () => {
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      const response = await apiClient.post('/auction/garak/sync-product-ids')
      
      if (response.data.success) {
        success.value = 'Product ID 연동 완료!'
        
        console.log('🔗 Product ID 연동 완료')
        
        // 연동 후 품질 상태 다시 확인
        await checkQualityStatus()
        
        return response.data.data
      } else {
        throw new Error(response.data.message || 'Product ID 연동 실패')
      }
    } catch (err) {
      handleError(err)
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 5. 전체 정리 프로세스
  const runFullCleanup = async () => {
    if (!confirm('전체 데이터 정리 프로세스를 실행하시겠습니까?\n\n1. 공백 데이터 정리\n2. 테스트 데이터 정리\n3. Product ID 연동\n\n이 작업은 되돌릴 수 없습니다.')) {
      return false
    }
    
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      console.log('🚀 전체 데이터 정리 프로세스 시작')
      
      // 1단계: 공백 데이터 정리
      console.log('1단계: 공백 데이터 정리 중...')
      await cleanupEmptyData()
      
      // 2단계: 테스트 데이터 정리
      console.log('2단계: 테스트 데이터 정리 중...')
      await cleanupTestData()
      
      // 3단계: Product ID 연동
      console.log('3단계: Product ID 연동 중...')
      await syncProductIds()
      
      // 4단계: 최종 품질 상태 확인
      console.log('4단계: 최종 품질 상태 확인 중...')
      await checkQualityStatus()
      
      success.value = '전체 데이터 정리 프로세스 완료!'
      console.log('✅ 전체 데이터 정리 프로세스 완료')
      
      return true
    } catch (err) {
      handleError(err)
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 6. 에러 처리
  const handleError = (err) => {
    if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
      error.value = '요청 시간이 초과되었습니다. 서버 상태를 확인해주세요.'
    } else if (err.type === 'NETWORK_ERROR') {
      error.value = '네트워크 연결을 확인해주세요. 서버가 실행 중인지 확인해주세요.'
    } else if (err.response?.status === 404) {
      error.value = '요청한 API를 찾을 수 없습니다.'
    } else if (err.response?.status >= 500) {
      error.value = '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.'
    } else {
      error.value = err.response?.data?.message || err.message || '알 수 없는 오류가 발생했습니다.'
    }
    
    console.error('🚨 데이터 품질 관리 오류:', {
      message: err.message,
      status: err.response?.status,
      data: err.response?.data,
      type: err.type,
      code: err.code
    })
  }
  
  // 7. 상태 초기화
  const resetState = () => {
    qualityStatus.value = {
      totalCount: 0,
      emptyPumNmCount: 0,
      nullProductIdCount: 0,
      invalidDateCount: 0,
      emptyPumNmRatio: 0,
      nullProductIdRatio: 0,
      invalidDateRatio: 0,
      qualityStatus: 'UNKNOWN'
    }
    
    cleanupResult.value = {
      testDataDeleted: 0,
      invalidLengthDeleted: 0,
      patternTestDeleted: 0,
      totalDeleted: 0
    }
    
    error.value = ''
    success.value = ''
  }
  
  return {
    // 상태
    loading,
    error,
    success,
    qualityStatus,
    cleanupResult,
    
    // 계산된 속성
    qualityScore,
    qualityLevel,
    
    // 메서드
    checkQualityStatus,
    cleanupTestData,
    cleanupEmptyData,
    syncProductIds,
    runFullCleanup,
    resetState,
    handleError
  }
} 