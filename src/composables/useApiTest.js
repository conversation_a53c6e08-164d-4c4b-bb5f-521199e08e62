import { ref } from 'vue';
import apiClient from '@/utils/axios';

export function useApiTest() {
  const testResults = ref([]);
  const testing = ref(false);

  const testEndpoint = async (name, method, url, data = null) => {
    const startTime = Date.now();
    try {
      let response;
      switch (method.toUpperCase()) {
        case 'GET':
          response = await apiClient.get(url);
          break;
        case 'POST':
          response = await apiClient.post(url, data);
          break;
        default:
          throw new Error(`Unsupported method: ${method}`);
      }
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      testResults.value.push({
        name,
        method,
        url,
        status: 'SUCCESS',
        statusCode: response.status,
        duration: `${duration}ms`,
        data: response.data,
        timestamp: new Date().toLocaleTimeString()
      });
      
      return response.data;
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      testResults.value.push({
        name,
        method,
        url,
        status: 'ERROR',
        statusCode: error.response?.status || 'N/A',
        duration: `${duration}ms`,
        error: error.message,
        details: error.response?.data || error,
        timestamp: new Date().toLocaleTimeString()
      });
      
      throw error;
    }
  };

  const runAllTests = async () => {
    testing.value = true;
    testResults.value = [];
    
    console.log('🧪 API 테스트 시작...');
    
    // 1. 경매 데이터 조회 테스트
    try {
      await testEndpoint('경매 데이터 조회 (전체)', 'GET', '/auction/garak');
      await testEndpoint('경매 데이터 조회 (상추)', 'GET', '/auction/garak?productName=상추');
      await testEndpoint('경매 데이터 조회 (날짜)', 'GET', '/auction/garak?productName=상추&date=20250729');
    } catch (error) {
      console.error('경매 데이터 API 테스트 실패:', error);
    }

    // 2. 작물 관리 API 테스트
    try {
      await testEndpoint('작물 목록 조회', 'GET', '/crops');
      await testEndpoint('현재 경매가 조회', 'GET', '/crops/current-auction-prices');
    } catch (error) {
      console.error('작물 관리 API 테스트 실패:', error);
    }

    // 3. 직원 관리 API 테스트
    try {
      await testEndpoint('직원 목록 조회', 'GET', '/employees');
      await testEndpoint('외국인 직원 조회', 'GET', '/employees/foreigners');
    } catch (error) {
      console.error('직원 관리 API 테스트 실패:', error);
    }

    // 4. 캐싱 API 테스트 (SSE는 별도로)
    try {
      // SSE 테스트는 복잡하므로 일단 제외
      console.log('📡 SSE 캐싱 API는 별도 테스트 필요');
    } catch (error) {
      console.error('캐싱 API 테스트 실패:', error);
    }

    testing.value = false;
    console.log('🧪 API 테스트 완료');
    
    return testResults.value;
  };

  const clearResults = () => {
    testResults.value = [];
  };

  const getSuccessCount = () => {
    return testResults.value.filter(result => result.status === 'SUCCESS').length;
  };

  const getErrorCount = () => {
    return testResults.value.filter(result => result.status === 'ERROR').length;
  };

  return {
    testResults,
    testing,
    testEndpoint,
    runAllTests,
    clearResults,
    getSuccessCount,
    getErrorCount
  };
}
