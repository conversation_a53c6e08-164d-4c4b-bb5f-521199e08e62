import { ref } from 'vue';
import { useHarvestManagement } from './useHarvestManagement';

export function useHarvestDashboard() {
  const {
    getIncompleteStatistics,
    getReminderTargets,
    getWeeklyReport,
    // 다른 필요한 함수들도 가져올 수 있습니다.
  } = useHarvestManagement();
  
  const loading = ref(false);
  const error = ref('');

  const statistics = ref(null);
  const reminderTargets = ref([]);
  const crops = ref([]);
  const weeklyReport = ref(null);

  // 여러 API 호출을 통합하는 함수
  const loadAllDashboardData = async () => {
    loading.value = true;
    error.value = '';
    
    try {
      const [statsRes, remindersRes, cropsRes, reportRes] = await Promise.all([
        getIncompleteStatistics(),
        getReminderTargets(),
        apiClient.get('/crops'), // API 가이드 문서에 따른 엔드포인트 사용
        getWeeklyReport(),
      ]);

      if (statsRes.success) {
        statistics.value = statsRes.data;
      } else {
        throw new Error('Failed to load statistics');
      }

      if (remindersRes.success) {
        reminderTargets.value = remindersRes.data;
      } else {
        // 이 부분은 에러를 던지지 않고 비워둘 수 있습니다. (선택적 데이터)
        reminderTargets.value = [];
        console.warn('Could not load reminder targets');
      }
      
      if (cropsRes.data.success) {
          crops.value = cropsRes.data.data;
      } else {
          throw new Error('Failed to load crop data');
      }

      if (reportRes.success) {
        weeklyReport.value = reportRes.data;
      } else {
        weeklyReport.value = null;
        console.warn('Could not load weekly report');
      }
      
    } catch (err) {
      console.error('Failed to load dashboard data:', err);
      error.value = err.message;
    } finally {
      loading.value = false;
    }
  };

  // 차트 데이터 가공 로직 (예시)
  const getProgressChartData = () => {
    if (!crops.value || crops.value.length === 0) return null;
    const statusCounts = crops.value.reduce((acc, crop) => {
        acc[crop.harvestStatus] = (acc[crop.harvestStatus] || 0) + 1;
        return acc;
    }, {});

    return {
        labels: ['시작 전', '진행중', '완료', '실패'],
        datasets: [{
            data: [
                statusCounts['NOT_STARTED'] || 0,
                statusCounts['IN_PROGRESS'] || 0,
                statusCounts['COMPLETED'] || 0,
                statusCounts['FAILED'] || 0,
            ],
            backgroundColor: ['#95a5a6', '#f39c12', '#27ae60', '#e74c3c'],
        }]
    };
  };

  return {
    loading,
    error,
    statistics,
    reminderTargets,
    crops,
    weeklyReport,
    loadAllDashboardData,
    getProgressChartData,
  };
} 