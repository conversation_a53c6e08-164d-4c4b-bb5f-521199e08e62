import { ref, computed } from 'vue'
import { apiUtils } from '@/utils/axios'

/**
 * 🚀 API 에러 처리를 위한 Composable
 * 
 * @returns {Object} 에러 상태와 처리 함수들
 */
export function useApiError() {
  // 📊 에러 상태 관리
  const error = ref(null)
  const isLoading = ref(false)
  const retryCount = ref(0)
  const maxRetries = 3

  // 🎯 계산된 속성들
  const hasError = computed(() => error.value !== null)
  const canRetry = computed(() => 
    error.value && 
    apiUtils.isRetryable(error.value) && 
    retryCount.value < maxRetries
  )
  const errorIcon = computed(() => 
    error.value ? apiUtils.getErrorIcon(error.value.type) : '❓'
  )
  const errorColor = computed(() => 
    error.value ? apiUtils.getErrorColor(error.value.type) : '#95a5a6'
  )

  /**
   * 🚨 API 에러 처리 함수
   * @param {Object} err - Axios 에러 객체
   * @param {string} context - 에러 발생 컨텍스트 (예: '작물 목록 로드')
   */
  const handleApiError = (err, context = '') => {
    console.error(`❌ ${context} 에러:`, err)
    
    // ApiResponse 형식의 오류 처리
    if (err.isApiResponseError && err.response?.data) {
      const apiResponse = err.response.data
      error.value = {
        type: 'API_RESPONSE_ERROR',
        message: apiResponse.message || `${context} 중 오류가 발생했습니다`,
        context,
        timestamp: new Date().toISOString(),
        details: {
          apiError: apiResponse.error,
          timestamp: apiResponse.timestamp,
          originalError: err
        },
        retryable: false
      }
    }
    // 에러 객체가 이미 처리된 형태인지 확인
    else if (err.type && err.message) {
      error.value = {
        ...err,
        context,
        timestamp: new Date().toISOString()
      }
    } else {
      // 기본 에러 처리
      error.value = {
        type: 'UNKNOWN_ERROR',
        message: `${context} 중 오류가 발생했습니다: ${err.message}`,
        context,
        timestamp: new Date().toISOString(),
        details: err,
        retryable: false
      }
    }
    
    // 재시도 카운트 증가
    if (apiUtils.isRetryable(error.value)) {
      retryCount.value++
    }
  }

  /**
   * 🔄 에러 재시도 함수
   * @param {Function} retryFunction - 재시도할 함수
   * @param {number} delay - 재시도 전 대기 시간 (ms)
   */
  const retry = async (retryFunction, delay = 1000) => {
    if (!canRetry.value) {
      console.warn('⚠️ 재시도 횟수 초과 또는 재시도 불가능한 에러')
      return
    }

    console.log(`🔄 재시도 중... (${retryCount.value}/${maxRetries})`)
    
    // 잠시 대기 후 재시도
    await new Promise(resolve => setTimeout(resolve, delay))
    
    try {
      clearError()
      await retryFunction()
    } catch (err) {
      handleApiError(err, error.value?.context || '재시도')
    }
  }

  /**
   * ✨ 에러 초기화
   */
  const clearError = () => {
    error.value = null
    retryCount.value = 0
  }

  /**
   * 🔄 로딩 상태 관리
   * @param {boolean} loading - 로딩 상태
   */
  const setLoading = (loading) => {
    isLoading.value = loading
  }

  /**
   * 🎯 안전한 API 호출 래퍼
   * @param {Function} apiCall - API 호출 함수
   * @param {string} context - 에러 컨텍스트
   * @param {boolean} showLoading - 로딩 상태 표시 여부
   */
  const safeApiCall = async (apiCall, context = '', showLoading = true) => {
    if (showLoading) {
      setLoading(true)
    }
    
    try {
      clearError()
      const result = await apiCall()
      return result
    } catch (err) {
      handleApiError(err, context)
      throw err
    } finally {
      if (showLoading) {
        setLoading(false)
      }
    }
  }

  /**
   * 📊 에러 통계 정보
   */
  const errorStats = computed(() => ({
    totalErrors: retryCount.value,
    canRetry: canRetry.value,
    maxRetries: maxRetries,
    remainingRetries: maxRetries - retryCount.value
  }))

  return {
    // 📊 상태
    error,
    isLoading,
    hasError,
    canRetry,
    errorIcon,
    errorColor,
    errorStats,
    
    // 🛠️ 함수들
    handleApiError,
    retry,
    clearError,
    setLoading,
    safeApiCall
  }
} 