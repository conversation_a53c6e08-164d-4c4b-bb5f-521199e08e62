import { ref, computed } from 'vue'
import apiClient from '../utils/axios'

export function useAuctionInput() {
  // 상태 관리
  const loading = ref(false)
  const error = ref('')
  const success = ref('')
  
  // 입력 폼 데이터
  const formData = ref({
    PUM_NM: '', // 품목명
    ADJ_DT: '', // 거래일자 (YYYYMMDD)
    MAX_PRC: '', // 최고가
    MIN_PRC: '', // 최저가
    AVG_PRC: '', // 평균가
    SALE_QTY: '', // 판매수량
    SALE_AMT: '' // 판매금액
  })
  
  // 대량 입력 데이터
  const bulkData = ref([])
  const bulkLoading = ref(false)
  const bulkProgress = ref(0)
  
  // SSE 스트리밍 상태 관리
  const status = ref('')
  const isLoading = ref(false)
  const progress = ref(0)
  const logs = ref([])
  const eventSource = ref(null)
  
  // 데이터 검증 함수
  const validateProductName = (pumNm) => {
    if (!pumNm || pumNm.trim().length === 0) {
      return { valid: false, message: '품목명을 입력해주세요.' }
    }
    
    // 테스트 키워드 체크
    const testKeywords = ['test', 'TEST', '테스트', '임시', 'dummy', 'sample']
    for (const keyword of testKeywords) {
      if (pumNm.includes(keyword)) {
        return { valid: false, message: '테스트 키워드가 포함되어 있습니다.' }
      }
    }
    
    // 길이 체크
    if (pumNm.length < 2) {
      return { valid: false, message: '품목명은 2자 이상이어야 합니다.' }
    }
    
    if (pumNm.length > 50) {
      return { valid: false, message: '품목명은 50자 이하여야 합니다.' }
    }
    
    return { valid: true, message: '' }
  }
  
  // 계산된 속성
  const isFormValid = computed(() => {
    const pumNmValidation = validateProductName(formData.value.PUM_NM)
    return pumNmValidation.valid && 
           formData.value.ADJ_DT && 
           formData.value.AVG_PRC
  })
  
  const formattedDate = computed(() => {
    if (!formData.value.ADJ_DT) return ''
    const date = formData.value.ADJ_DT
    return `${date.substring(0, 4)}-${date.substring(4, 6)}-${date.substring(6, 8)}`
  })
  
  // 단일 데이터 입력
  const submitAuctionData = async () => {
    // 데이터 검증
    const pumNmValidation = validateProductName(formData.value.PUM_NM)
    if (!pumNmValidation.valid) {
      error.value = pumNmValidation.message
      return false
    }
    
    if (!formData.value.ADJ_DT || !formData.value.AVG_PRC) {
      error.value = '필수 필드를 모두 입력해주세요.'
      return false
    }
    
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      const response = await apiClient.post('/auction/garak', {
        pumName: formData.value.PUM_NM,
        data: [{
          PUM_NM: formData.value.PUM_NM,
          ADJ_DT: formData.value.ADJ_DT,
          MAX_PRC: parseInt(formData.value.MAX_PRC) || 0,
          MIN_PRC: parseInt(formData.value.MIN_PRC) || 0,
          AVG_PRC: parseInt(formData.value.AVG_PRC) || 0,
          SALE_QTY: parseInt(formData.value.SALE_QTY) || 0,
          SALE_AMT: parseInt(formData.value.SALE_AMT) || 0
        }]
      })
      
      if (response.data.success) {
        success.value = '경매 데이터가 성공적으로 입력되었습니다.'
        resetForm()
        return true
      } else {
        error.value = response.data.message || '데이터 입력에 실패했습니다.'
        return false
      }
    } catch (err) {
      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        error.value = '요청 시간이 초과되었습니다. 백엔드 서버가 실행 중인지 확인해주세요.'
      } else if (err.type === 'NETWORK_ERROR') {
        error.value = '네트워크 연결을 확인해주세요. 서버가 실행 중인지 확인해주세요.'
      } else {
        error.value = err.response?.data?.message || err.message || '서버 오류가 발생했습니다.'
      }
      console.error('API 오류 상세:', err)
      return false
    } finally {
      loading.value = false
    }
  }
  

  
  // 금일 데이터 불러오기 (SSE 스트리밍)
  const loadTodayData = async () => {
    isLoading.value = true
    progress.value = 0
    logs.value = []
    status.value = '금일 데이터를 불러오는 중...'
    error.value = ''
    success.value = ''

    try {
      eventSource.value = new EventSource('http://localhost:8080/api/auction/garak/load-real-data/stream')
      
      eventSource.value.onmessage = (event) => {
        const data = JSON.parse(event.data)
        
        switch (data.type) {
          case 'log':
            logs.value.push(data.message)
            break
          case 'progress':
            progress.value = data.progress
            break
          case 'complete':
            eventSource.value.close()
            isLoading.value = false
            if (data.success) {
              status.value = `✅ 금일 데이터 불러오기 완료! (${data.totalSaved}건 저장)`
            } else {
              status.value = `❌ 오류: ${data.error}`
            }
            break
        }
      }

      eventSource.value.onerror = () => {
        eventSource.value.close()
        isLoading.value = false
        status.value = '❌ 스트리밍 연결 오류'
      }

    } catch (error) {
      isLoading.value = false
      status.value = `❌ 오류: ${error.message}`
    }
  }
  
  // 3년치 대량 데이터 불러오기 (SSE 스트리밍)
  const loadBulkData = async () => {
    isLoading.value = true
    progress.value = 0
    logs.value = []
    status.value = '금일부터 3년치 대량 데이터를 불러오는 중... (약 30분 소요)'
    error.value = ''
    success.value = ''

    try {
      eventSource.value = new EventSource('http://localhost:8080/api/auction/garak/bulk-insert/stream')
      
      eventSource.value.onmessage = (event) => {
        const data = JSON.parse(event.data)
        
        switch (data.type) {
          case 'log':
            logs.value.push(data.message)
            break
          case 'progress':
            progress.value = data.progress
            break
          case 'complete':
            eventSource.value.close()
            isLoading.value = false
            if (data.success) {
              status.value = `✅ 3년치 대량 데이터 불러오기 완료! (${data.totalSaved}건 저장)`
            } else {
              status.value = `❌ 오류: ${data.error}`
            }
            break
        }
      }

      eventSource.value.onerror = () => {
        eventSource.value.close()
        isLoading.value = false
        status.value = '❌ 스트리밍 연결 오류'
      }

    } catch (error) {
      isLoading.value = false
      status.value = `❌ 오류: ${error.message}`
    }
  }
  

  
  // 상품 초기화
  const initializeProducts = async () => {
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      const response = await apiClient.post('/products/initialize')
      
      if (response.data.success) {
        success.value = '상품 목록이 초기화되었습니다.'
        return true
      } else {
        error.value = response.data.message || '상품 초기화에 실패했습니다.'
        return false
      }
    } catch (err) {
      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        error.value = '요청 시간이 초과되었습니다. 백엔드 서버가 실행 중인지 확인해주세요.'
      } else if (err.type === 'NETWORK_ERROR') {
        error.value = '네트워크 연결을 확인해주세요. 서버가 실행 중인지 확인해주세요.'
      } else {
        error.value = err.response?.data?.message || err.message || '서버 오류가 발생했습니다.'
      }
      console.error('API 오류 상세:', err)
      return false
    } finally {
      loading.value = false
    }
  }

  // 테이블 초기화 (관리자 전용)
  const truncateTable = async () => {
    if (!confirm('정말로 모든 경매 데이터를 삭제하시겠습니까? 이 작업은 되돌릴 수 없습니다.')) {
      return false
    }
    
    loading.value = true
    error.value = ''
    success.value = ''
    
    try {
      const response = await apiClient.delete('/auction/garak/truncate')
      
      if (response.data.success) {
        success.value = '경매 데이터 테이블이 초기화되었습니다.'
        return true
      } else {
        error.value = response.data.message || '테이블 초기화에 실패했습니다.'
        return false
      }
    } catch (err) {
      if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
        error.value = '요청 시간이 초과되었습니다. 백엔드 서버가 실행 중인지 확인해주세요.'
      } else if (err.type === 'NETWORK_ERROR') {
        error.value = '네트워크 연결을 확인해주세요. 서버가 실행 중인지 확인해주세요.'
      } else {
        error.value = err.response?.data?.message || err.message || '서버 오류가 발생했습니다.'
      }
      console.error('API 오류 상세:', err)
      return false
    } finally {
      loading.value = false
    }
  }
  
  // 폼 초기화
  const resetForm = () => {
    formData.value = {
      PUM_NM: '',
      ADJ_DT: '',
      MAX_PRC: '',
      MIN_PRC: '',
      AVG_PRC: '',
      SALE_QTY: '',
      SALE_AMT: ''
    }
  }
  

  
  // 오류 초기화
  const clearError = () => {
    error.value = ''
  }
  
  // 성공 메시지 초기화
  const clearSuccess = () => {
    success.value = ''
  }
  
  return {
    // 상태
    loading,
    error,
    success,
    formData,
    bulkData,
    bulkLoading,
    bulkProgress,
    
    // SSE 스트리밍 상태
    progress,
    logs,
    status,
    isLoading,
    eventSource,
    
    // 계산된 속성
    isFormValid,
    formattedDate,
    
    // 메서드
    submitAuctionData,
    loadTodayData,
    loadBulkData,
    initializeProducts,
    truncateTable,
    resetForm,
    clearError,
    clearSuccess,
    validateProductName
  }
} 