import { ref } from 'vue'

export function useProductManagement() {
  // 주요 품목 리스트
  const majorProducts = ref([])
  const productsLoading = ref(false)

  // 쿠키 유틸리티 함수들
  const getCookie = (name) => {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop().split(';').shift()
    return null
  }

  const setCookie = (name, value, days = 30) => {
    const expires = new Date()
    expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000))
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`
  }

  // 상품 순서 저장/불러오기
  const saveProductOrder = () => {
    setCookie('auction_product_order', JSON.stringify(majorProducts.value))
    console.log('상품 순서 저장됨:', majorProducts.value)
  }

  const loadProductOrder = () => {
    const savedOrder = getCookie('auction_product_order')
    if (savedOrder) {
      try {
        const parsedOrder = JSON.parse(savedOrder)
        console.log('저장된 상품 순서 불러옴:', parsedOrder)
        return parsedOrder
      } catch (error) {
        console.error('저장된 상품 순서 파싱 실패:', error)
      }
    }
    return null
  }

  // 상품 목록 설정
  const fetchProductList = async () => {
    productsLoading.value = true
    try {
      console.log('상품 목록 설정 시작...')
      
      // 통합 API 가이드에 따른 상품 목록 엔드포인트 사용
      const response = await fetch('http://localhost:8080/api/products')
      if (response.ok) {
        const data = await response.json()
        console.log('상품 API 응답:', data)
        
        // ApiResponse<T> 형식 처리
        if (data.success && Array.isArray(data.data)) {
          const products = data.data
          // 활성화된 상품만 필터링
          const activeProducts = products.filter(product => product.isActive)
          const apiProducts = activeProducts.map(product => product.productName)
          console.log('API에서 활성 상품 목록 가져옴:', apiProducts)
          
          // 저장된 순서 불러오기
          const savedOrder = loadProductOrder()
          if (savedOrder && savedOrder.length > 0) {
            // 저장된 순서가 있으면 사용, 없는 상품은 API 목록에서 추가
            const savedSet = new Set(savedOrder)
            const missingProducts = apiProducts.filter(product => !savedSet.has(product))
            majorProducts.value = [...savedOrder, ...missingProducts]
            console.log('저장된 순서로 상품 목록 설정:', majorProducts.value.length, '개')
          } else {
            // 저장된 순서가 없으면 API 순서 사용
            majorProducts.value = [...apiProducts]
            console.log('API 순서로 상품 목록 설정:', majorProducts.value.length, '개')
          }
        } else {
          console.warn('상품 API 응답 형식 오류:', data)
          throw new Error(data.message || 'API 응답 형식 오류')
        }
      } else {
        console.error('상품 API 호출 실패:', response.status)
        throw new Error('API 호출 실패')
      }
      
    } catch (error) {
      console.error('상품 목록 설정 오류:', error)
      // 에러 발생 시 명확한 에러 메시지와 함께 에러를 다시 던짐
      throw new Error(`상품 API 호출 실패: ${error.message}`)
    } finally {
      productsLoading.value = false
    }
  }

  // 상품 선택
  const selectProduct = (productName, callback) => {
    if (callback) {
      callback(productName)
    }
  }

  // 선택 초기화
  const clearSelection = (callback) => {
    if (callback) {
      callback()
    }
  }

  return {
    // 상태
    majorProducts,
    productsLoading,
    
    // 메서드
    fetchProductList,
    selectProduct,
    clearSelection,
    saveProductOrder,
    loadProductOrder
  }
} 