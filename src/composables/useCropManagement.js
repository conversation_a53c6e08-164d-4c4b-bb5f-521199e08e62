import { ref, computed } from 'vue';
import apiClient from '@/utils/axios';

export function useCropManagement() {
  // 상태
  const crops = ref([]);
  const statistics = ref({
    totalCrops: 0,
    upcomingHarvests: 0,
    categories: {}
  });
  const availableGreenhouses = ref([]);
  const productCodes = ref([]);
  
  const loading = ref(false);
  const saving = ref(false);
  const error = ref('');

  // 계산된 속성
  const activeCrops = computed(() => crops.value.filter(c => c.isActive));
  
  // API 호출 함수
  const loadCrops = async () => {
    loading.value = true;
    error.value = '';
    try {
      const response = await apiClient.get('/crops');
      if (response.data.success) {
        crops.value = response.data.data || [];
        calculateStatistics();
      } else {
        throw new Error(response.data.message || 'Failed to load crops');
      }
    } catch (err) {
      error.value = err.message;
      console.error('Error loading crops:', err);
    } finally {
      loading.value = false;
    }
  };
  
  const loadGreenhouses = async () => {
    try {
        const response = await apiClient.get('/greenhouses');
        if (response.data.success) {
            availableGreenhouses.value = response.data.data.filter(g => g.isActive !== false);
        } else {
            availableGreenhouses.value = [];
        }
    } catch (err) {
        console.error('Failed to load greenhouses:', err);
    }
  };

  const loadProductCodes = async () => {
    try {
        const response = await apiClient.get('/crops/product-codes');
        if (response.data.success) {
            productCodes.value = response.data.data || [];
        } else {
            productCodes.value = [];
        }
    } catch (err) {
        console.error('Failed to load product codes:', err);
    }
  };

  const saveCrop = async (cropData, cropId = null) => {
    saving.value = true;
    error.value = '';
    try {
      let response;
      if (cropId) {
        response = await apiClient.put(`/crops/${cropId}`, cropData);
      } else {
        response = await apiClient.post('/crops', cropData);
      }
      
      if (response.data.success) {
        await loadCrops();
        return response.data.data;
      } else {
        throw new Error(response.data.message || 'Failed to save crop');
      }
    } catch (err) {
      error.value = err.message;
      console.error('Error saving crop:', err);
      return null;
    } finally {
      saving.value = false;
    }
  };

  const toggleCropStatus = async (cropId) => {
    // API 문서에 따라 DELETE가 비활성화/아카이브 역할을 한다고 가정
    try {
      const response = await apiClient.delete(`/crops/${cropId}`);
      if (response.data.success) {
        await loadCrops();
      } else {
        throw new Error(response.data.message || 'Failed to toggle crop status');
      }
    } catch (err) {
      error.value = err.message;
      console.error('Error toggling crop status:', err);
    }
  };
  
  const assignGreenhousesToCrop = async (cropId, greenhouseIds) => {
      saving.value = true;
      try {
          const response = await apiClient.post(`/crops/${cropId}/assign-greenhouses`, greenhouseIds);
          if (response.data.success) {
              await loadCrops();
              return true;
          }
          return false;
      } catch (err) {
          console.error('Error assigning greenhouses:', err);
          error.value = err.message;
          return false;
      } finally {
          saving.value = false;
      }
  }
  
  // 유틸리티
  const calculateStatistics = () => {
    const active = activeCrops.value;
    const total = active.length;
    const upcoming = active.filter(crop => {
      if (!crop.expectedHarvestDate) return false;
      const diffDays = (new Date(crop.expectedHarvestDate) - new Date()) / (1000 * 60 * 60 * 24);
      return diffDays >= 0 && diffDays <= 7;
    }).length;
    
    const categories = active.reduce((acc, crop) => {
      const category = crop.category || '미분류';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {});

    statistics.value = { totalCrops: total, upcomingHarvests: upcoming, categories };
  };

  return {
    crops,
    statistics,
    availableGreenhouses,
    productCodes,
    loading,
    saving,
    error,
    activeCrops,
    loadCrops,
    loadGreenhouses,
    loadProductCodes,
    saveCrop,
    toggleCropStatus,
    assignGreenhousesToCrop
  };
} 