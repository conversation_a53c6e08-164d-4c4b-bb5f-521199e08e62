import { ref, computed } from 'vue'
import apiClient from '../utils/axios'
import { usePerformanceMonitor } from './usePerformanceMonitor'

export function useOptimizedAuction() {
  // 성능 모니터링
  const performanceMonitor = usePerformanceMonitor()
  
  // 상태 관리
  const loading = ref(false)
  const error = ref('')
  const success = ref('')
  
  // 데이터 상태
  const auctionData = ref([])
  const totalCount = ref(0)
  const currentPage = ref(1)
  const pageSize = ref(100)
  const hasMore = ref(true)
  
    // 성능 측정 함수 (성능 모니터링 통합)
  const measurePerformance = async (apiCall, endpoint = '', method = '') => {
    return await performanceMonitor.measurePerformance(apiCall, endpoint, method)
  }
  
    // 1. 최적화된 기본 데이터 조회 (GET 방식)
  const fetchAuctionDataOptimized = async (startIndex = 1, endIndex = 100) => {
    loading.value = true
    error.value = ''

    try {
      const result = await measurePerformance(async () => {
        const response = await apiClient.get(`/auction/garak?startIndex=${startIndex}&endIndex=${endIndex}`)
        return response.data
      }, '/auction/garak', 'GET')
      
      if (result.success) {
        auctionData.value = result.data.data || []
        totalCount.value = result.data.totalCount || 0
        currentPage.value = Math.ceil(startIndex / pageSize.value) + 1
        hasMore.value = endIndex < totalCount.value
        
        success.value = `데이터 조회 완료 (${result.data.dataCount}건)`
        return result.data
      } else {
        throw new Error(result.message || '데이터 조회 실패')
      }
    } catch (err) {
      handleError(err)
      return null
    } finally {
      loading.value = false
    }
  }
  
    // 2. 특정 품목 조회 (GET 방식)
  const fetchProductDataOptimized = async (pumName, startIndex = 1, endIndex = 100) => {
    loading.value = true
    error.value = ''

    try {
      const result = await measurePerformance(async () => {
        const response = await apiClient.get(`/auction/garak?pumName=${encodeURIComponent(pumName)}&startIndex=${startIndex}&endIndex=${endIndex}`)
        return response.data
      }, `/auction/garak?pumName=${pumName}`, 'GET')
      
      if (result.success) {
        auctionData.value = result.data.data || []
        totalCount.value = result.data.totalCount || 0
        currentPage.value = Math.ceil(startIndex / pageSize.value) + 1
        hasMore.value = endIndex < totalCount.value
        
        success.value = `${pumName} 데이터 조회 완료 (${result.data.dataCount}건)`
        return result.data
      } else {
        throw new Error(result.message || '데이터 조회 실패')
      }
    } catch (err) {
      handleError(err)
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 3. 상태 확인 (헬스체크)
  const checkServerStatus = async () => {
    try {
      const response = await apiClient.get('/auction/garak/status')
      return response.data
    } catch (err) {
      console.error('서버 상태 확인 실패:', err)
      return null
    }
  }
  
  // 4. 더 보기 (페이징)
  const loadMore = async () => {
    if (!hasMore.value || loading.value) return
    
    const nextStartIndex = (currentPage.value - 1) * pageSize.value + 1
    const nextEndIndex = Math.min(nextStartIndex + pageSize.value - 1, totalCount.value)
    
    try {
      const result = await measurePerformance(async () => {
        const response = await apiClient.get(`/auction/garak?startIndex=${nextStartIndex}&endIndex=${nextEndIndex}`)
        return response.data
      })
      
      if (result.success && result.data.data) {
        auctionData.value.push(...result.data.data)
        currentPage.value++
        hasMore.value = nextEndIndex < totalCount.value
        
        console.log(`📥 더 보기 완료: ${result.data.data.length}건 추가`)
      }
    } catch (err) {
      handleError(err)
    }
  }
  
  // 5. 검색 (기존 POST 방식도 지원)
  const searchAuction = async (params) => {
    loading.value = true
    error.value = ''
    
    try {
      let result
      
      if (params.pumName) {
        // GET 방식으로 특정 품목 검색
        result = await fetchProductDataOptimized(params.pumName, params.startIndex || 1, params.endIndex || 100)
      } else {
        // GET 방식으로 전체 검색
        result = await fetchAuctionDataOptimized(params.startIndex || 1, params.endIndex || 100)
      }
      
      return result
    } catch (err) {
      handleError(err)
      return null
    } finally {
      loading.value = false
    }
  }
  
  // 6. 에러 처리 통합
  const handleError = (err) => {
    if (err.code === 'ECONNABORTED' || err.message.includes('timeout')) {
      error.value = '요청 시간이 초과되었습니다. 서버 상태를 확인해주세요.'
    } else if (err.type === 'NETWORK_ERROR') {
      error.value = '네트워크 연결을 확인해주세요. 서버가 실행 중인지 확인해주세요.'
    } else if (err.response?.status === 404) {
      error.value = '요청한 데이터를 찾을 수 없습니다.'
    } else if (err.response?.status >= 500) {
      error.value = '서버 오류가 발생했습니다. 잠시 후 다시 시도해주세요.'
    } else {
      error.value = err.response?.data?.message || err.message || '알 수 없는 오류가 발생했습니다.'
    }
    
    console.error('🚨 API 오류 상세:', {
      message: err.message,
      status: err.response?.status,
      data: err.response?.data,
      type: err.type,
      code: err.code
    })
  }
  
  // 7. 상태 초기화
  const resetState = () => {
    auctionData.value = []
    totalCount.value = 0
    currentPage.value = 1
    hasMore.value = true
    error.value = ''
    success.value = ''
  }
  
    // 8. 성능 통계 (성능 모니터링 통합)
  const getPerformanceStats = computed(() => {
    const metrics = performanceMonitor.performanceMetrics.value
    return {
      ...metrics,
      averageResponseTime: performanceMonitor.averageResponseTime.value.toFixed(2) + 'ms',
      dataSizeKB: metrics.dataSize > 0 ? (metrics.dataSize / 1024).toFixed(2) + 'KB' : 'N/A',
      successRate: performanceMonitor.successRate.value.toFixed(1) + '%',
      totalRequests: performanceMonitor.performanceHistory.value.length
    }
  })
  
    return {
    // 상태
    loading,
    error,
    success,
    auctionData,
    totalCount,
    currentPage,
    pageSize,
    hasMore,

    // 계산된 속성
    getPerformanceStats,

    // 메서드
    fetchAuctionDataOptimized,
    fetchProductDataOptimized,
    checkServerStatus,
    loadMore,
    searchAuction,
    resetState,
    handleError,
    
    // 성능 모니터링
    performanceMonitor
  }
} 