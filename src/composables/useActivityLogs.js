import { ref } from 'vue'
import axios from 'axios'

export function useActivityLogs() {
  // 상태 관리
  const activityLogs = ref([])
  const errorLogs = ref([])
  const loading = ref(false)
  const error = ref('')
  const totalElements = ref(0)
  const totalPages = ref(0)
  const currentPage = ref(0)
  const pageSize = 20

  // API 설정
  const API_BASE_URL = 'http://localhost:8080/api'

  // 전체 활동 로그 가져오기
  const fetchActivityLogs = async (page = 0, size = 20) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await axios.get(`${API_BASE_URL}/activity-logs`, {
        params: { page, size }
      })
      
      if (response.data.success) {
        const data = response.data.data
        activityLogs.value = data.content || []
        totalElements.value = data.totalElements || 0
        totalPages.value = data.totalPages || 0
        currentPage.value = data.number || 0
      } else {
        error.value = response.data.message || '활동 로그를 불러오는데 실패했습니다.'
      }
    } catch (err) {
      console.error('활동 로그 로드 실패:', err)
      error.value = '서버 오류가 발생했습니다.'
    } finally {
      loading.value = false
    }
  }

  // 오류 로그만 가져오기
  const fetchErrorLogs = async (page = 0, size = 20) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await axios.get(`${API_BASE_URL}/activity-logs/errors`, {
        params: { page, size }
      })
      
      if (response.data.success) {
        const data = response.data.data
        errorLogs.value = data.content || []
        totalElements.value = data.totalElements || 0
        totalPages.value = data.totalPages || 0
        currentPage.value = data.number || 0
      } else {
        error.value = response.data.message || '오류 로그를 불러오는데 실패했습니다.'
      }
    } catch (err) {
      console.error('오류 로그 로드 실패:', err)
      error.value = '서버 오류가 발생했습니다.'
    } finally {
      loading.value = false
    }
  }

  // 최근 활동 로그 가져오기
  const fetchRecentLogs = async () => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await axios.get(`${API_BASE_URL}/activity-logs/recent`)
      
      if (response.data.success) {
        activityLogs.value = response.data.data || []
      } else {
        error.value = response.data.message || '최근 활동 로그를 불러오는데 실패했습니다.'
      }
    } catch (err) {
      console.error('최근 활동 로그 로드 실패:', err)
      error.value = '서버 오류가 발생했습니다.'
    } finally {
      loading.value = false
    }
  }

  // 카테고리별 로그 가져오기
  const fetchLogsByCategory = async (category, page = 0, size = 20) => {
    loading.value = true
    error.value = ''
    
    try {
      const response = await axios.get(`${API_BASE_URL}/activity-logs/category/${category}`, {
        params: { page, size }
      })
      
      if (response.data.success) {
        const data = response.data.data
        activityLogs.value = data.content || []
        totalElements.value = data.totalElements || 0
        totalPages.value = data.totalPages || 0
        currentPage.value = data.number || 0
      } else {
        error.value = response.data.message || '카테고리별 로그를 불러오는데 실패했습니다.'
      }
    } catch (err) {
      console.error('카테고리별 로그 로드 실패:', err)
      error.value = '서버 오류가 발생했습니다.'
    } finally {
      loading.value = false
    }
  }

  // 상태 초기화
  const resetState = () => {
    activityLogs.value = []
    errorLogs.value = []
    loading.value = false
    error.value = ''
    totalElements.value = 0
    totalPages.value = 0
    currentPage.value = 0
  }

  return {
    // 상태
    activityLogs,
    errorLogs,
    loading,
    error,
    totalElements,
    totalPages,
    currentPage,
    pageSize,
    
    // 메서드
    fetchActivityLogs,
    fetchErrorLogs,
    fetchRecentLogs,
    fetchLogsByCategory,
    resetState
  }
} 