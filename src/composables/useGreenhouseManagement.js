import { ref, computed } from 'vue';
import apiClient from '@/utils/axios';

export function useGreenhouseManagement() {
  const greenhouses = ref([]);
  const statistics = ref({
    totalGreenhouses: 0,
    cultivatingGreenhouses: 0,
    statusBreakdown: {}
  });
  const availableCrops = ref([]);

  const loading = ref(false);
  const saving = ref(false);
  const error = ref('');

  const loadGreenhouses = async () => {
    loading.value = true;
    error.value = '';
    try {
      const response = await apiClient.get('/greenhouses');
      if (response.data.success) {
        greenhouses.value = response.data.data || [];
        calculateStatistics();
      } else {
        throw new Error(response.data.message || 'Failed to load greenhouses');
      }
    } catch (err) {
      error.value = err.message;
      console.error(err);
    } finally {
      loading.value = false;
    }
  };

  const loadAvailableCrops = async () => {
    try {
      // 이 API는 /crops/product-codes 이지만, 실제로는 작물 이름을 가져와야 하므로,
      // 백엔드에 작물 이름 목록을 제공하는 API가 있는지 확인이 필요합니다.
      // 우선 기존 로직을 유지합니다.
      const response = await apiClient.get('/crops/product-codes');
      if (response.data.success) {
        availableCrops.value = response.data.data || [];
      }
    } catch (err) {
      console.error('Failed to load available crops', err);
    }
  };

  const saveGreenhouse = async (greenhouseData, greenhouseId = null) => {
    saving.value = true;
    error.value = '';
    try {
      let response;
      if (greenhouseId) {
        response = await apiClient.put(`/greenhouses/${greenhouseId}`, greenhouseData);
      } else {
        response = await apiClient.post('/greenhouses', greenhouseData);
      }
      if (response.data.success) {
        await loadGreenhouses();
        return true;
      } else {
        throw new Error(response.data.message || 'Failed to save greenhouse');
      }
    } catch (err) {
      error.value = err.message;
      console.error(err);
      return false;
    } finally {
      saving.value = false;
    }
  };
  
  const updateGreenhouseStatus = async (greenhouseId, statusData) => {
    saving.value = true;
    try {
      // API 문서에 PUT /greenhouses/{id} 로 수정하는 것으로 되어있으므로, 해당 API를 호출하도록 유도.
      // 별도의 status 변경 API가 있다면 그쪽으로 변경해야 합니다.
      // 여기서는 전체 데이터를 업데이트 하는 것으로 가정합니다.
      const greenhouse = greenhouses.value.find(g => g.id === greenhouseId);
      if(!greenhouse) throw new Error("Greenhouse not found");
      
      const updatedData = { ...greenhouse, ...statusData };
      const response = await apiClient.put(`/greenhouses/${greenhouseId}`, updatedData);
      
      if (response.data.success) {
        await loadGreenhouses();
        return true;
      }
      return false;
    } catch(err) {
      error.value = err.message;
      console.error(err);
      return false;
    } finally {
      saving.value = false;
    }
  };

  const toggleGreenhouseStatus = async (greenhouseId) => {
    // API 문서에 따라 DELETE가 비활성화 역할을 한다고 가정
    try {
      const response = await apiClient.delete(`/greenhouses/${greenhouseId}`);
      if (response.data.success) {
        await loadGreenhouses();
        return true;
      }
      return false;
    } catch (err) {
      error.value = err.message;
      console.error(err);
      return false;
    }
  };

  const calculateStatistics = () => {
    const total = greenhouses.value.length;
    const cultivating = greenhouses.value.filter(g => g.status === 'CULTIVATING' && g.isActive).length;
    const breakdown = greenhouses.value.reduce((acc, g) => {
        if(g.isActive) {
            acc[g.status] = (acc[g.status] || 0) + 1;
        }
        return acc;
    }, {});
    
    statistics.value = {
      totalGreenhouses: total,
      cultivatingGreenhouses: cultivating,
      statusBreakdown: breakdown,
    };
  };

  return {
    greenhouses,
    statistics,
    availableCrops,
    loading,
    saving,
    error,
    loadGreenhouses,
    loadAvailableCrops,
    saveGreenhouse,
    updateGreenhouseStatus,
    toggleGreenhouseStatus
  };
} 