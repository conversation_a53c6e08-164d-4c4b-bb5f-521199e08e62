import { ref } from 'vue';
import apiClient from '@/utils/axios';

export function useEmployeeManagement() {
  const employees = ref([]);
  const statistics = ref(null);
  const loading = ref(false);
  const saving = ref(false);
  const error = ref('');

  const loadEmployees = async () => {
    loading.value = true;
    error.value = '';
    try {
      // API 가이드 문서에 따라 직원 목록을 가져옵니다.
      const response = await apiClient.get('/employees');
      if (response.data.success) {
        employees.value = response.data.data || [];
      } else {
        throw new Error(response.data.message || 'Failed to load employees');
      }
    } catch (err) {
      error.value = err.message;
      console.error(err);
    } finally {
      loading.value = false;
    }
  };

  const loadStatistics = async () => {
    try {
      // 별도의 통계 API가 있다면 호출합니다. (문서에는 없지만 기존 코드에 존재)
      const response = await apiClient.get('/employees/statistics');
      if (response.data.success) {
        statistics.value = response.data.data;
      }
    } catch (err) {
      console.error('Failed to load statistics:', err);
      // 통계 로드 실패는 전체 페이지 에러로 간주하지 않을 수 있습니다.
    }
  };

  const saveEmployee = async (employeeData, employeeId = null) => {
    saving.value = true;
    error.value = '';
    try {
      let response;
      if (employeeId) {
        response = await apiClient.put(`/employees/${employeeId}`, employeeData);
      } else {
        response = await apiClient.post('/employees', employeeData);
      }
      
      if (response.data.success) {
        await loadEmployees(); // 목록 새로고침
        return true;
      } else {
        throw new Error(response.data.message || 'Failed to save employee');
      }
    } catch (err) {
      error.value = err.message;
      console.error(err);
      return false;
    } finally {
      saving.value = false;
    }
  };
  
  const toggleEmployeeStatus = async (employeeId) => {
    // API 문서에 따라 DELETE가 비활성화 역할을 한다고 가정
    try {
      const response = await apiClient.delete(`/employees/${employeeId}`);
      if (response.data.success) {
        await loadEmployees();
        return true;
      }
      return false;
    } catch (err) {
      error.value = err.message;
      console.error(err);
      return false;
    }
  };

  const searchEmployees = async (keyword) => {
      loading.value = true;
      error.value = '';
      try {
          const response = await apiClient.get(`/employees/search?keyword=${keyword}`);
          if (response.data.success) {
              employees.value = response.data.data || [];
          }
      } catch (err) {
          error.value = "Search failed";
          console.error(err);
      } finally {
          loading.value = false;
      }
  }

  // 유틸리티 함수
  const formatDate = (dateString) => {
    if (!dateString) return '';
    return new Date(dateString).toLocaleDateString('ko-KR');
  };

  const formatCurrency = (amount) => {
    if (!amount) return '0원';
    return new Intl.NumberFormat('ko-KR').format(amount) + '원';
  };

  const getVisaExpiryClass = (dateString) => {
    if (!dateString) return '';
    const diffDays = (new Date(dateString) - new Date()) / (1000 * 60 * 60 * 24);
    if (diffDays < 0) return 'expired';
    if (diffDays <= 30) return 'urgent';
    if (diffDays <= 90) return 'warning';
    return '';
  };


  return {
    employees,
    statistics,
    loading,
    saving,
    error,
    loadEmployees,
    loadStatistics,
    saveEmployee,
    toggleEmployeeStatus,
    searchEmployees,
    formatDate,
    formatCurrency,
    getVisaExpiryClass,
  };
} 