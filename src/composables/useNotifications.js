import { ref, onMounted } from 'vue'

export function useNotifications() {
  const isSupported = ref(false)
  const permission = ref('default')
  const subscription = ref(null)
  const registration = ref(null)

  // 브라우저 지원 여부 확인
  const checkSupport = () => {
    isSupported.value = 'serviceWorker' in navigator && 'PushManager' in window
    return isSupported.value
  }

  // 알림 권한 확인
  const checkPermission = () => {
    if (!isSupported.value) return false
    
    permission.value = Notification.permission
    return permission.value
  }

  // Service Worker 등록
  const registerServiceWorker = async () => {
    if (!isSupported.value) {
      throw new Error('브라우저가 푸시 알림을 지원하지 않습니다.')
    }

    try {
      registration.value = await navigator.serviceWorker.register('/sw.js')
      console.log('Service Worker 등록 성공:', registration.value)
      return registration.value
    } catch (error) {
      console.error('Service Worker 등록 실패:', error)
      throw error
    }
  }

  // 알림 권한 요청
  const requestPermission = async () => {
    if (!isSupported.value) {
      throw new Error('브라우저가 푸시 알림을 지원하지 않습니다.')
    }

    try {
      const result = await Notification.requestPermission()
      permission.value = result
      return result
    } catch (error) {
      console.error('알림 권한 요청 실패:', error)
      throw error
    }
  }

  // VAPID 키 변환
  const urlBase64ToUint8Array = (base64String) => {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  // 푸시 구독 생성
  const subscribeToPush = async (vapidPublicKey) => {
    if (!registration.value) {
      throw new Error('Service Worker가 등록되지 않았습니다.')
    }

    if (permission.value !== 'granted') {
      throw new Error('알림 권한이 허용되지 않았습니다.')
    }

    try {
      const pushSubscription = await registration.value.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(vapidPublicKey)
      })

      subscription.value = pushSubscription
      console.log('푸시 구독 성공:', pushSubscription)
      return pushSubscription
    } catch (error) {
      console.error('푸시 구독 실패:', error)
      throw error
    }
  }

  // 구독 해제
  const unsubscribeFromPush = async () => {
    if (!subscription.value) return

    try {
      await subscription.value.unsubscribe()
      subscription.value = null
      console.log('푸시 구독 해제 성공')
    } catch (error) {
      console.error('푸시 구독 해제 실패:', error)
      throw error
    }
  }

  // 로컬 알림 표시 (테스트용)
  const showLocalNotification = (title, options = {}) => {
    if (permission.value !== 'granted') {
      throw new Error('알림 권한이 허용되지 않았습니다.')
    }

    const defaultOptions = {
      icon: '/favicon.ico',
      badge: '/favicon.ico',
      tag: 'farm-notification',
      requireInteraction: true
    }

    const notification = new Notification(title, {
      ...defaultOptions,
      ...options
    })

    return notification
  }

  // 수확 완료 알림
  const showHarvestReminder = (cropCount) => {
    // Service Worker를 통한 알림 시도
    if (registration.value) {
      return registration.value.showNotification('🌾 수확 완료 알림', {
        body: `${cropCount}개의 작물이 수확 완료를 기다리고 있습니다.`,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'harvest-reminder',
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: '확인하기',
            icon: '/favicon.ico'
          },
          {
            action: 'dismiss',
            title: '닫기',
            icon: '/favicon.ico'
          }
        ],
        data: {
          type: 'harvest-reminder',
          cropCount: cropCount
        }
      })
    } else {
      // 로컬 알림으로 폴백
      return showLocalNotification('🌾 수확 완료 알림', {
        body: `${cropCount}개의 작물이 수확 완료를 기다리고 있습니다.`,
        tag: 'harvest-reminder',
        data: {
          type: 'harvest-reminder',
          cropCount: cropCount
        }
      })
    }
  }

  // 자동 완료 알림
  const showAutoCompleteNotification = (cropName) => {
    // Service Worker를 통한 알림 시도
    if (registration.value) {
      return registration.value.showNotification('🤖 자동 완료 처리', {
        body: `${cropName}이(가) 자동으로 수확 완료 처리되었습니다.`,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'auto-complete',
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: '확인하기',
            icon: '/favicon.ico'
          },
          {
            action: 'dismiss',
            title: '닫기',
            icon: '/favicon.ico'
          }
        ],
        data: {
          type: 'auto-complete',
          cropName: cropName
        }
      })
    } else {
      // 로컬 알림으로 폴백
      return showLocalNotification('🤖 자동 완료 처리', {
        body: `${cropName}이(가) 자동으로 수확 완료 처리되었습니다.`,
        tag: 'auto-complete',
        data: {
          type: 'auto-complete',
          cropName: cropName
        }
      })
    }
  }

  // 수확 실패 알림
  const showHarvestFailureNotification = (cropName, reason) => {
    // Service Worker를 통한 알림 시도
    if (registration.value) {
      return registration.value.showNotification('❌ 수확 실패 알림', {
        body: `${cropName} 수확이 실패했습니다. (사유: ${reason})`,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'harvest-failure',
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: '확인하기',
            icon: '/favicon.ico'
          },
          {
            action: 'dismiss',
            title: '닫기',
            icon: '/favicon.ico'
          }
        ],
        data: {
          type: 'harvest-failure',
          cropName: cropName,
          reason: reason
        }
      })
    } else {
      // 로컬 알림으로 폴백
      return showLocalNotification('❌ 수확 실패 알림', {
        body: `${cropName} 수확이 실패했습니다. (사유: ${reason})`,
        tag: 'harvest-failure',
        data: {
          type: 'harvest-failure',
          cropName: cropName,
          reason: reason
        }
      })
    }
  }

  // 주간 리포트 알림
  const showWeeklyReportNotification = (successRate, totalYield) => {
    // Service Worker를 통한 알림 시도
    if (registration.value) {
      return registration.value.showNotification('📊 주간 수확 리포트', {
        body: `수확 성공률: ${successRate}%, 총 수확량: ${totalYield}kg`,
        icon: '/favicon.ico',
        badge: '/favicon.ico',
        tag: 'weekly-report',
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: '확인하기',
            icon: '/favicon.ico'
          },
          {
            action: 'dismiss',
            title: '닫기',
            icon: '/favicon.ico'
          }
        ],
        data: {
          type: 'weekly-report',
          successRate: successRate,
          totalYield: totalYield
        }
      })
    } else {
      // 로컬 알림으로 폴백
      return showLocalNotification('📊 주간 수확 리포트', {
        body: `수확 성공률: ${successRate}%, 총 수확량: ${totalYield}kg`,
        tag: 'weekly-report',
        data: {
          type: 'weekly-report',
          successRate: successRate,
          totalYield: totalYield
        }
      })
    }
  }

  // 알림 초기화
  const initializeNotifications = async (vapidPublicKey = null) => {
    try {
      // 지원 여부 확인
      if (!checkSupport()) {
        console.warn('브라우저가 푸시 알림을 지원하지 않습니다.')
        return false
      }

      // Service Worker 등록
      await registerServiceWorker()

      // 권한 확인
      checkPermission()

      // 권한이 없으면 요청
      if (permission.value === 'default') {
        await requestPermission()
      }

      // VAPID 키가 있으면 구독
      if (vapidPublicKey && permission.value === 'granted') {
        await subscribeToPush(vapidPublicKey)
      }

      return true
    } catch (error) {
      console.error('알림 초기화 실패:', error)
      return false
    }
  }

  // 구독 정보 가져오기
  const getSubscription = async () => {
    if (!registration.value) return null

    try {
      const existingSubscription = await registration.value.pushManager.getSubscription()
      subscription.value = existingSubscription
      return existingSubscription
    } catch (error) {
      console.error('구독 정보 가져오기 실패:', error)
      return null
    }
  }

  // 컴포넌트 마운트 시 초기화
  onMounted(() => {
    initializeNotifications()
  })

  return {
    // 상태
    isSupported,
    permission,
    subscription,
    registration,

    // 메서드
    checkSupport,
    checkPermission,
    registerServiceWorker,
    requestPermission,
    subscribeToPush,
    unsubscribeFromPush,
    showLocalNotification,
    initializeNotifications,
    getSubscription,

    // 알림 타입별 메서드
    showHarvestReminder,
    showAutoCompleteNotification,
    showHarvestFailureNotification,
    showWeeklyReportNotification
  }
} 