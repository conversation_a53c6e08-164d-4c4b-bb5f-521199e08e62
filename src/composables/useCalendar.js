import { ref, computed } from 'vue'
import apiClient from '@/utils/axios'
import { useApiError } from '@/composables/useApiError'

export function useCalendar() {
  // API 에러 처리
  const { 
    error, 
    isLoading, 
    hasError, 
    canRetry, 
    errorStats,
    handleApiError, 
    retry, 
    clearError
  } = useApiError()

  // 상태 관리
  const currentView = ref('month')
  const currentDate = ref(new Date())
  const events = ref([])
  const availableCrops = ref([])
  const availableGreenhouses = ref([])

  // 필터 상태
  const selectedEventType = ref('')
  const selectedCrop = ref('')
  const selectedGreenhouse = ref('')

  // 모달 상태
  const showAddEventModal = ref(false)
  const showEventModal = ref(false)
  const selectedEvent = ref(null)
  const selectedDate = ref(null)

  // 주간 요일
  const weekDays = ['일', '월', '화', '수', '목', '금', '토']

  // 계산된 속성들
  const currentPeriodText = computed(() => {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth() + 1
    
    if (currentView.value === 'month') {
      return `${year}년 ${month}월`
    } else if (currentView.value === 'week') {
      const startOfWeek = getStartOfWeek(currentDate.value)
      const endOfWeek = getEndOfWeek(currentDate.value)
      return `${formatDate(startOfWeek)} ~ ${formatDate(endOfWeek)}`
    } else {
      return formatDate(currentDate.value)
    }
  })

  const calendarDates = computed(() => {
    if (currentView.value === 'month') {
      return generateMonthDates(currentDate.value)
    } else if (currentView.value === 'week') {
      return generateWeekDates(currentDate.value)
    } else {
      return generateDayDates(currentDate.value)
    }
  })

  const weekDates = computed(() => {
    return generateWeekDates(currentDate.value)
  })

  // 유틸리티 함수들
  const formatDate = (date) => {
    return date.toLocaleDateString('ko-KR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  }

  const getStartOfWeek = (date) => {
    const d = new Date(date)
    const day = d.getDay()
    const diff = d.getDate() - day
    return new Date(d.setDate(diff))
  }

  const getEndOfWeek = (date) => {
    const d = new Date(date)
    const day = d.getDay()
    const diff = d.getDate() - day + 6
    return new Date(d.setDate(diff))
  }

  const generateMonthDates = (date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay())
    
    const dates = []
    const today = new Date()
    
    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate)
      currentDate.setDate(startDate.getDate() + i)
      
      const isCurrentMonth = currentDate.getMonth() === month
      const isToday = currentDate.toDateString() === today.toDateString()
      const dayEvents = getEventsForDate(currentDate)
      
      dates.push({
        key: currentDate.toISOString(),
        date: currentDate,
        dayNumber: currentDate.getDate(),
        isCurrentMonth,
        isToday,
        events: dayEvents
      })
    }
    
    return dates
  }

  const generateWeekDates = (date) => {
    const startOfWeek = getStartOfWeek(date)
    const dates = []
    const today = new Date()
    
    for (let i = 0; i < 7; i++) {
      const currentDate = new Date(startOfWeek)
      currentDate.setDate(startOfWeek.getDate() + i)
      
      const isToday = currentDate.toDateString() === today.toDateString()
      const dayEvents = getEventsForDate(currentDate)
      
      dates.push({
        key: currentDate.toISOString(),
        date: currentDate,
        dayNumber: currentDate.getDate(),
        isToday,
        events: dayEvents
      })
    }
    
    return dates
  }

  const generateDayDates = (date) => {
    const today = new Date()
    const isToday = date.toDateString() === today.toDateString()
    const dayEvents = getEventsForDate(date)
    
    return [{
      key: date.toISOString(),
      date: date,
      dayNumber: date.getDate(),
      isToday,
      events: dayEvents
    }]
  }

  const getEventsForDate = (date) => {
    return events.value.filter(event => {
      const eventDate = new Date(event.date)
      return eventDate.toDateString() === date.toDateString()
    })
  }

  const getEventsForHour = (hour) => {
    return events.value.filter(event => {
      const eventDate = new Date(event.date)
      return eventDate.getHours() === hour
    })
  }

  const getEventPosition = (event) => {
    const eventDate = new Date(event.date)
    const minutes = eventDate.getMinutes()
    return `${minutes}px`
  }

  const formatEventTime = (event) => {
    const eventDate = new Date(event.date)
    return eventDate.toLocaleTimeString('ko-KR', {
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const isToday = (day) => {
    const today = new Date()
    return day.toDateString() === today.toDateString()
  }

  // 네비게이션 함수들
  const previousPeriod = () => {
    const newDate = new Date(currentDate.value)
    
    if (currentView.value === 'month') {
      newDate.setMonth(newDate.getMonth() - 1)
    } else if (currentView.value === 'week') {
      newDate.setDate(newDate.getDate() - 7)
    } else {
      newDate.setDate(newDate.getDate() - 1)
    }
    
    currentDate.value = newDate
  }

  const nextPeriod = () => {
    const newDate = new Date(currentDate.value)
    
    if (currentView.value === 'month') {
      newDate.setMonth(newDate.getMonth() + 1)
    } else if (currentView.value === 'week') {
      newDate.setDate(newDate.getDate() + 7)
    } else {
      newDate.setDate(newDate.getDate() + 1)
    }
    
    currentDate.value = newDate
  }

  const goToToday = () => {
    currentDate.value = new Date()
  }

  // 이벤트 관련 함수들
  const selectDate = (date) => {
    selectedDate.value = date
    showAddEventModal.value = true
  }

  const selectEvent = (event) => {
    selectedEvent.value = event
    showEventModal.value = true
  }

  const closeEventModal = () => {
    showEventModal.value = false
    selectedEvent.value = null
  }

  // API 호출 함수들
  const loadEvents = async () => {
    try {
      const response = await apiClient.get('/calendar/events')
      // ApiResponse<T> 형식 처리
      if (response.data.success) {
        events.value = response.data.data || []
        console.log('일정 목록 로드 완료:', response.data.data)
      } else {
        throw new Error(response.data.message || '일정 목록 로드 실패')
      }
    } catch (err) {
      handleApiError(err, '일정 목록 로드')
      events.value = []
    }
  }

  const loadCrops = async () => {
    try {
      const response = await apiClient.get('/crops')
      // ApiResponse<T> 형식 처리
      if (response.data.success) {
        availableCrops.value = response.data.data || []
      } else {
        throw new Error(response.data.message || '작물 목록 로드 실패')
      }
    } catch (err) {
      console.error('작물 목록 로드 실패:', err)
      availableCrops.value = []
    }
  }

  const loadGreenhouses = async () => {
    try {
      const response = await apiClient.get('/greenhouses')
      // ApiResponse<T> 형식 처리
      if (response.data.success) {
        availableGreenhouses.value = (response.data.data || []).filter(g => g.isActive)
      } else {
        throw new Error(response.data.message || '비닐하우스 목록 로드 실패')
      }
    } catch (err) {
      console.error('비닐하우스 목록 로드 실패:', err)
      availableGreenhouses.value = []
    }
  }

  return {
    // 상태
    currentView,
    currentDate,
    events,
    availableCrops,
    availableGreenhouses,
    selectedEventType,
    selectedCrop,
    selectedGreenhouse,
    showAddEventModal,
    showEventModal,
    selectedEvent,
    selectedDate,
    weekDays,
    
    // 계산된 속성
    currentPeriodText,
    calendarDates,
    weekDates,
    
    // 에러 처리
    error,
    isLoading,
    hasError,
    canRetry,
    errorStats,
    clearError,
    
    // 메서드
    previousPeriod,
    nextPeriod,
    goToToday,
    selectDate,
    selectEvent,
    closeEventModal,
    loadEvents,
    loadCrops,
    loadGreenhouses,
    getEventsForHour,
    getEventPosition,
    formatEventTime,
    isToday,
    retry
  }
} 