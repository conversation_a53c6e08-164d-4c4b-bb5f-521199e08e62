import { ref } from 'vue'
import axios from '../utils/axios'

export function useHarvestManagement() {
  const loading = ref(false)
  const error = ref('')

  // 기본 수확 관리 API
  const updateHarvestStatus = async (cropId, statusData) => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.patch(`/crops/${cropId}/harvest-status`, statusData)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const completeHarvest = async (cropId, actualYield) => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.post(`/crops/${cropId}/complete`, {
        actualYield: parseFloat(actualYield)
      })
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const failHarvest = async (cropId, failureData) => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.post(`/crops/${cropId}/harvest-failed`, failureData)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const updateProgress = async (cropId, progress) => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.patch(`/crops/${cropId}/progress`, {
        progress: parseInt(progress)
      })
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // 자동 수확 관리 API
  const autoCompleteHarvest = async (cropId) => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.post(`/crops/${cropId}/auto-complete`)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const archiveCrop = async (cropId) => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.post(`/crops/${cropId}/archive`)
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const getIncompleteStatistics = async () => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.get('/crops/incomplete-statistics')
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const getWeeklyReport = async () => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.get('/crops/weekly-report')
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const getReminderTargets = async () => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.get('/crops/reminder-targets')
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  const getFailureReasons = async () => {
    try {
      loading.value = true
      error.value = ''
      
      const response = await axios.get('/crops/failure-reasons')
      return response.data
    } catch (err) {
      error.value = err.response?.data?.message || err.message
      throw err
    } finally {
      loading.value = false
    }
  }

  // 수확 상태 텍스트 변환
  const getStatusText = (status) => {
    const statusMap = {
      'NOT_STARTED': '수확 시작 전',
      'IN_PROGRESS': '수확 진행중',
      'COMPLETED': '수확 완료',
      'FAILED': '수확 실패'
    }
    return statusMap[status] || '알 수 없음'
  }

  // 수확 실패 사유 텍스트 변환
  const getFailureReasonText = (reason) => {
    const reasonMap = {
      'DISEASE': '질병',
      'PEST': '해충',
      'WEATHER': '기상 악화',
      'NUTRIENT_DEFICIENCY': '영양 부족',
      'WATER_ISSUE': '수분 문제',
      'EQUIPMENT_FAILURE': '장비 고장',
      'HUMAN_ERROR': '인적 오류',
      'OTHER': '기타'
    }
    return reasonMap[reason] || reason
  }

  // 수확 상태 클래스 반환
  const getStatusClass = (status) => {
    const statusMap = {
      'NOT_STARTED': 'status-not-started',
      'IN_PROGRESS': 'status-in-progress',
      'COMPLETED': 'status-completed',
      'FAILED': 'status-failed'
    }
    return statusMap[status] || 'status-unknown'
  }

  // 날짜 포맷팅
  const formatDate = (dateString) => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    return date.toLocaleDateString('ko-KR')
  }

  // 진행률 계산
  const calculateProgress = (crop) => {
    if (crop.harvestStatus === 'COMPLETED') return 100
    if (crop.harvestStatus === 'FAILED') return 0
    return crop.harvestProgress || 0
  }

  // 수확 완료 예정일 체크
  const isOverdue = (expectedDate) => {
    if (!expectedDate) return false
    const expected = new Date(expectedDate)
    const now = new Date()
    const diffDays = Math.ceil((now - expected) / (1000 * 60 * 60 * 24))
    return diffDays > 0
  }

  // 긴급 알림 대상 체크 (예상 수확일 + 7일)
  const isUrgentReminder = (expectedDate) => {
    if (!expectedDate) return false
    const expected = new Date(expectedDate)
    const now = new Date()
    const diffDays = Math.ceil((now - expected) / (1000 * 60 * 60 * 24))
    return diffDays >= 7
  }

  // 자동 완료 대상 체크 (예상 수확일 + 30일)
  const isAutoCompleteTarget = (expectedDate) => {
    if (!expectedDate) return false
    const expected = new Date(expectedDate)
    const now = new Date()
    const diffDays = Math.ceil((now - expected) / (1000 * 60 * 60 * 24))
    return diffDays >= 30
  }

  return {
    // 상태
    loading,
    error,
    
    // 기본 수확 관리 API
    updateHarvestStatus,
    completeHarvest,
    failHarvest,
    updateProgress,
    
    // 자동 수확 관리 API
    autoCompleteHarvest,
    archiveCrop,
    getIncompleteStatistics,
    getWeeklyReport,
    getReminderTargets,
    getFailureReasons,
    
    // 유틸리티 함수
    getStatusText,
    getFailureReasonText,
    getStatusClass,
    formatDate,
    calculateProgress,
    isOverdue,
    isUrgentReminder,
    isAutoCompleteTarget
  }
} 