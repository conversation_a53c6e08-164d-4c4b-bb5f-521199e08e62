import { ref, computed } from 'vue';
import apiClient from '@/utils/axios';

export function useHarvestManagement() {
  const harvests = ref([]);
  const statistics = ref({
    totalQuantity: 0,
    totalRevenue: 0,
  });
  const loading = ref(false);
  const error = ref('');
  const saving = ref(false);

  const availableCrops = ref([]);
  const availableGreenhouses = ref([]);

  const loadHarvests = async () => {
    loading.value = true;
    error.value = '';
    try {
      const response = await apiClient.get('/harvests');
      if (response.data.success && Array.isArray(response.data.data)) {
        harvests.value = response.data.data;
        calculateStatistics();
      } else {
        harvests.value = [];
        error.value = '출하 기록 형식이 올바르지 않습니다.';
      }
    } catch (err) {
      console.error('출하 기록 로드 실패:', err);
      error.value = '출하 기록을 불러오는데 실패했습니다.';
      harvests.value = [];
    } finally {
      loading.value = false;
    }
  };
  
  const loadCrops = async () => {
    try {
      const response = await apiClient.get('/crops');
      if (response.data.success && Array.isArray(response.data.data)) {
        availableCrops.value = response.data.data.filter(crop => crop.isActive);
      } else {
        availableCrops.value = [];
      }
    } catch (err) {
      console.error('작물 목록 로드 실패:', err);
      availableCrops.value = [];
    }
  };

  const loadGreenhouses = async () => {
    try {
      const response = await apiClient.get('/greenhouses');
      if (response.data.success && Array.isArray(response.data.data)) {
        availableGreenhouses.value = response.data.data.filter(g => g.isActive !== false); // 상태가 없거나 true인 경우
      } else {
        availableGreenhouses.value = [];
      }
    } catch (err) {
      console.error('비닐하우스 목록 로드 실패:', err);
      availableGreenhouses.value = [];
    }
  };

  const saveHarvest = async (harvestData, harvestId = null) => {
    saving.value = true;
    try {
      if (harvestId) {
        await apiClient.put(`/harvests/${harvestId}`, harvestData);
      } else {
        await apiClient.post('/harvests', harvestData);
      }
      await loadHarvests();
      return true;
    } catch (err) {
      console.error('출하 기록 저장 실패:', err);
      error.value = err.response?.data?.error || err.message || '출하 기록 저장에 실패했습니다.';
      return false;
    } finally {
      saving.value = false;
    }
  };

  const toggleHarvestStatus = async (harvestId) => {
    try {
      // API 문서에 따르면 DELETE 메서드를 사용하므로, 이를 따릅니다.
      // 실제 비활성화/활성화 토글 API가 있다면 PATCH /harvests/{id}/toggle 등으로 변경해야 합니다.
      await apiClient.delete(`/harvests/${harvestId}`);
      await loadHarvests();
      return true;
    } catch (err) {
      console.error('출하 기록 상태 변경 실패:', err);
      error.value = '출하 기록 상태 변경에 실패했습니다.';
      return false;
    }
  };
  
  const generateHarvestQRCode = async (harvestId) => {
    try {
        const response = await apiClient.get(`/qrcode/harvest/${harvestId}/detailed`);
        if (response.data.success) {
            return response.data;
        }
        return null;
    } catch (err) {
        console.error('QR 코드 생성 실패:', err);
        error.value = 'QR 코드 생성에 실패했습니다.';
        return null;
    }
  };

  const calculateStatistics = () => {
    const totalQuantity = harvests.value
        .filter(h => h.isActive)
        .reduce((sum, harvest) => sum + (harvest.quantity || 0), 0);
    const totalRevenue = harvests.value
        .filter(h => h.isActive)
        .reduce((sum, harvest) => sum + (harvest.totalPrice || 0), 0);
    
    statistics.value = {
      totalQuantity,
      totalRevenue,
    };
  };
  
  const todayHarvests = computed(() => {
    const today = new Date().toISOString().split('T')[0];
    return harvests.value.filter(
      (harvest) => harvest.isActive && harvest.harvestDate && harvest.harvestDate.startsWith(today)
    );
  });

  return {
    harvests,
    statistics,
    loading,
    error,
    saving,
    availableCrops,
    availableGreenhouses,
    todayHarvests,
    loadHarvests,
    loadCrops,
    loadGreenhouses,
    saveHarvest,
    toggleHarvestStatus,
    generateHarvestQRCode,
  };
} 