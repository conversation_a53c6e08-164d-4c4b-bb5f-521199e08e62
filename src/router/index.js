import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import AuctionInfo from '../views/AuctionInfo.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/dashboard',
    name: 'DashboardAlt',
    component: Dashboard
  },
  {
    path: '/auction',
    name: 'AuctionInfo',
    component: AuctionInfo
  },
  {
    path: '/api-test',
    name: 'ApiTest',
    component: () => import('../views/ApiTest.vue')
  },
  {
    path: '/code',
    name: 'CodeManagement',
    component: () => import('../views/CodeManagement.vue')
  },
  {
    path: '/products',
    name: 'ProductManagement',
    component: () => import('../views/ProductManagement.vue')
  },
  {
    path: '/crop',
    name: 'CropManagement',
    component: () => import('../views/CropManagement.vue')
  },
  {
    path: '/equipment',
    redirect: '/'
  },
  {
    path: '/greenhouse',
    name: 'GreenhouseManagement',
    component: () => import('../views/GreenhouseManagement.vue')
  },
  {
    path: '/shipment',
    name: 'HarvestManagement',
    component: () => import('../views/HarvestManagement.vue')
  },
  {
    path: '/calendar',
    name: 'Calendar',
    component: () => import('../views/Calendar.vue')
  },
  {
    path: '/salary',
    name: 'SalaryManagement',
    component: () => import('../views/SalaryManagement.vue')
  },
  {
    path: '/staff',
    name: 'EmployeeManagement',
    component: () => import('../views/EmployeeManagement.vue')
  },
  {
    path: '/stats',
    name: 'Statistics',
    component: () => import('../views/Statistics.vue')
  },
  {
    path: '/harvest-dashboard',
    name: 'HarvestDashboard',
    component: () => import('../views/HarvestDashboard.vue')
  },
  {
    path: '/harvest-test',
    name: 'HarvestTest',
    component: () => import('../views/HarvestTest.vue')
  },
  {
    path: '/activity-logs',
    name: 'ActivityLogs',
    component: () => import('../views/ActivityLogs.vue')
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router 