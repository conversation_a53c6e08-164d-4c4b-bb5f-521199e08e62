<template>
  <div class="system-section">
    <div class="system-status">
      <h3>📊 작물 현황</h3>
      <div class="status-cards">
        <div class="status-card">
          <div class="status-icon">🌱</div>
          <div class="status-info">
            <div class="status-label">총 작물</div>
            <div class="status-value">{{ statistics?.totalCrops || 0 }}개</div>
          </div>
        </div>
        <div class="status-card">
          <div class="status-icon">📅</div>
          <div class="status-info">
            <div class="status-label">수확 예정</div>
            <div class="status-value">{{ statistics?.upcomingHarvests || 0 }}개</div>
          </div>
        </div>
        <div class="status-card">
          <div class="status-icon">📊</div>
          <div class="status-info">
            <div class="status-label">카테고리별</div>
            <div class="status-value">{{ getCategoryBreakdown() }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  statistics: {
    type: Object,
    default: () => ({})
  }
})

const getCategoryBreakdown = () => {
  if (!props.statistics.categories) return '0/0/0/0'
  const { categories } = props.statistics
  return `${categories['채소'] || 0}/${categories['과일'] || 0}/${categories['곡물'] || 0}/${categories['구황작물'] || 0}`
}
</script>

<style scoped>
.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon {
  font-size: 2rem;
}

.status-info {
  flex: 1;
}

.status-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.status-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}
</style> 