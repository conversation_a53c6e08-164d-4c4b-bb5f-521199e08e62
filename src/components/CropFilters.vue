<template>
  <div class="filters">
    <div class="search-box">
      <input 
        v-model="searchKeyword" 
        type="text" 
        placeholder="작물명 또는 상품코드 검색..."
        class="search-input"
        @input="onSearchChange"
      >
      <span class="search-icon">🔍</span>
    </div>
    <select v-model="selectedCategory" class="category-select" @change="onCategoryChange">
      <option value="">전체 카테고리</option>
      <option value="채소">채소</option>
      <option value="과일">과일</option>
      <option value="곡물">곡물</option>
      <option value="구황작물">구황작물</option>
    </select>
    <button @click="onUpcomingClick" class="upcoming-btn">
      📅 수확 예정
    </button>
    <select v-model="selectedGreenhouse" @change="onGreenhouseChange" class="greenhouse-select">
      <option value="">전체 비닐하우스</option>
      <option v-for="greenhouse in availableGreenhouses" :key="greenhouse.id" :value="greenhouse.id">
        {{ greenhouse.greenhouseNumber }}
      </option>
    </select>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'

const props = defineProps({
  availableGreenhouses: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['search-change', 'category-change', 'upcoming-click', 'greenhouse-change'])

const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedGreenhouse = ref('')

const onSearchChange = () => {
  emit('search-change', searchKeyword.value)
}

const onCategoryChange = () => {
  emit('category-change', selectedCategory.value)
}

const onUpcomingClick = () => {
  emit('upcoming-click')
}

const onGreenhouseChange = () => {
  emit('greenhouse-change', selectedGreenhouse.value)
}

// 외부에서 필터 초기화할 때 사용
const resetFilters = () => {
  searchKeyword.value = ''
  selectedCategory.value = ''
  selectedGreenhouse.value = ''
}

// 외부에서 접근할 수 있도록 expose
defineExpose({
  resetFilters,
  searchKeyword,
  selectedCategory,
  selectedGreenhouse
})
</script>

<style scoped>
.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.category-select,
.greenhouse-select,
.upcoming-btn {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 150px;
  cursor: pointer;
}

.upcoming-btn {
  background: #3498db;
  color: white;
  border: none;
}

.upcoming-btn:hover {
  background: #2980b9;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
}
</style> 