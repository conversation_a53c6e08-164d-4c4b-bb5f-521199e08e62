<template>
  <div v-if="error" class="error-handler" :style="{ borderLeftColor: errorColor }">
    <!-- 🚨 에러 헤더 -->
    <div class="error-header">
      <div class="error-icon">{{ errorIcon }}</div>
      <div class="error-title">
        <h4>{{ getErrorTitle() }}</h4>
        <span class="error-context" v-if="error.context">{{ error.context }}</span>
      </div>
      <button @click="close" class="close-btn" title="에러 닫기">✕</button>
    </div>

    <!-- 📝 에러 메시지 -->
    <div class="error-message">
      <p>{{ error.message }}</p>
    </div>

    <!-- 🔍 상세 정보 (개발 모드에서만 표시) -->
    <div v-if="showDetails" class="error-details">
      <details>
        <summary>🔍 상세 정보 보기</summary>
        <div class="details-content">
          <div class="detail-item">
            <strong>에러 타입:</strong> {{ error.type }}
          </div>
          <div class="detail-item">
            <strong>발생 시간:</strong> {{ formatTimestamp(error.timestamp) }}
          </div>
          <div class="detail-item" v-if="error.details?.url">
            <strong>요청 URL:</strong> {{ error.details.url }}
          </div>
          <div class="detail-item" v-if="error.details?.method">
            <strong>요청 방법:</strong> {{ error.details.method.toUpperCase() }}
          </div>
          <div class="detail-item" v-if="error.details?.status">
            <strong>HTTP 상태:</strong> {{ error.details.status }} {{ error.details.statusText }}
          </div>
          <div class="detail-item" v-if="error.details?.originalError">
            <strong>원본 에러:</strong> {{ error.details.originalError }}
          </div>
          <div class="detail-item" v-if="errorStats.totalErrors > 0">
            <strong>재시도 횟수:</strong> {{ errorStats.totalErrors }}/{{ errorStats.maxRetries }}
          </div>
        </div>
      </details>
    </div>

    <!-- 🛠️ 액션 버튼들 -->
    <div class="error-actions">
      <button 
        v-if="canRetry" 
        @click="handleRetry" 
        class="retry-btn"
        :disabled="isRetrying"
      >
        <span v-if="isRetrying" class="loading-spinner">🔄</span>
        <span v-else>🔄 다시 시도</span>
        <span v-if="errorStats.remainingRetries > 0" class="retry-count">
          ({{ errorStats.remainingRetries }}회 남음)
        </span>
      </button>
      
      <button @click="close" class="close-action-btn">
        ✕ 닫기
      </button>
    </div>

    <!-- 💡 도움말 팁 -->
    <div class="error-tips">
      <div class="tip-item" v-if="error.type === 'LAZY_LOADING_ERROR'">
        <p>🐛 <strong>지연 로딩 오류:</strong> 백엔드에서 데이터베이스 세션 관리 문제가 발생했습니다.</p>
        <p>💡 <strong>해결 방법:</strong> 잠시 후 다시 시도하거나 개발팀에 문의해주세요.</p>
      </div>
      
      <div class="tip-item" v-else-if="error.type === 'NETWORK_ERROR'">
        <p>🌐 <strong>네트워크 오류:</strong> 서버와의 연결에 문제가 있습니다.</p>
        <p>💡 <strong>확인사항:</strong></p>
        <ul>
          <li>인터넷 연결 상태 확인</li>
          <li>백엔드 서버가 실행 중인지 확인 (포트 8080)</li>
          <li>방화벽 설정 확인</li>
        </ul>
      </div>
      
      <div class="tip-item" v-else-if="error.type === 'TIMEOUT_ERROR'">
        <p>⏰ <strong>시간 초과:</strong> 요청이 너무 오래 걸렸습니다.</p>
        <p>💡 <strong>해결 방법:</strong> 네트워크 상태를 확인하고 다시 시도해주세요.</p>
      </div>
      
      <div class="tip-item" v-else-if="error.type === 'DATABASE_ERROR'">
        <p>🗄️ <strong>데이터베이스 오류:</strong> 데이터베이스 연결에 문제가 있습니다.</p>
        <p>💡 <strong>해결 방법:</strong> 잠시 후 다시 시도하거나 관리자에게 문의해주세요.</p>
      </div>
      
      <div class="tip-item" v-else>
        <p>💡 <strong>일반적인 해결 방법:</strong></p>
        <ul>
          <li>페이지를 새로고침해보세요</li>
          <li>잠시 후 다시 시도해보세요</li>
          <li>문제가 지속되면 개발팀에 문의해주세요</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { apiUtils } from '@/utils/axios'

// 📋 Props 정의
const props = defineProps({
  error: {
    type: Object,
    default: null
  },
  canRetry: {
    type: Boolean,
    default: false
  },
  errorStats: {
    type: Object,
    default: () => ({ totalErrors: 0, maxRetries: 3, remainingRetries: 3 })
  },
  showDetails: {
    type: Boolean,
    default: false
  }
})

// 📋 Emits 정의
const emit = defineEmits(['retry', 'close'])

// 📊 상태 관리
const isRetrying = ref(false)

// 🎯 계산된 속성들
const errorIcon = computed(() => 
  props.error ? apiUtils.getErrorIcon(props.error.type) : '❓'
)

const errorColor = computed(() => 
  props.error ? apiUtils.getErrorColor(props.error.type) : '#95a5a6'
)

// 🛠️ 메서드들
const getErrorTitle = () => {
  if (!props.error) return '오류'
  
  const titles = {
    'LAZY_LOADING_ERROR': '데이터베이스 세션 오류',
    'DATABASE_ERROR': '데이터베이스 연결 오류',
    'NETWORK_ERROR': '네트워크 연결 오류',
    'TIMEOUT_ERROR': '요청 시간 초과',
    'SERVER_ERROR': '서버 내부 오류',
    'BAD_REQUEST': '잘못된 요청',
    'NOT_FOUND': '리소스를 찾을 수 없음',
    'UNAUTHORIZED': '인증 필요',
    'FORBIDDEN': '접근 권한 없음',
    'SERVICE_UNAVAILABLE': '서비스 사용 불가',
    'UNKNOWN_ERROR': '알 수 없는 오류'
  }
  
  return titles[props.error.type] || '오류 발생'
}

const formatTimestamp = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const handleRetry = async () => {
  if (isRetrying.value) return
  
  isRetrying.value = true
  emit('retry')
  
  // 3초 후 로딩 상태 해제
  setTimeout(() => {
    isRetrying.value = false
  }, 3000)
}

const close = () => {
  emit('close')
}
</script>

<style scoped>
.error-handler {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  border-left: 4px solid;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.error-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.error-title {
  flex: 1;
}

.error-title h4 {
  margin: 0 0 5px 0;
  color: #856404;
  font-size: 1.1rem;
}

.error-context {
  font-size: 0.9rem;
  color: #7f8c8d;
  font-style: italic;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #e74c3c;
}

.error-message {
  margin-bottom: 15px;
}

.error-message p {
  margin: 0;
  color: #856404;
  font-size: 1rem;
  line-height: 1.5;
}

.error-details {
  margin-bottom: 15px;
}

.error-details details {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 6px;
  padding: 10px;
}

.error-details summary {
  cursor: pointer;
  font-weight: bold;
  color: #7f8c8d;
  margin-bottom: 10px;
}

.details-content {
  font-size: 0.85rem;
  color: #2c3e50;
}

.detail-item {
  margin-bottom: 5px;
  padding: 3px 0;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
  flex-wrap: wrap;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.retry-btn:hover:not(:disabled) {
  background: #2980b9;
  transform: translateY(-2px);
}

.retry-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
}

.retry-count {
  font-size: 0.8rem;
  opacity: 0.8;
}

.close-action-btn {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.close-action-btn:hover {
  background: #7f8c8d;
  transform: translateY(-2px);
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-tips {
  background: rgba(255, 255, 255, 0.7);
  border-radius: 8px;
  padding: 15px;
  border-left: 4px solid #f39c12;
}

.tip-item p {
  margin: 5px 0;
  font-size: 0.9rem;
  color: #856404;
}

.tip-item ul {
  margin: 5px 0 5px 20px;
  padding: 0;
}

.tip-item li {
  margin: 3px 0;
  font-size: 0.85rem;
  color: #856404;
}

/* 반응형 */
@media (max-width: 768px) {
  .error-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .error-actions {
    flex-direction: column;
  }
  
  .retry-btn,
  .close-action-btn {
    width: 100%;
    justify-content: center;
  }
}
</style> 