<template>
  <div class="layout-container">
    <!-- 상단바 -->
    <header class="topbar">
      <div class="header-content">
        <div class="header-left">
          <h1 class="logo">🏠 농장 관리 대시보드</h1>
          <p class="welcome-message">오늘도 좋은 하루 되세요!</p>
        </div>
        <div class="header-controls">
          <button class="logout-btn" @click="logout">
            <span class="logout-icon">🚪</span>
            로그아웃
          </button>
        </div>
      </div>
    </header>

    <!-- 🚀 롤링 배너 -->
    <RollingBanner 
      v-if="showRollingBanner"
      :auto-start="true"
      :interval="6000"
      :max-items="3"
      @close="closeRollingBanner"
    />

    <!-- 날씨 위젯 영역 (주석 처리) -->
    <!--
    <div class="weather-widget">
      <div class="weather-container">
        <div class="weather-header">
          <div class="weather-title">📅 일주일 날씨</div>
          <div class="weather-location">{{ weatherLocation }}</div>
          <div v-if="weatherLoading" class="weather-loading">
            <span class="loading-spinner">🌤️</span>
            날씨 정보 로딩 중...
          </div>
        </div>
        
        <div v-if="weatherAlerts.length > 0" class="weather-alert-banner">
          <div class="alert-banner-content">
            <span class="alert-icon">⚠️</span>
            <span class="alert-text">{{ weatherAlerts[0].alertMessage }}</span>
            <button class="alert-detail-btn" @click="showAlertDetail = true">
              자세히 보기
            </button>
          </div>
        </div>
        
        <div class="weather-cards">
          <div 
            v-for="(day, index) in weatherData" 
            :key="index" 
            class="weather-card"
            :class="getAlertLevelClass(day.alertLevel)"
            @click="showWeatherDetail(day)"
          >
            <div class="weather-day">{{ formatDay(day.date) }}</div>
            <div class="weather-icon">{{ getWeatherIcon(day.weatherIcon) }}</div>
            <div class="weather-temp">
              <div class="temp-current">{{ formatTemperature(day.tempCurrent) }}°</div>
              <div class="temp-range">
                <span class="temp-max">{{ formatTemperature(day.tempMax) }}°</span>
                <span class="temp-min">{{ formatTemperature(day.tempMin) }}°</span>
              </div>
            </div>
            <div class="weather-desc">{{ day.weatherDescription }}</div>
            <div v-if="day.alertLevel !== 'NONE'" class="weather-alert">
              ⚠️ {{ day.alertMessage }}
            </div>
          </div>
        </div>
      </div>
    </div>
    -->

    <!-- 플로팅 최근 방문 메뉴 -->
    <div class="floating-history" :class="{ 'floating-history-collapsed': isHistoryCollapsed }">
      <div class="floating-history-toggle" @click="toggleHistory">
        <span v-if="!isHistoryCollapsed">◀</span>
        <span v-else>▶</span>
      </div>
      <div class="floating-history-content">
        <div class="floating-history-header">
          <h3>📋 최근 방문</h3>
        </div>
        <div class="floating-history-cards">
          <!-- 고정 홈 카드 -->
          <div 
            class="floating-history-card home-card"
            @click="goToHistory('/')"
          >
            <div class="floating-history-icon">🏠</div>
            <div class="floating-history-title">홈</div>
          </div>
          <!-- 최근 방문 페이지들 (최대 4개) -->
          <div 
            v-for="(route, index) in history.slice(0, 4)" 
            :key="index"
            class="floating-history-card"
            @click="goToHistory(route)"
          >
            <div class="floating-history-icon">{{ getRouteIcon(route) }}</div>
            <div class="floating-history-title">{{ getRouteTitle(route) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 날씨 위젯 영역 -->
    <div class="weather-widget">
      <div class="weather-container">
        <div class="weather-header">
          <div class="weather-title">📅 일주일 날씨</div>
          <div class="weather-location">
            {{ weatherLocation }}
            <span v-if="locationPermission === 'granted'" class="location-status granted">📍</span>
            <span v-else-if="locationPermission === 'denied'" class="location-status denied">❌</span>
            <span v-else class="location-status loading">⏳</span>
          </div>
          <div v-if="weatherLoading" class="weather-loading">
            <span class="loading-spinner">🌤️</span>
            날씨 정보 로딩 중...
          </div>
        </div>
        
        <!-- 위치 권한 요청 배너 -->
        <div v-if="showLocationRequest" class="location-request-banner">
          <div class="location-request-content">
            <span class="location-icon">📍</span>
            <span class="location-text">정확한 날씨 정보를 위해 위치 권한이 필요합니다.</span>
            <button class="location-request-btn" @click="requestLocationPermission">
              위치 권한 허용
            </button>
          </div>
        </div>
        
        <div class="weather-cards">
          <div v-if="weatherData.length === 0 && !weatherLoading" class="no-weather-data">
            <div class="no-data-icon">🌤️</div>
            <div class="no-data-text">날씨 정보를 불러올 수 없습니다</div>
            <button class="retry-btn" @click="fetchWeatherData">다시 시도</button>
          </div>
          <div 
            v-for="(day, index) in weatherData" 
            :key="index" 
            class="weather-card"
            :class="getAlertLevelClass(day.alertLevel)"
            @click="showWeatherDetail(day)"
          >
            <div class="weather-day">{{ formatDay(day.date) }}</div>
            <div class="weather-icon">{{ getWeatherIcon(day.weatherIcon) }}</div>
            <div class="weather-temp">
              <div class="temp-current">{{ formatTemperature(day.tempCurrent) }}°</div>
              <div class="temp-range">
                <span class="temp-max">{{ formatTemperature(day.tempMax) }}°</span>
                <span class="temp-min">{{ formatTemperature(day.tempMin) }}°</span>
              </div>
            </div>
            <div class="weather-desc">{{ day.weatherDescription }}</div>
            <div v-if="day.alertLevel !== 'NONE'" class="weather-alert">
              ⚠️ {{ day.alertMessage }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 메인 콘텐츠 영역 -->
    <main class="main-content scroll-container">
      <slot></slot>
    </main>

    <!-- 푸터 -->
    <footer class="footer">
      <p>© 2025 농사관리시스템. All rights reserved.</p>
    </footer>

    <!-- 날씨 상세 모달 (주석 처리) -->
    <!--
    <div v-if="showWeatherModal" class="weather-modal-overlay" @click="closeWeatherModal">
      <div class="weather-modal-content" @click.stop>
        <div class="weather-modal-header">
          <h3>{{ selectedWeather?.date }} 날씨 상세</h3>
          <button class="close-btn" @click="closeWeatherModal">✕</button>
        </div>
        <div v-if="selectedWeather" class="weather-modal-body">
          <div class="weather-detail-grid">
            <div class="detail-item">
              <div class="detail-label">기온</div>
              <div class="detail-value">{{ Math.round(selectedWeather.tempCurrent) }}°C</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">최고/최저</div>
              <div class="detail-value">{{ Math.round(selectedWeather.tempMax) }}° / {{ Math.round(selectedWeather.tempMin) }}°</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">습도</div>
              <div class="detail-value">{{ selectedWeather.humidity }}%</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">풍속</div>
              <div class="detail-value">{{ selectedWeather.windSpeed }}m/s</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">강수량</div>
              <div class="detail-value">{{ selectedWeather.rainAmount }}mm</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">날씨</div>
              <div class="detail-value">{{ selectedWeather.weatherDescription }}</div>
            </div>
          </div>
          <div v-if="selectedWeather.alertLevel !== 'NONE'" class="weather-alert-detail">
            <div class="alert-header">⚠️ 기상 주의사항</div>
            <div class="alert-message">{{ selectedWeather.alertMessage }}</div>
          </div>
        </div>
      </div>
    </div>
    -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import RollingBanner from './RollingBanner.vue'

// 🆕 API 베이스 URL 설정
const API_BASE_URL = 'http://localhost:8080/api'

const history = ref([])
const weatherData = ref([])
const showRollingBanner = ref(true) // 롤링 배너 표시 여부
const weatherLocation = ref('위치 확인 중...')
const weatherLoading = ref(false)
const showWeatherModal = ref(false)
const selectedWeather = ref(null)
const weatherAlerts = ref([])
const showAlertDetail = ref(false)
const locationPermission = ref('prompt') // 'prompt', 'granted', 'denied'
const showLocationRequest = ref(false)
const isHistoryCollapsed = ref(false) // 플로팅 메뉴 접기/펼치기 상태

// 라우트 아이콘 매핑
const getRouteIcon = (route) => {
  const iconMap = {
    '/': '🏠',
    '/dashboard': '🏠',
    '/crop': '🌾',
    '/equipment': '🛠️',
    '/greenhouse': '🏠',
    '/shipment': '📦',
    '/auction': '📊',
    '/products': '🌾',
    '/salary': '💰',
    '/staff': '👷',
    '/stats': '📈',
    '/harvest-dashboard': '🌾',
    '/harvest-test': '🧪',
    '/code': '⚙️',
    '/calendar': '📅',
    '/activity-logs': '📋',
    '/api-test': '🧪'
  }
  return iconMap[route] || '📄'
}

// 라우트 제목 매핑
const getRouteTitle = (route) => {
  const titleMap = {
    '/': '대시보드',
    '/dashboard': '대시보드',
    '/crop': '작물관리',
    '/equipment': '장비관리',
    '/greenhouse': '비닐하우스',
    '/shipment': '출하관리',
    '/auction': '경매조회',
    '/products': '상품관리',
    '/salary': '급여/잔고',
    '/staff': '직원관리',
    '/stats': '통계',
    '/harvest-dashboard': '수확관리',
    '/harvest-test': '수확테스트',
    '/code': '코드관리',
    '/calendar': '일정관리',
    '/activity-logs': '활동내역',
    '/api-test': 'API테스트'
  }
  return titleMap[route] || route
}

// 방문 시간 계산
const getVisitTime = (index) => {
  const times = ['방금 전', '5분 전', '10분 전', '30분 전', '1시간 전']
  return times[index] || '오래 전'
}

// 안전한 온도 포맷팅
const formatTemperature = (temp) => {
  if (temp === null || temp === undefined || isNaN(temp)) {
    return '--'
  }
  const numTemp = parseFloat(temp)
  if (isNaN(numTemp)) {
    return '--'
  }
  return Math.round(numTemp)
}

// 날씨 데이터 검증 및 정리
const validateWeatherData = (data) => {
  console.log('validateWeatherData 호출됨, 입력 데이터:', data)
  console.log('데이터 타입:', typeof data)
  console.log('Array.isArray(data):', Array.isArray(data))
  
  if (!Array.isArray(data)) {
    console.warn('날씨 데이터가 배열이 아닙니다:', data)
    return []
  }
  
  const validatedData = data.map(day => ({
    date: day.date || '날짜 없음',
    tempMin: parseFloat(day.tempMin) || 0,
    tempMax: parseFloat(day.tempMax) || 0,
    tempCurrent: parseFloat(day.tempCurrent) || 0,
    humidity: parseFloat(day.humidity) || 0,
    windSpeed: parseFloat(day.windSpeed) || 0,
    rainAmount: parseFloat(day.rainAmount) || 0,
    weatherMain: day.weatherMain || 'Unknown',
    weatherDescription: getWeatherDescription(day.weatherDescription || '날씨 정보 없음'),
    weatherIcon: day.weatherIcon || '01d',
    alertLevel: day.alertLevel || 'NONE',
    alertMessage: day.alertMessage || ''
  }))
  
  console.log('검증된 데이터:', validatedData)
  return validatedData
}

// 쿠키에서 히스토리 로드
const loadHistoryFromCookie = () => {
  const cookieValue = document.cookie
    .split('; ')
    .find(row => row.startsWith('pageHistory='))
    ?.split('=')[1]
  
  if (cookieValue) {
    try {
      const decoded = decodeURIComponent(cookieValue)
      history.value = JSON.parse(decoded)
    } catch (error) {
      console.error('히스토리 쿠키 파싱 오류:', error)
      history.value = []
    }
  }
}

// 쿠키에 히스토리 저장
const saveHistoryToCookie = () => {
  const historyJson = JSON.stringify(history.value)
  const encoded = encodeURIComponent(historyJson)
  document.cookie = `pageHistory=${encoded}; path=/; max-age=86400` // 24시간 유지
}

// 히스토리에 페이지 추가
const addToHistory = (route) => {
  // 이미 있는 경우 제거
  const existingIndex = history.value.indexOf(route)
  if (existingIndex > -1) {
    history.value.splice(existingIndex, 1)
  }
  
  // 맨 앞에 추가
  history.value.unshift(route)
  
  // 최대 5개까지만 유지
  if (history.value.length > 5) {
    history.value = history.value.slice(0, 5)
  }
  
  // 쿠키에 저장
  saveHistoryToCookie()
}

// 히스토리 이동
const goToHistory = (route) => {
  // 현재 페이지를 히스토리에 추가
  const currentRoute = window.location.pathname
  if (currentRoute !== route) {
    addToHistory(currentRoute)
  }
  
  // 해당 페이지로 이동
  window.location.href = route
}

// 플로팅 메뉴 토글
const toggleHistory = () => {
  isHistoryCollapsed.value = !isHistoryCollapsed.value
}

// 위치 권한 확인
const checkLocationPermission = () => {
  if (!navigator.permissions) {
    return 'prompt'
  }
  
  return navigator.permissions.query({ name: 'geolocation' })
    .then(result => result.state)
    .catch(() => 'prompt')
}

// 사용자 위치 가져오기
const getUserLocation = () => {
  return new Promise(async (resolve, reject) => {
    if (!navigator.geolocation) {
      locationPermission.value = 'denied'
      weatherLocation.value = '위치 정보 미지원'
      reject(new Error('브라우저가 위치 정보를 지원하지 않습니다.'))
      return
    }
    
    try {
      const permission = await checkLocationPermission()
      locationPermission.value = permission
      
      if (permission === 'denied') {
        weatherLocation.value = '위치 권한 거부됨'
        showLocationRequest.value = true
        reject(new Error('위치 권한이 거부되었습니다.'))
        return
      }
      
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          locationPermission.value = 'granted'
          weatherLocation.value = '위치 확인 중...'
          
          try {
            // Reverse Geocoding으로 도시명 가져오기
            const cityName = await getCityName(position.coords.latitude, position.coords.longitude)
            weatherLocation.value = cityName
          } catch (error) {
            console.warn('도시명 가져오기 실패:', error)
            weatherLocation.value = '현재 위치'
          }
          
          resolve({
            latitude: position.coords.latitude,
            longitude: position.coords.longitude
          })
        },
        (error) => {
          console.warn('위치 정보 가져오기 실패:', error.message)
          locationPermission.value = 'denied'
          weatherLocation.value = '위치 확인 실패'
          showLocationRequest.value = true
          // 기본값으로 서울 사용
          resolve({
            latitude: 37.5665,
            longitude: 126.9780
          })
        },
        {
          enableHighAccuracy: false,
          timeout: 5000,
          maximumAge: 600000 // 10분
        }
      )
    } catch (error) {
      locationPermission.value = 'denied'
      weatherLocation.value = '위치 확인 실패'
      reject(error)
    }
  })
}

// Reverse Geocoding으로 도시명 가져오기
const getCityName = async (latitude, longitude) => {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10&accept-language=ko`
    )
    
    if (!response.ok) {
      throw new Error('Reverse geocoding API 호출 실패')
    }
    
    const data = await response.json()
    console.log('Reverse geocoding 결과:', data)
    
    // 주소에서 도시명 추출
    const address = data.address
    if (address) {
      // 한국의 경우 시/군/구 단위로 표시
      if (address.city) {
        return address.city
      } else if (address.county) {
        return address.county
      } else if (address.state) {
        return address.state
      } else if (address.town) {
        return address.town
      } else if (address.village) {
        return address.village
      }
    }
    
    // 기본값
    return '현재 위치'
  } catch (error) {
    console.error('Reverse geocoding 오류:', error)
    return '현재 위치'
  }
}

// 위치 권한 다시 요청
const requestLocationPermission = () => {
  showLocationRequest.value = false
  weatherLocation.value = '위치 확인 중...'
  fetchWeatherData()
}

// 날씨 API 호출 중복 방지
let isWeatherLoading = false

// 날씨 API 호출
const fetchWeatherData = async () => {
  if (isWeatherLoading) {
    console.log('날씨 데이터 로딩 중, 중복 요청 무시')
    return
  }
  
  isWeatherLoading = true
  weatherLoading.value = true
  console.log('날씨 데이터 로딩 시작...')
  
  try {
    // 사용자 위치 가져오기
    const userLocation = await getUserLocation()
    console.log('사용자 위치:', userLocation)
    
    const response = await axios.post(`${API_BASE_URL}/weather/weekly`, {
      latitude: userLocation.latitude,
      longitude: userLocation.longitude,
      locationName: '' // 서버에서 위치명을 반환하도록 빈 값 전송
    })
    
    console.log('날씨 API 응답:', response.data)
    
    if (response.data && response.data.success) {
      console.log('API 응답 데이터 구조:', {
        data: response.data.data,
        forecast: response.data.data?.forecast,
        isArray: Array.isArray(response.data.data?.forecast),
        length: response.data.data?.forecast?.length
      })
      weatherData.value = validateWeatherData(response.data.data?.forecast || [])
      
      // 백엔드에서 위치명을 제공하는 경우 사용, 아니면 현재 설정된 위치명 유지
      if (response.data.data?.location?.name) {
        weatherLocation.value = response.data.data.location.name
      }
      
      console.log('날씨 데이터 로드 완료:', weatherData.value)
    } else {
      console.error('날씨 API 응답 실패:', response.data)
      weatherData.value = []
      weatherLocation.value = '날씨 정보 없음'
    }
  } catch (error) {
    console.error('날씨 API 호출 오류:', error)
    weatherData.value = []
    weatherLocation.value = '날씨 정보 없음'
  } finally {
    weatherLoading.value = false
    isWeatherLoading = false
    console.log('최종 날씨 데이터:', weatherData.value)
  }
}

// 날씨 알림 API 호출
const fetchWeatherAlerts = async () => {
  try {
    // 사용자 위치 가져오기
    const userLocation = await getUserLocation()
    
    const response = await axios.get(`${API_BASE_URL}/weather/alerts`, {
      params: {
        latitude: userLocation.latitude,
        longitude: userLocation.longitude
      }
    })
    
    console.log('날씨 알림 API 응답:', response.data)
    console.log('알림 데이터 구조:', {
      data: response.data.data,
      alerts: response.data.data?.alerts,
      isArray: Array.isArray(response.data.data?.alerts),
      length: response.data.data?.alerts?.length
    })
    
    if (response.data && response.data.success) {
      weatherAlerts.value = response.data.data?.alerts || []
      console.log('날씨 알림 로드 완료:', response.data)
    } else {
      console.error('날씨 알림 API 응답 실패:', response.data)
      weatherAlerts.value = []
    }
  } catch (error) {
    console.error('날씨 알림 API 호출 오류:', error)
    weatherAlerts.value = []
  }
}



// 날씨 아이콘 매핑
const getWeatherIcon = (iconCode) => {
  const iconMap = {
    '01d': '☀️', '01n': '🌙',
    '02d': '⛅', '02n': '☁️',
    '03d': '☁️', '03n': '☁️',
    '04d': '☁️', '04n': '☁️',
    '09d': '🌧️', '09n': '🌧️',
    '10d': '🌦️', '10n': '🌧️',
    '11d': '⛈️', '11n': '⛈️',
    '13d': '🌨️', '13n': '🌨️',
    '50d': '🌫️', '50n': '🌫️'
  }
  return iconMap[iconCode] || '🌤️'
}

// 날씨 설명 한글 변환
const getWeatherDescription = (description) => {
  const weatherMap = {
    // 맑음
    'clear sky': '맑음',
    'clear': '맑음',
    
    // 흐림
    'scattered clouds': '구름 조금',
    'broken clouds': '구름 많음',
    'overcast clouds': '흐림',
    'few clouds': '구름 조금',
    'clouds': '구름',
    
    // 비
    'light rain': '가벼운 비',
    'moderate rain': '비',
    'heavy rain': '강한 비',
    'rain': '비',
    'drizzle': '이슬비',
    'shower rain': '소나기',
    
    // 눈
    'snow': '눈',
    'light snow': '가벼운 눈',
    'heavy snow': '강한 눈',
    'sleet': '진눈깨비',
    
    // 천둥번개
    'thunderstorm': '천둥번개',
    'storm': '폭풍',
    
    // 안개
    'mist': '안개',
    'fog': '안개',
    'haze': '연무',
    
    // 기타
    'smoke': '연기',
    'dust': '먼지',
    'sand': '모래',
    'ash': '재',
    'squall': '돌풍',
    'tornado': '토네이도'
  }
  
  // 소문자로 변환하여 매칭
  const lowerDescription = description.toLowerCase()
  
  // 정확한 매칭 시도
  if (weatherMap[lowerDescription]) {
    return weatherMap[lowerDescription]
  }
  
  // 부분 매칭 시도
  for (const [english, korean] of Object.entries(weatherMap)) {
    if (lowerDescription.includes(english)) {
      return korean
    }
  }
  
  // 매칭되지 않으면 원본 반환
  return description
}

// 알림 레벨 클래스
const getAlertLevelClass = (alertLevel) => {
  const classMap = {
    'NONE': '',
    'LOW': 'alert-low',
    'MEDIUM': 'alert-medium',
    'HIGH': 'alert-high'
  }
  return classMap[alertLevel] || ''
}

// 날짜 포맷팅
const formatDay = (dateStr) => {
  const date = new Date(dateStr)
  const today = new Date()
  const tomorrow = new Date(today)
  tomorrow.setDate(tomorrow.getDate() + 1)
  
  if (date.toDateString() === today.toDateString()) {
    return '오늘'
  } else if (date.toDateString() === tomorrow.toDateString()) {
    return '내일'
  } else {
    const days = ['일', '월', '화', '수', '목', '금', '토']
    return days[date.getDay()]
  }
}

// 날씨 상세 모달
const showWeatherDetail = (weather) => {
  selectedWeather.value = weather
  showWeatherModal.value = true
}

const closeWeatherModal = () => {
  showWeatherModal.value = false
  selectedWeather.value = null
}

// 로그아웃 함수
const logout = () => {
  if (confirm('정말 로그아웃 하시겠습니까?')) {
    console.log('로그아웃')
  }
}

// 롤링 배너 닫기 함수
const closeRollingBanner = () => {
  showRollingBanner.value = false
}

// 외부에서 호출할 수 있도록 함수들 expose
defineExpose({
  addToHistory,
  goToHistory,
  loadHistoryFromCookie,
  saveHistoryToCookie
})

// 컴포넌트 마운트 시 날씨 데이터 로드
onMounted(() => {
  fetchWeatherData()
  fetchWeatherAlerts() // 알림 데이터도 함께 로드
  
  // 쿠키에서 히스토리 로드
  loadHistoryFromCookie()
  
  // 현재 페이지를 히스토리에 추가 (홈이 아닌 경우)
  const currentRoute = window.location.pathname
  if (currentRoute !== '/') {
    addToHistory(currentRoute)
  }
})
</script>

<style scoped>
.layout-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  font-family: sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  box-sizing: border-box;
  position: relative;
}

.topbar {
  height: 80px;
  background: #2c3e50;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 25px;
  z-index: 10;
  flex-shrink: 0;
  border-radius: 20px 20px 0 0;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
  margin: 0 15px;
  margin-top: 5px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.logo {
  font-size: 1.3rem;
  margin: 0;
  white-space: nowrap;
  font-weight: bold;
}

.welcome-message {
  font-size: 0.9rem;
  margin: 0;
  opacity: 0.9;
  font-style: italic;
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logout-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  transition: background-color 0.3s ease;
}

.logout-btn:hover {
  background: #c0392b;
}

.logout-icon {
  font-size: 16px;
}

.user-avatar {
  font-size: 20px;
  flex-shrink: 0;
}

/* 날씨 위젯 스타일 */
.weather-widget {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 12px 0;
  flex-shrink: 0;
  margin: 0 15px 15px 15px;
  margin-top: 20px;
  border-radius: 16px;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.25);
  position: relative;
}

.weather-widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border-radius: 16px;
  pointer-events: none;
}

.weather-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  position: relative;
  z-index: 1;
}

.weather-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.weather-title {
  font-size: 1rem;
  font-weight: bold;
}

.weather-location {
  font-size: 0.85rem;
  opacity: 0.9;
}

.weather-loading {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  opacity: 0.8;
}

.loading-spinner {
  animation: spin 2s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.weather-cards {
  display: flex;
  gap: 12px;
  justify-content: space-between;
  flex-wrap: nowrap;
  width: 100%;
}

.weather-card {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  padding: 10px 6px;
  text-align: center;
  min-width: 70px;
  flex: 1;
  transition: transform 0.3s ease;
  cursor: pointer;
  position: relative;
}

.weather-card:hover {
  transform: translateY(-5px);
}

.weather-card.alert-low {
  border: 2px solid #f39c12;
  background: rgba(243, 156, 18, 0.1);
}

.weather-card.alert-medium {
  border: 2px solid #e67e22;
  background: rgba(230, 126, 34, 0.1);
}

.weather-card.alert-high {
  border: 2px solid #e74c3c;
  background: rgba(231, 76, 60, 0.1);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(231, 76, 60, 0); }
  100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

.weather-day {
  font-size: 0.75rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.weather-icon {
  font-size: 1.3rem;
  margin-bottom: 5px;
}

.weather-temp {
  margin-bottom: 5px;
}

.temp-current {
  font-size: 1rem;
  font-weight: bold;
  margin-bottom: 2px;
}

.temp-range {
  font-size: 0.65rem;
  opacity: 0.9;
}

.temp-max {
  color: #ff6b6b;
  margin-right: 6px;
}

.temp-min {
  color: #74b9ff;
}

.weather-desc {
  font-size: 0.65rem;
  opacity: 0.9;
  margin-bottom: 3px;
}

.weather-alert {
  font-size: 0.6rem;
  color: #ff6b6b;
  font-weight: bold;
  background: rgba(255, 107, 107, 0.2);
  padding: 3px 6px;
  border-radius: 4px;
  margin-top: 4px;
}

/* 플로팅 최근 방문 메뉴 */
.floating-history {
  position: fixed;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  background: #34495e;
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: all 0.3s ease;
  max-height: 80vh;
  overflow: hidden;
}

.floating-history-collapsed {
  width: 50px;
}

.floating-history-collapsed .floating-history-content {
  opacity: 0;
  pointer-events: none;
}

.floating-history-toggle {
  position: absolute;
  right: -15px;
  top: 50%;
  transform: translateY(-50%);
  background: #2c3e50;
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.8rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  z-index: 1001;
}

.floating-history-toggle:hover {
  background: #3498db;
  transform: translateY(-50%) scale(1.1);
}

.floating-history-content {
  padding: 15px;
  transition: all 0.3s ease;
  width: 200px;
}

.floating-history-header {
  margin-bottom: 12px;
  text-align: center;
}

.floating-history-header h3 {
  margin: 0;
  font-size: 0.9rem;
  color: white;
}

.floating-history-cards {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.floating-history-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  min-height: 40px;
}

.floating-history-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.floating-history-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.floating-history-title {
  font-weight: bold;
  font-size: 0.8rem;
  flex: 1;
  text-align: left;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.floating-history-card.home-card {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.floating-history-card.home-card:hover {
  background: rgba(255, 255, 255, 0.25);
}

.sidebar ul {
  display: flex;
  gap: 20px;
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  padding: 10px 16px;
  background: #3d566e;
  border-radius: 8px;
  cursor: pointer;
  white-space: nowrap;
  font-size: 1rem;
}

.main-content {
  flex: 1;
  padding: 20px;
  padding-top: 10px;
  overflow-y: auto;
  min-height: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.footer {
  background: #2c3e50;
  color: white;
  text-align: center;
  padding: 12px;
  font-size: 14px;
  flex-shrink: 0;
  margin: 15px;
  margin-top: auto;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 날씨 알림 배너 */
.weather-alert-banner {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border-radius: 8px;
  padding: 12px 20px;
  margin-bottom: 15px;
  animation: slideIn 0.5s ease;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.alert-banner-content {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.alert-icon {
  font-size: 1.2rem;
  animation: pulse 2s infinite;
}

.alert-text {
  flex: 1;
  font-weight: bold;
  font-size: 0.95rem;
}

.alert-detail-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.alert-detail-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 날씨 상세 모달 */
.weather-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.weather-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.weather-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
}

.weather-modal-header h3 {
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: white;
}

.weather-modal-body {
  padding: 20px;
}

.weather-detail-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.detail-item {
  text-align: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.detail-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.detail-value {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.weather-alert-detail {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
}

.alert-header {
  font-weight: bold;
  color: #856404;
  margin-bottom: 8px;
}

.alert-message {
  color: #856404;
  font-size: 0.9rem;
}

/* 위치 권한 관련 스타일 */
.location-status {
  margin-left: 8px;
  font-size: 0.9rem;
}

.location-status.granted {
  color: #27ae60;
}

.location-status.denied {
  color: #e74c3c;
}

.location-status.loading {
  color: #f39c12;
  animation: pulse 1.5s infinite;
}

.location-request-banner {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: 8px;
  padding: 12px 20px;
  margin-bottom: 15px;
  animation: slideIn 0.5s ease;
}

.location-request-content {
  display: flex;
  align-items: center;
  gap: 12px;
  color: white;
}

.location-icon {
  font-size: 1.2rem;
}

.location-text {
  flex: 1;
  font-size: 0.9rem;
}

.location-request-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: background-color 0.3s ease;
  white-space: nowrap;
}

.location-request-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}



.no-weather-data {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.no-data-text {
  font-size: 1rem;
  margin-bottom: 20px;
}

.retry-btn {
  background: #3498db;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background: #2980b9;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 반응형 디자인 */
@media (max-width: 768px) {
  .floating-history {
    left: 10px;
    transform: translateY(-50%);
  }
  
  .floating-history-content {
    width: 160px;
  }
  
  .floating-history-card {
    padding: 8px;
    min-height: 35px;
  }
  
  .floating-history-icon {
    font-size: 1rem;
  }
  
  .floating-history-title {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .floating-history {
    left: 5px;
  }
  
  .floating-history-content {
    width: 140px;
  }
  
  .floating-history-toggle {
    width: 25px;
    height: 25px;
    font-size: 0.7rem;
    right: -12px;
  }
}
</style> 