<template>
  <div class="easy-search-section">
    <div class="easy-search-header">
      <h3 class="easy-search-title">🌱 어머니, 어떤 작물을 찾으세요?</h3>
      <p class="easy-search-subtitle">원하시는 작물을 클릭하시면 바로 경매 정보를 확인할 수 있어요!</p>
    </div>
    
    <div class="product-buttons" ref="productButtonsContainer">
      <!-- 로딩 중 -->
      <div v-if="productsLoading" class="products-loading">
        <div class="loading-spinner">🔄</div>
        <span>상품 목록을 불러오는 중...</span>
      </div>
      
      <!-- 상품 버튼들 (드래그 앤 드롭 가능) -->
      <div 
        v-else
        v-for="product in majorProducts" 
        :key="product"
        class="product-btn-wrapper"
        :class="{ active: selectedProduct === product }"
      >
        <button 
          @click="selectProduct(product)"
          class="product-btn"
          :class="{ active: selectedProduct === product }"
        >
          <span class="product-text">{{ product }}</span>
          <span class="drag-handle">⋮⋮</span>
        </button>
      </div>
    </div>
    
    <div class="selected-product-info" v-if="selectedProduct">
      <div class="selected-badge">
        <span class="selected-icon">✅</span>
        <span class="selected-text">현재 선택: <strong>{{ selectedProduct }}</strong></span>
        <button @click="clearSelection" class="clear-btn">다른 작물 선택</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick, watch } from 'vue'
import Sortable from 'sortablejs'

const props = defineProps({
  majorProducts: {
    type: Array,
    default: () => []
  },
  productsLoading: {
    type: Boolean,
    default: false
  },
  selectedProduct: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['select-product', 'clear-selection', 'order-changed'])

const productButtonsContainer = ref(null)

// 상품 선택
const selectProduct = (productName) => {
  emit('select-product', productName)
}

// 선택 초기화
const clearSelection = () => {
  emit('clear-selection')
}

// 상품 버튼 드래그 앤 드롭 설정
const setupProductSortable = () => {
  if (!productButtonsContainer.value) {
    console.log('상품 버튼 컨테이너가 아직 준비되지 않았습니다.')
    return
  }
  
  console.log('상품 버튼 드래그 앤 드롭 설정 시작')
  
  Sortable.create(productButtonsContainer.value, {
    animation: 150,
    ghostClass: 'sortable-ghost',
    chosenClass: 'sortable-chosen',
    dragClass: 'sortable-drag',
    handle: '.drag-handle',
    onEnd: (evt) => {
      console.log('상품 순서 변경됨:', evt.oldIndex, '->', evt.newIndex)
      emit('order-changed', { oldIndex: evt.oldIndex, newIndex: evt.newIndex })
    }
  })
  
  console.log('상품 버튼 드래그 앤 드롭 설정 완료')
}

onMounted(async () => {
  await nextTick()
  setupProductSortable()
})

// 상품 목록 변경 시 드래그 앤 드롭 재설정
watch(() => props.majorProducts, () => {
  nextTick(() => {
    setupProductSortable()
  })
}, { deep: true })
</script>

<style scoped>
.easy-search-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.easy-search-header {
  text-align: center;
  margin-bottom: 25px;
}

.easy-search-title {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.easy-search-subtitle {
  color: #7f8c8d;
  margin: 0;
  font-size: 1rem;
}

.product-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
  margin-bottom: 20px;
}

.products-loading {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #7f8c8d;
  font-size: 1rem;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.product-btn-wrapper {
  position: relative;
}

.product-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  color: #495057;
}

.product-btn:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.product-btn.active {
  background: #27ae60;
  border-color: #27ae60;
  color: white;
  box-shadow: 0 4px 12px rgba(39, 174, 96, 0.3);
}

.product-text {
  font-weight: 500;
}

.drag-handle {
  font-size: 0.8rem;
  opacity: 0.6;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.selected-product-info {
  text-align: center;
}

.selected-badge {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 20px;
  padding: 10px 20px;
  color: #155724;
}

.selected-icon {
  font-size: 1.2rem;
}

.clear-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 15px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* 드래그 앤 드롭 스타일 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  transform: rotate(5deg);
}

.sortable-drag {
  transform: rotate(10deg);
}
</style> 