<template>
  <div class="rolling-banner" v-if="auctionData.length > 0 || isLoading">
    <div class="banner-container">
      <div class="banner-icon">📊</div>
      <div class="banner-title">실시간 경매가</div>
      
      <!-- 로딩 상태 -->
      <div v-if="isLoading" class="banner-loading">
        <div class="loading-spinner">🔄</div>
        <span>경매 데이터 로딩 중...</span>
      </div>
      
      <!-- 데이터 없음 상태 -->
      <div v-else-if="auctionData.length === 0" class="banner-empty">
        <span>재배 중인 작물이 없습니다.</span>
        <span class="empty-subtitle">작물을 등록하면 실시간 경매가를 확인할 수 있습니다.</span>
      </div>
      
      <!-- 🚀 Vue 3 Carousel 배너 콘텐츠 -->
      <div v-else class="banner-content">
        <Carousel
          :items-to-show="3"
          :wrap-around="true"
          :autoplay="5000"
          :pause-autoplay-on-hover="true"
          :transition="500"
          snap-align="start"
          class="auction-carousel"
        >
          <Slide v-for="(item, index) in auctionData" :key="`${item.cropId}-${index}`">
            <div class="banner-item">
              <span class="crop-name">{{ item.cropName }}</span>
              <span class="price-info">
                <span class="price-label">최고가:</span>
                <span class="price-value" :class="getPriceColorClass(item.priceColor)">
                  {{ formatPrice(item.maxPrice) }}원
                </span>
                <span class="price-separator">|</span>
                <span class="price-label">최저가:</span>
                <span class="price-value" :class="getPriceColorClass(item.priceColor)">
                  {{ formatPrice(item.minPrice) }}원
                </span>
              </span>
              <span class="auction-date">{{ formatDate(item.auctionDate) }}</span>
            </div>
          </Slide>
          
          <template #addons>
            <Navigation />
            <Pagination />
          </template>
        </Carousel>
      </div>
      
      <div class="banner-controls">
        <button @click="closeBanner" class="control-btn">✕</button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { Carousel, Slide, Navigation, Pagination } from 'vue3-carousel'
import 'vue3-carousel/dist/carousel.css'
import apiClient from '@/utils/axios'
import { useApiError } from '@/composables/useApiError'

// Props
const props = defineProps({
  autoStart: {
    type: Boolean,
    default: true
  },
  interval: {
    type: Number,
    default: 5000 // 5초
  },
  maxItems: {
    type: Number,
    default: 3 // 한 번에 보여줄 아이템 수
  }
})

// Emits
const emit = defineEmits(['close'])

// 🚀 API 에러 처리
const { handleApiError } = useApiError()

// 상태 관리
const auctionData = ref([])
const isLoading = ref(false)
const showEmptyState = ref(false)

// 🚀 API 호출
const loadAuctionData = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  try {
    const response = await apiClient.get('/crops/current-auction-prices')
    
    if (response.data.success && response.data.data && response.data.data.length > 0) {
      auctionData.value = response.data.data
      showEmptyState.value = false
      console.log('롤링 배너 데이터 로드 완료:', response.data.data)
    } else {
      console.warn('롤링 배너 데이터가 없습니다.')
      auctionData.value = []
      showEmptyState.value = true
    }
  } catch (err) {
    console.error('롤링 배너 데이터 로드 실패:', err)
    auctionData.value = []
    showEmptyState.value = true
  } finally {
    isLoading.value = false
  }
}

const closeBanner = () => {
  emit('close')
}

// 🎨 유틸리티 함수들
const formatPrice = (price) => {
  if (!price) return '0'
  return parseInt(price).toLocaleString()
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  
  // YYYYMMDD 형식을 YYYY-MM-DD로 변환
  const year = dateString.substring(0, 4)
  const month = dateString.substring(4, 6)
  const day = dateString.substring(6, 8)
  
  return `${year}-${month}-${day}`
}

const getPriceColorClass = (colorCode) => {
  const colorMap = {
    'red': 'price-red',
    'blue': 'price-blue', 
    'green': 'price-green',
    'orange': 'price-orange',
    'yellow': 'price-yellow',
    'gray': 'price-gray'
  }
  return colorMap[colorCode] || 'price-gray'
}

// 🔄 자동 새로고침
const startAutoRefresh = () => {
  // 5분마다 데이터 새로고침
  setInterval(() => {
    loadAuctionData()
  }, 5 * 60 * 1000) // 5분
}

// 🎯 라이프사이클
onMounted(async () => {
  await loadAuctionData()
  startAutoRefresh()
})
</script>

<style scoped>
.rolling-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 8px 0;
  font-size: 0.9rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  margin: 0 15px;
  border-radius: 0 0 16px 16px;
}

.banner-container {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px; /* 간격 줄이기 */
  padding: 0 30px; /* 패딩 줄이기 */
  box-sizing: border-box;
  position: relative; /* 절대 위치 자식 요소를 위한 상대 위치 */
}

.banner-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.banner-title {
  font-weight: bold;
  font-size: 0.95rem;
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 5px; /* 오른쪽 여백 줄이기 */
}

.banner-loading {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.banner-loading .loading-spinner {
  animation: spin 1s linear infinite;
}

.banner-empty {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.9rem;
}

.empty-subtitle {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.7);
  font-style: italic;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.banner-content {
  flex: 1;
  position: relative;
  min-width: 0;
  margin-left: 5px; /* 왼쪽 여백 줄이기 */
}

/* 🚀 Vue 3 Carousel 커스터마이징 */
.auction-carousel {
  width: calc(100% - 80px); /* 컨트롤 버튼 공간 줄이기 */
  margin-right: 80px; /* 오른쪽 여백 줄이기 */
  margin-left: 10px; /* 왼쪽 여백 줄이기 */
}

.auction-carousel :deep(.carousel__viewport) {
  overflow: hidden; /* 오버플로우 숨김 */
}

.auction-carousel :deep(.carousel__track) {
  gap: 18px; /* 간격 더 늘리기 */
}

.auction-carousel :deep(.carousel__slide) {
  padding: 0;
  width: 380px !important; /* 고정 너비 더 늘리기 */
  min-width: 380px; /* 최소 너비 */
  max-width: 380px; /* 최대 너비 */
  flex-shrink: 0; /* 줄어들지 않도록 */
}

.auction-carousel :deep(.carousel__prev),
.auction-carousel :deep(.carousel__next) {
  display: none; /* 네비게이션 버튼 숨김 */
}

.auction-carousel :deep(.carousel__pagination) {
  display: none; /* 페이지네이션 숨김 */
}

.banner-item {
  display: flex;
  align-items: center;
  justify-content: center; /* 중앙 정렬 */
  gap: 2px; /* 간격 팍팍 줄이기 */
  padding: 6px 12px; /* 패딩 늘리기 */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  white-space: nowrap;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  flex-shrink: 0;
  min-width: 0;
  height: 40px;
  width: 100%; /* 부모 컨테이너에 맞춤 */
  box-sizing: border-box;
}

.banner-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transition: left 0.8s ease;
}

.banner-item:hover::before {
  left: 100%;
}

.banner-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-1px) scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.crop-name {
  font-weight: bold;
  color: #fff;
  min-width: 50px; /* 너비 늘리기 */
  flex-shrink: 0;
  font-size: 0.95rem; /* 폰트 크기 늘리기 */
  margin-right: 0px; /* 오른쪽 여백 제거 */
}

.price-info {
  display: flex;
  align-items: center;
  gap: 2px; /* 간격 팍팍 줄이기 */
  font-size: 0.85rem; /* 폰트 크기 늘리기 */
  flex-shrink: 0;
}

.price-label {
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.8rem;
  margin-right: 1px; /* 오른쪽 여백 최소화 */
}

.price-value {
  font-weight: bold;
  font-size: 0.9rem;
}

.price-separator {
  color: rgba(255, 255, 255, 0.5);
  margin: 0 2px; /* 간격 팍팍 줄이기 */
}

.auction-date {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.8rem; /* 폰트 크기 늘리기 */
  min-width: 75px; /* 너비 늘리기 */
  flex-shrink: 0;
}

.banner-controls {
  display: flex;
  gap: 5px;
  flex-shrink: 0;
  align-items: center;
  margin-left: auto;
  position: absolute; /* 절대 위치로 변경 */
  right: 35px; /* 오른쪽에서 35px로 늘리기 */
  top: 50%; /* 세로 중앙 정렬 */
  transform: translateY(-50%); /* 세로 중앙 정렬 */
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
  min-width: 32px;
  min-height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* 가격 색상 클래스들 */
.price-red {
  color: #ff6b6b;
}

.price-blue {
  color: #74b9ff;
}

.price-green {
  color: #00b894;
}

.price-orange {
  color: #fdcb6e;
}

.price-yellow {
  color: #ffeaa7;
}

.price-gray {
  color: #b2bec3;
}

/* 반응형 */
@media (max-width: 768px) {
  .banner-container {
    padding: 0 20px; /* 패딩 줄이기 */
    gap: 8px; /* 간격 더 줄이기 */
  }
  
  .banner-title {
    display: none;
  }
  
  .auction-carousel {
    width: calc(100% - 60px); /* 모바일에서도 여백 줄이기 */
    margin-right: 60px;
    margin-left: 5px; /* 왼쪽 여백 줄이기 */
  }
  
  .auction-carousel :deep(.carousel__track) {
    gap: 10px; /* 간격 늘리기 */
  }
  
  .auction-carousel :deep(.carousel__slide) {
    width: 340px !important; /* 모바일 고정 너비 더 늘리기 */
    min-width: 340px;
    max-width: 340px;
  }
  
  .banner-item {
    padding: 4px 10px; /* 패딩 늘리기 */
    gap: 1px; /* 간격 팍팍 줄이기 */
  }
  
  .crop-name {
    min-width: 40px; /* 너비 줄이기 */
    font-size: 0.8rem; /* 폰트 크기 줄이기 */
  }
  
  .price-info {
    font-size: 0.75rem; /* 폰트 크기 줄이기 */
    gap: 1px; /* 간격 팍팍 줄이기 */
  }
  
  .auction-date {
    display: none;
  }
  
  .banner-controls {
    right: 25px; /* 모바일에서도 여백 늘리기 */
  }
}

@media (max-width: 480px) {
  .rolling-banner {
    font-size: 0.8rem;
  }
  
  .banner-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 2px;
    height: auto;
    min-height: 40px;
  }
  
  .price-info {
    flex-direction: column;
    gap: 2px;
  }
  
  .price-separator {
    display: none;
  }
}
</style> 