<template>
  <div class="month-view">
    <div class="calendar-header">
      <div v-for="day in weekDays" :key="day" class="calendar-day-header">
        {{ day }}
      </div>
    </div>
    
    <div class="calendar-grid">
      <div 
        v-for="date in calendarDates" 
        :key="date.key"
        class="calendar-day"
        :class="{ 
          'other-month': !date.isCurrentMonth,
          'today': date.isToday,
          'has-events': date.events.length > 0
        }"
        @click="onDateClick(date)"
      >
        <div class="day-number">{{ date.dayNumber }}</div>
        <div class="day-events">
          <div 
            v-for="event in date.events.slice(0, 3)" 
            :key="event.id"
            class="event-dot"
            :style="{ backgroundColor: event.color }"
            :title="event.title"
            @click.stop="onEventClick(event)"
          ></div>
          <div v-if="date.events.length > 3" class="more-events">
            +{{ date.events.length - 3 }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  calendarDates: {
    type: Array,
    default: () => []
  },
  weekDays: {
    type: Array,
    default: () => ['일', '월', '화', '수', '목', '금', '토']
  }
})

const emit = defineEmits(['date-click', 'event-click'])

const onDateClick = (date) => {
  emit('date-click', date)
}

const onEventClick = (event) => {
  emit('event-click', event)
}
</script>

<style scoped>
.month-view {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.calendar-day-header {
  padding: 15px;
  text-align: center;
  font-weight: bold;
  color: #2c3e50;
  border-right: 1px solid #e0e0e0;
}

.calendar-day-header:last-child {
  border-right: none;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
}

.calendar-day {
  min-height: 120px;
  padding: 10px;
  border-right: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  cursor: pointer;
  transition: background-color 0.3s ease;
  position: relative;
}

.calendar-day:nth-child(7n) {
  border-right: none;
}

.calendar-day:hover {
  background: #f8f9fa;
}

.calendar-day.other-month {
  background: #f8f9fa;
  color: #bdc3c7;
}

.calendar-day.today {
  background: #e3f2fd;
  border: 2px solid #2196f3;
}

.calendar-day.has-events {
  background: #fff3e0;
}

.day-number {
  font-size: 1.1rem;
  font-weight: bold;
  margin-bottom: 8px;
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.event-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.event-dot:hover {
  transform: scale(1.2);
}

.more-events {
  font-size: 0.8rem;
  color: #7f8c8d;
  text-align: center;
  padding: 2px;
}

@media (max-width: 768px) {
  .calendar-day {
    min-height: 80px;
    padding: 5px;
  }
  
  .day-number {
    font-size: 0.9rem;
  }
  
  .event-dot {
    width: 6px;
    height: 6px;
  }
}
</style> 