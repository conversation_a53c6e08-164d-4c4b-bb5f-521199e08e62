// 가격 포맷팅
export const formatPrice = (price) => {
  if (!price || price === '0' || price === 0) return '-'
  // 문자열인 경우 숫자로 변환
  const numPrice = typeof price === 'string' ? parseInt(price, 10) : price
  if (isNaN(numPrice) || numPrice === 0) return '-'
  return new Intl.NumberFormat('ko-KR').format(numPrice) + '원'
}

// 날짜 포맷팅 (YYYYMMDD -> YYYY-MM-DD)
export const formatDate = (dateString) => {
  if (!dateString) return ''
  // YYYYMMDD 형식을 YYYY-MM-DD로 변환
  if (dateString.length === 8) {
    return `${dateString.substring(0, 4)}-${dateString.substring(4, 6)}-${dateString.substring(6, 8)}`
  }
  // 이미 YYYY-MM-DD 형식인 경우
  return dateString
}

// 표시용 날짜 포맷팅
export const formatDisplayDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('ko-KR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// 색상 코드 유틸리티 함수
export const getColorCode = (colorCode) => {
  const colors = {
    'red': '#d32f2f',
    'blue': '#1976d2', 
    'green': '#388e3c',
    'orange': '#f57c00',
    'yellow': '#fbc02d',
    'gray': '#757575'
  }
  return colors[colorCode] || '#757575'
}

// 트렌드 클래스 반환
export const getTrendClass = (trend) => {
  if (trend === '상승') return 'trend-up'
  if (trend === '하락') return 'trend-down'
  return 'trend-stable'
}

// 가격 변화 클래스 반환
export const getPriceChangeClass = (change) => {
  if (change > 0) return 'change-up'
  if (change < 0) return 'change-down'
  return 'change-stable'
}

// 변동성 클래스 반환
export const getVolatilityClass = (volatility) => {
  if (volatility >= 30) return 'volatility-high'
  if (volatility >= 15) return 'volatility-medium'
  return 'volatility-low'
}

// 카드 색상 클래스 반환
export const getCardColorClass = (item) => {
  const price = parseInt(item.AVG_PRC || 0)
  if (price > 5000) return 'high-price'
  if (price > 3000) return 'medium-price'
  if (price > 1000) return 'low-price'
  return 'very-low-price'
}

// 아이템 색상 반환
export const getItemColor = (item) => {
  const price = parseInt(item.AVG_PRC || 0)
  if (price > 5000) return '#d32f2f'
  if (price > 3000) return '#f57c00'
  if (price > 1000) return '#388e3c'
  return '#757575'
}

// 변동 클래스 반환
export const getVariationClass = (variation) => {
  if (variation > 0) return 'variation-up'
  if (variation < 0) return 'variation-down'
  return 'variation-stable'
}

/**
 * snake_case 문자열을 camelCase로 변환합니다.
 * @param {string} str - 변환할 문자열
 * @returns {string} camelCase로 변환된 문자열
 */
const toCamelCase = (str) => {
  if (!str) return '';
  return str.toLowerCase().replace(/_([a-z])/g, (g) => g[1].toUpperCase());
};

/**
 * 객체의 모든 키를 camelCase로 변환합니다. (재귀적으로 동작)
 * @param {object | object[]} data - 변환할 객체 또는 객체 배열
 * @returns {object | object[]} 키가 camelCase로 변환된 객체 또는 객체 배열
 */
export const convertKeysToCamelCase = (data) => {
  if (Array.isArray(data)) {
    return data.map((item) => convertKeysToCamelCase(item));
  }
  if (data !== null && typeof data === 'object') {
    return Object.keys(data).reduce((acc, key) => {
      const camelKey = toCamelCase(key);
      acc[camelKey] = data[key]; // 값에 대한 재귀 호출 제거
      return acc;
    }, {});
  }
  return data;
}; 