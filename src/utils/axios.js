import axios from 'axios'

// 🚀 API 설정
const API_BASE_URL = 'http://localhost:8080/api'

// 📊 API별 타임아웃 설정
const API_TIMEOUTS = {
  default: 30000,    // 30초
  bulkData: 300000,  // 5분
  streaming: 1800000 // 30분
}

// 📡 기본 Axios 인스턴스 생성
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUTS.default,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 📡 대량 데이터용 Axios 인스턴스 생성
const bulkDataClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUTS.bulkData,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 📡 스트리밍용 Axios 인스턴스 생성
const streamingClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: API_TIMEOUTS.streaming,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 🔄 재시도 로직
const retryRequest = async (requestFn, maxRetries = 3, delay = 1000) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await requestFn()
    } catch (error) {
      if (i === maxRetries - 1) {
        throw error
      }
      
      console.log(`🔄 재시도 ${i + 1}/${maxRetries} - ${error.message}`)
      await new Promise(resolve => setTimeout(resolve, delay * (i + 1)))
    }
  }
}

// 🔍 요청 인터셉터 - 로깅 및 공통 헤더
const addRequestInterceptor = (instance, name) => {
  instance.interceptors.request.use(
    config => {
      console.log(`🚀 ${name} API 요청: ${config.method?.toUpperCase()} ${config.url}`)
      if (config.data) {
        console.log('📤 요청 데이터:', config.data)
      }
      return config
    },
    error => {
      console.error(`❌ ${name} 요청 인터셉터 오류:`, error)
      return Promise.reject(error)
    }
  )
}

addRequestInterceptor(apiClient, '기본')
addRequestInterceptor(bulkDataClient, '대량데이터')
addRequestInterceptor(streamingClient, '스트리밍')

// 🔍 응답 인터셉터 - 공통 에러 처리
apiClient.interceptors.response.use(
  response => {
    console.log(`✅ API 응답: ${response.config.method?.toUpperCase()} ${response.config.url}`)
    if (response.data) {
      console.log('📥 응답 데이터:', response.data)
      
      // 통합 API 가이드에 따른 ApiResponse<T> 형식 검증
      if (response.data.hasOwnProperty('success')) {
        if (!response.data.success) {
          // ApiResponse 형식의 오류 응답
          const error = new Error(response.data.message || 'API 호출 실패')
          error.response = {
            data: response.data,
            status: response.status,
            statusText: response.statusText
          }
          error.isApiResponseError = true
          return Promise.reject(error)
        }
      }
    }
    return response
  },
  error => {
    console.error('❌ API 오류:', error)
    
    // 🚨 상세한 에러 분석
    const errorInfo = {
      url: error.config?.url,
      method: error.config?.method,
      status: error.response?.status,
      statusText: error.response?.statusText,
      message: error.message,
      responseData: error.response?.data
    }
    
    console.error('🔍 에러 상세 정보:', errorInfo)
    
    // 🎯 에러 타입별 처리
    let processedError = {
      type: 'UNKNOWN_ERROR',
      message: '알 수 없는 오류가 발생했습니다.',
      details: errorInfo,
      retryable: false
    }
    
    if (error.response) {
      // 서버 응답이 있는 경우
      const status = error.response.status
      const responseData = error.response.data
      
      // ApiResponse 형식의 오류 처리
      if (responseData && responseData.hasOwnProperty('success') && !responseData.success) {
        processedError = {
          type: 'API_RESPONSE_ERROR',
          message: responseData.message || 'API 호출 실패',
          details: { ...errorInfo, apiError: responseData.error },
          retryable: false
        }
      } else {
        switch (status) {
          case 400:
            processedError = {
              type: 'BAD_REQUEST',
              message: '잘못된 요청입니다. 입력 데이터를 확인해주세요.',
              details: { ...errorInfo, validationErrors: responseData },
              retryable: false
            }
            break
            
          case 401:
            processedError = {
              type: 'UNAUTHORIZED',
              message: '인증이 필요합니다. 로그인해주세요.',
              details: errorInfo,
              retryable: false
            }
            break
            
          case 403:
            processedError = {
              type: 'FORBIDDEN',
              message: '접근 권한이 없습니다.',
              details: errorInfo,
              retryable: false
            }
            break
            
          case 404:
            processedError = {
              type: 'NOT_FOUND',
              message: '요청한 리소스를 찾을 수 없습니다.',
              details: errorInfo,
              retryable: false
            }
            break
            
          case 500:
            const errorMessage = responseData?.message || responseData?.error || '서버 내부 오류'
            
            if (errorMessage.includes('LazyInitializationException') || 
                errorMessage.includes('greenhouses') ||
                errorMessage.includes('Session')) {
              processedError = {
                type: 'LAZY_LOADING_ERROR',
                message: '데이터베이스 세션 관리 문제가 발생했습니다. 잠시 후 다시 시도해주세요.',
                details: { ...errorInfo, originalError: errorMessage },
                retryable: true
              }
            } else if (errorMessage.includes('Connection') || 
                       errorMessage.includes('Database')) {
              processedError = {
                type: 'DATABASE_ERROR',
                message: '데이터베이스 연결 오류가 발생했습니다. 잠시 후 다시 시도해주세요.',
                details: { ...errorInfo, originalError: errorMessage },
                retryable: true
              }
            } else {
              processedError = {
                type: 'SERVER_ERROR',
                message: `서버 내부 오류: ${errorMessage}`,
                details: { ...errorInfo, originalError: errorMessage },
                retryable: true
              }
            }
            break
            
          case 502:
          case 503:
          case 504:
            processedError = {
              type: 'SERVICE_UNAVAILABLE',
              message: '서비스가 일시적으로 사용할 수 없습니다. 잠시 후 다시 시도해주세요.',
              details: errorInfo,
              retryable: true
            }
            break
            
          default:
            processedError = {
              type: 'HTTP_ERROR',
              message: `HTTP 오류 (${status}): ${error.response.statusText}`,
              details: errorInfo,
              retryable: status >= 500
            }
        }
      }
    } else if (error.request) {
      // 요청은 보냈지만 응답이 없는 경우 (네트워크 오류)
      processedError = {
        type: 'NETWORK_ERROR',
        message: '네트워크 연결을 확인해주세요. 서버가 실행 중인지 확인해주세요.',
        details: { ...errorInfo, networkError: true },
        retryable: true
      }
    } else {
      // 요청 설정 중 오류
      processedError = {
        type: 'REQUEST_ERROR',
        message: `요청 설정 오류: ${error.message}`,
        details: errorInfo,
        retryable: false
      }
    }
    
    // 🎯 타임아웃 특별 처리
    if (error.code === 'ECONNABORTED') {
      processedError = {
        type: 'TIMEOUT_ERROR',
        message: '요청 시간이 초과되었습니다. 네트워크 상태를 확인해주세요.',
        details: { ...errorInfo, timeout: true },
        retryable: true
      }
    }
    
    return Promise.reject(processedError)
  }
)

// 🛠️ 유틸리티 함수들
export const apiUtils = {
  // 재시도 가능한 에러인지 확인
  isRetryable: (error) => error.retryable === true,
  
  // 에러 타입별 아이콘 반환
  getErrorIcon: (errorType) => {
    const icons = {
      'API_RESPONSE_ERROR': '⚠️',
      'LAZY_LOADING_ERROR': '🐛',
      'DATABASE_ERROR': '🗄️',
      'NETWORK_ERROR': '🌐',
      'TIMEOUT_ERROR': '⏰',
      'SERVER_ERROR': '🖥️',
      'BAD_REQUEST': '📝',
      'NOT_FOUND': '🔍',
      'UNAUTHORIZED': '🔐',
      'FORBIDDEN': '🚫',
      'SERVICE_UNAVAILABLE': '🔧',
      'UNKNOWN_ERROR': '❓'
    }
    return icons[errorType] || '❓'
  },
  
  // 에러 타입별 색상 반환
  getErrorColor: (errorType) => {
    const colors = {
      'API_RESPONSE_ERROR': '#e74c3c',
      'LAZY_LOADING_ERROR': '#e74c3c',
      'DATABASE_ERROR': '#e67e22',
      'NETWORK_ERROR': '#3498db',
      'TIMEOUT_ERROR': '#f39c12',
      'SERVER_ERROR': '#e74c3c',
      'BAD_REQUEST': '#e74c3c',
      'NOT_FOUND': '#95a5a6',
      'UNAUTHORIZED': '#e74c3c',
      'FORBIDDEN': '#e74c3c',
      'SERVICE_UNAVAILABLE': '#f39c12',
      'UNKNOWN_ERROR': '#95a5a6'
    }
    return colors[errorType] || '#95a5a6'
  }
}

export default apiClient
export { bulkDataClient, streamingClient, retryRequest } 