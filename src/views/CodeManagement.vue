<template>
  <Layout ref="layoutRef">
    <div class="code-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1 class="page-title">⚙️ 코드 관리</h1>
        <p class="page-subtitle">시스템 코드 및 설정을 관리합니다</p>
      </div>

      <!-- 코드 카테고리 탭 -->
      <div class="code-tabs">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          class="tab-btn"
          :class="{ 'active': activeTab === tab.id }"
          @click="activeTab = tab.id"
        >
          {{ tab.icon }} {{ tab.name }}
        </button>
      </div>

      <!-- 코드 목록 -->
      <div class="code-content">
        <div class="code-header">
          <h2 class="section-title">{{ getActiveTabName() }}</h2>
          <button class="add-btn" @click="showAddModal = true">
            <span class="add-icon">➕</span>
            새 코드 추가
          </button>
        </div>

        <!-- 로딩 상태 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>코드 목록을 불러오는 중...</p>
        </div>

        <!-- 에러 상태 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ error }}</p>
          <button @click="loadCodes" class="retry-btn">다시 시도</button>
        </div>

        <!-- 코드 테이블 -->
        <div v-else class="code-table">
          <div class="table-header">
            <div class="header-cell">코드</div>
            <div class="header-cell">이름</div>
            <div class="header-cell">설명</div>
            <div class="header-cell">사용여부</div>
            <div class="header-cell">관리</div>
          </div>
          
          <div 
            v-for="code in filteredCodes" 
            :key="code.id" 
            class="table-row"
          >
            <div class="cell">{{ code.code }}</div>
            <div class="cell">{{ code.name }}</div>
            <div class="cell">{{ code.description }}</div>
            <div class="cell">
              <span class="status-badge" :class="code.active ? 'active' : 'inactive'">
                {{ code.active ? '사용' : '미사용' }}
              </span>
            </div>
            <div class="cell">
              <div class="action-buttons">
                <button class="action-btn edit" @click="editCode(code)">
                  ✏️
                </button>
                <button class="action-btn delete" @click="deleteCode(code.id)">
                  🗑️
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 검색 및 필터 -->
        <div class="code-filters">
          <div class="search-box">
            <input 
              v-model="searchTerm" 
              type="text" 
              placeholder="코드 또는 이름으로 검색..."
              class="search-input"
            >
            <span class="search-icon">🔍</span>
          </div>
          
          <div class="filter-options">
            <select v-model="statusFilter" class="filter-select">
              <option value="">전체</option>
              <option value="true">사용중</option>
              <option value="false">미사용</option>
            </select>
          </div>
        </div>
      </div>

      <!-- 코드 추가/수정 모달 -->
      <div v-if="showAddModal" class="modal-overlay" @click="showAddModal = false">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>{{ editingCode ? '코드 수정' : '새 코드 추가' }}</h3>
            <button class="close-btn" @click="showAddModal = false">✕</button>
          </div>
          
          <div class="modal-body">
            <div class="form-group">
              <label>코드</label>
              <input v-model="codeForm.code" type="text" placeholder="코드를 입력하세요" class="form-input">
            </div>
            
            <div class="form-group">
              <label>이름</label>
              <input v-model="codeForm.name" type="text" placeholder="이름을 입력하세요" class="form-input">
            </div>
            
            <div class="form-group">
              <label>설명</label>
              <textarea v-model="codeForm.description" placeholder="설명을 입력하세요" class="form-textarea"></textarea>
            </div>
            
            <div class="form-group">
              <label class="checkbox-label">
                <input v-model="codeForm.active" type="checkbox">
                사용 여부
              </label>
            </div>
          </div>
          
          <div class="modal-footer">
            <button class="btn-cancel" @click="showAddModal = false">취소</button>
            <button class="btn-save" @click="saveCode">저장</button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import Layout from '../components/Layout.vue'

const layoutRef = ref(null)
const activeTab = ref('crop')
const searchTerm = ref('')
const statusFilter = ref('')
const showAddModal = ref(false)
const editingCode = ref(null)

// 탭 데이터
const tabs = ref([
  { id: 'crop', name: '작물 코드', icon: '🌾' },
  { id: 'equipment', name: '장비 코드', icon: '🛠️' },
  { id: 'status', name: '상태 코드', icon: '📊' },
  { id: 'unit', name: '단위 코드', icon: '📏' },
  { id: 'system', name: '시스템 코드', icon: '⚙️' }
])

// 코드 데이터
const codes = ref([])
const loading = ref(false)
const error = ref('')

// 폼 데이터
const codeForm = ref({
  code: '',
  name: '',
  description: '',
  active: true
})

// 활성 탭 이름 반환
const getActiveTabName = () => {
  const tab = tabs.value.find(t => t.id === activeTab.value)
  return tab ? tab.name : ''
}

// 필터링된 코드 목록
const filteredCodes = computed(() => {
  let filtered = codes.value.filter(code => code.category === activeTab.value)
  
  if (searchTerm.value) {
    filtered = filtered.filter(code => 
      code.code.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
      code.name.toLowerCase().includes(searchTerm.value.toLowerCase())
    )
  }
  
  if (statusFilter.value !== '') {
    filtered = filtered.filter(code => code.active.toString() === statusFilter.value)
  }
  
  return filtered
})

// API 호출 함수들
const loadCodes = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await fetch('http://localhost:8080/api/codes')
    if (response.ok) {
      const data = await response.json()
      if (data.success && Array.isArray(data.data)) {
        codes.value = data.data
        console.log('코드 목록 로드 완료:', codes.value.length, '개')
      } else {
        console.warn('코드 API 응답 형식 오류:', data)
        codes.value = []
      }
    } else {
      console.error('코드 API 호출 실패:', response.status)
      codes.value = []
    }
  } catch (err) {
    console.error('코드 API 호출 오류:', err)
    error.value = '코드 목록을 불러오는데 실패했습니다.'
    codes.value = []
  } finally {
    loading.value = false
  }
}

// 코드 편집
const editCode = (code) => {
  editingCode.value = code
  codeForm.value = {
    code: code.code,
    name: code.name,
    description: code.description,
    active: code.active
  }
  showAddModal.value = true
}

// 코드 삭제
const deleteCode = (id) => {
  if (confirm('정말로 이 코드를 삭제하시겠습니까?')) {
    codes.value = codes.value.filter(code => code.id !== id)
  }
}

// 코드 저장
const saveCode = () => {
  if (!codeForm.value.code || !codeForm.value.name) {
    alert('코드와 이름을 입력해주세요.')
    return
  }
  
  if (editingCode.value) {
    // 수정
    const index = codes.value.findIndex(code => code.id === editingCode.value.id)
    if (index !== -1) {
      codes.value[index] = {
        ...editingCode.value,
        ...codeForm.value
      }
    }
  } else {
    // 추가
    const newCode = {
      id: Date.now(),
      category: activeTab.value,
      ...codeForm.value
    }
    codes.value.push(newCode)
  }
  
  showAddModal.value = false
  editingCode.value = null
  codeForm.value = {
    code: '',
    name: '',
    description: '',
    active: true
  }
}

// 컴포넌트 마운트
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/code')
  }
  
  loadCodes()
})
</script>

<style scoped>
.code-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-title {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.code-tabs {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.tab-btn {
  padding: 12px 20px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  background: #f8f9fa;
  border-color: #27ae60;
}

.tab-btn.active {
  background: #27ae60;
  color: white;
  border-color: #27ae60;
}

.code-content {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.code-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.code-table {
  margin-bottom: 25px;
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 2fr 1fr 1fr;
  gap: 15px;
  padding: 15px;
  border-bottom: 1px solid #e0e0e0;
  align-items: center;
}

.table-row:hover {
  background: #f8f9fa;
}

.cell {
  color: #2c3e50;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: bold;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.action-btn.edit {
  background: #3498db;
  color: white;
}

.action-btn.delete {
  background: #e74c3c;
  color: white;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.code-filters {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.filter-select {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 120px;
  cursor: pointer;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.modal-footer {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  padding: 20px 25px;
  border-top: 1px solid #e0e0e0;
}

.btn-cancel,
.btn-save {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: #95a5a6;
  color: white;
}

.btn-save {
  background: #27ae60;
  color: white;
}

.btn-cancel:hover,
.btn-save:hover {
  transform: translateY(-2px);
}

.error-container {
  text-align: center;
  padding: 25px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.error-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
  opacity: 0.7;
}

.error-text {
  color: #856404;
  font-size: 0.95rem;
  margin-bottom: 0;
  flex: 1;
  text-align: center;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e67e22;
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .header-cell {
    display: none;
  }
  
  .cell {
    padding: 5px 0;
  }
  
  .code-filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style> 