<template>
  <Layout>
    <div class="activity-logs-container">
      <!-- 페이지 헤더 -->
      <div class="main-title">
        <h1>📊 활동 내역</h1>
        <p class="subtitle">모든 활동 로그를 조회하고 검색할 수 있습니다</p>
      </div>

      <!-- 검색 및 필터 -->
      <div class="search-filter-section">
        <div class="search-box">
          <input 
            v-model="searchQuery" 
            type="text" 
            placeholder="로그 내용을 검색하세요..."
            class="search-input"
            @input="handleSearch"
          />
          <span class="search-icon">🔍</span>
        </div>
        
        <div class="filter-options">
          <select v-model="selectedCategory" @change="handleFilter" class="category-filter">
            <option value="">전체 카테고리</option>
            <option value="HARVEST">수확</option>
            <option value="PRODUCT">상품</option>
            <option value="CROP">작물</option>
            <option value="AUTH">인증</option>
            <option value="ERROR">오류</option>
            <option value="INFO">정보</option>
          </select>
          
          <select v-model="selectedLevel" @change="handleFilter" class="level-filter">
            <option value="">전체 레벨</option>
            <option v-for="level in availableLevels" :key="level" :value="level">{{ getLevelDisplayName(level) }}</option>
          </select>
        </div>
      </div>

      <!-- 로그 목록 -->
      <div class="logs-section">
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">⏳</div>
          <p>로그를 불러오는 중...</p>
        </div>
        
        <div v-else-if="filteredLogs.length === 0" class="empty-container">
          <div class="empty-icon">📋</div>
          <p>검색 결과가 없습니다.</p>
          <button @click="resetFilters" class="reset-btn">필터 초기화</button>
        </div>
        
        <div v-else class="logs-list scroll-container">
          <div v-for="log in paginatedLogs" :key="log.id" class="log-item" @click="openModal(log)">
            <div class="log-icon">
              {{ getActivityIcon(log.category || log.type || log.level) }}
            </div>
            <div class="log-content">
              <div class="log-header">
                <div class="log-title">{{ log.errorMessage || log.action || getActivityTitle(log.category) }}</div>
                <div class="log-meta">
                  <span class="log-time">{{ formatTimeAgo(log.timestamp || log.createdAt || log.regDate) }}</span>
                  <span class="log-category">{{ log.category }}</span>
                </div>
              </div>
              <div v-if="log.details" class="log-details">
                {{ log.details }}
              </div>
            </div>
            <div class="log-click-hint">클릭하여 상세보기</div>
          </div>
        </div>

        <!-- 페이징 -->
        <div v-if="totalPages > 1" class="pagination">
          <button 
            @click="changePage(currentPage - 1)" 
            :disabled="currentPage === 0"
            class="page-btn"
          >
            ← 이전
          </button>
          
          <div class="page-numbers">
            <button 
              v-for="page in visiblePages" 
              :key="page"
              @click="changePage(page - 1)"
              :class="['page-btn', { active: currentPage === page - 1 }]"
            >
              {{ page }}
            </button>
          </div>
          
          <button 
            @click="changePage(currentPage + 1)" 
            :disabled="currentPage === totalPages - 1"
            class="page-btn"
          >
            다음 →
          </button>
        </div>
      </div>

      <!-- 상세내역 모달 -->
      <div v-if="showModal" class="modal-overlay" @click="closeModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3 class="modal-title">📋 로그 상세내역</h3>
            <button @click="closeModal" class="modal-close">×</button>
          </div>
          <div class="modal-body">
            <div class="detail-item">
              <label>액션:</label>
              <div class="detail-value">{{ selectedLog?.action || '없음' }}</div>
            </div>
            <div v-if="selectedLog?.errorMessage" class="detail-item">
              <label>오류 메시지:</label>
              <div class="detail-value details-text">{{ selectedLog.errorMessage }}</div>
            </div>
            <div class="detail-item">
              <label>카테고리:</label>
              <div class="detail-value">
                <span class="category-badge">{{ selectedLog?.category || '기본' }}</span>
              </div>
            </div>
            <div class="detail-item">
              <label>시간:</label>
              <div class="detail-value">{{ formatFullTime(selectedLog?.createdAt) }}</div>
            </div>
            <div class="detail-item">
              <label>로그 ID:</label>
              <div class="detail-value">{{ selectedLog?.id || 'N/A' }}</div>
            </div>
            <div v-if="selectedLog?.requestUrl" class="detail-item">
              <label>요청 URL:</label>
              <div class="detail-value">{{ selectedLog.requestUrl }}</div>
            </div>
            <div v-if="selectedLog?.requestMethod" class="detail-item">
              <label>요청 메서드:</label>
              <div class="detail-value">{{ selectedLog.requestMethod }}</div>
            </div>
            <div v-if="selectedLog?.responseStatus" class="detail-item">
              <label>응답 상태:</label>
              <div class="detail-value">{{ selectedLog.responseStatus }}</div>
            </div>
            <div v-if="selectedLog?.executionTimeMs" class="detail-item">
              <label>실행 시간:</label>
              <div class="detail-value">{{ selectedLog.executionTimeMs }}ms</div>
            </div>
            <div class="detail-item">
              <label>사용자:</label>
              <div class="detail-value">{{ selectedLog?.userId || '시스템' }}</div>
            </div>
            <div class="detail-item">
              <label>IP 주소:</label>
              <div class="detail-value">{{ selectedLog?.ipAddress || '없음' }}</div>
            </div>
          </div>
          <div class="modal-footer">
            <button @click="closeModal" class="modal-btn">닫기</button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import Layout from '../components/Layout.vue'

// 상태 관리
const logs = ref([])
const loading = ref(false)
const searchQuery = ref('')
const selectedCategory = ref('')
const selectedLevel = ref('')
const currentPage = ref(0)
const pageSize = ref(20)
const totalElements = ref(0)
const showModal = ref(false)
const selectedLog = ref(null)

// 계산된 속성
const filteredLogs = computed(() => {
  let filtered = logs.value

  // 검색 필터
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(log => 
      (log.errorMessage || log.action || log.category || '').toLowerCase().includes(query)
    )
  }

  // 카테고리 필터
  if (selectedCategory.value) {
    filtered = filtered.filter(log => 
      log.category?.toUpperCase() === selectedCategory.value
    )
  }

  // 레벨 필터 (응답 상태 기반)
  if (selectedLevel.value) {
    filtered = filtered.filter(log => {
      if (selectedLevel.value === 'ERROR') {
        return log.responseStatus >= 400
      } else if (selectedLevel.value === 'WARN') {
        return log.responseStatus >= 300 && log.responseStatus < 400
      } else if (selectedLevel.value === 'INFO') {
        return log.responseStatus >= 200 && log.responseStatus < 300
      }
      return true
    })
  }

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredLogs.value.length / pageSize.value))

const paginatedLogs = computed(() => {
  const start = currentPage.value * pageSize.value
  const end = start + pageSize.value
  return filteredLogs.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const maxVisible = 5
  let start = Math.max(0, currentPage.value - Math.floor(maxVisible / 2))
  let end = Math.min(totalPages.value, start + maxVisible)
  
  if (end - start < maxVisible) {
    start = Math.max(0, end - maxVisible)
  }
  
  for (let i = start + 1; i <= end; i++) {
    pages.push(i)
  }
  
  return pages
})

const availableLevels = computed(() => {
  return ['INFO', 'WARN', 'ERROR']
})

const fetchLogs = async () => {
  loading.value = true
  
  try {
    const response = await fetch(`http://localhost:8080/api/activity-logs?page=${currentPage.value}&size=${pageSize.value}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('활동 로그 API 응답:', data)
      
      if (data.success && data.data && data.data.content && Array.isArray(data.data.content)) {
        logs.value = data.data.content
        totalElements.value = data.data.totalElements || 0
        console.log('활동 로그 로드 완료:', logs.value.length, '개')
      } else {
        console.warn('활동 로그 API 응답 형식 오류:', data)
        logs.value = []
        totalElements.value = 0
      }
    } else {
      console.error('활동 로그 API 호출 실패:', response.status)
      logs.value = []
      totalElements.value = 0
    }
  } catch (error) {
    console.error('활동 로그 API 호출 오류:', error)
    logs.value = []
    totalElements.value = 0
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  currentPage.value = 0
}

const handleFilter = () => {
  currentPage.value = 0
}

const resetFilters = () => {
  searchQuery.value = ''
  selectedCategory.value = ''
  selectedLevel.value = ''
  currentPage.value = 0
}

const changePage = (page) => {
  if (page >= 0 && page < totalPages.value) {
    currentPage.value = page
  }
}

const formatTimeAgo = (timestamp) => {
  if (!timestamp) return '시간 정보 없음'
  
  const now = new Date()
  const logTime = new Date(timestamp)
  const diffMs = now - logTime
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 1) return '방금 전'
  if (diffMinutes < 60) return `${diffMinutes}분 전`
  if (diffHours < 24) return `${diffHours}시간 전`
  if (diffDays < 7) return `${diffDays}일 전`
  
  return logTime.toLocaleDateString('ko-KR')
}

const getActivityIcon = (type) => {
  const iconMap = {
    'HARVEST': '🌾',
    'PRODUCT': '📦',
    'CROP': '🌱',
    'AUTH': '🔐',
    'ERROR': '❌',
    'WARN': '⚠️',
    'INFO': 'ℹ️',
    'default': '📋'
  }
  return iconMap[type?.toUpperCase()] || iconMap.default
}

const getActivityTitle = (type) => {
  const titleMap = {
    'AUTH': '인증 활동',
    'CROP': '작물 관리',
    'PRODUCT': '상품 관리',
    'HARVEST': '수확 활동',
    'ERROR': '오류 메시지',
    'WARN': '경고 메시지',
    'INFO': '정보 메시지',
    'harvest': '수확 활동',
    'planting': '재배 활동',
    'shipment': '출하 활동',
    'equipment': '장비 관리',
    'salary': '급여 관리',
    'auction': '경매 활동',
    'weather': '날씨 정보',
    'alert': '알림 확인',
    'maintenance': '정비 활동',
    'default': '시스템 활동'
  }
  return titleMap[type?.toUpperCase()] || titleMap.default
}

const openModal = (log) => {
  selectedLog.value = log
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedLog.value = null
}

const formatFullTime = (timestamp) => {
  if (!timestamp) return '시간 정보 없음'
  
  const logTime = new Date(timestamp)
  return logTime.toLocaleString('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getLevelDisplayName = (level) => {
  const levelMap = {
    'INFO': '정보 메시지',
    'WARN': '경고 메시지',
    'WARNING': '경고 메시지',
    'ERROR': '오류 메시지',
    'FATAL': '치명적 오류',
    'DEBUG': '디버그',
    'TRACE': '추적'
  }
  return levelMap[level] || level
}

onMounted(() => {
  fetchLogs()
})
</script>

<style scoped>
.activity-logs-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
}

.main-title h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.search-filter-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-box {
  position: relative;
  margin-bottom: 20px;
}

.search-input {
  width: 100%;
  padding: 12px 45px 12px 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.filter-options {
  display: flex;
  gap: 15px;
}

.category-filter,
.level-filter {
  flex: 1;
  padding: 10px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
}

.category-filter:focus,
.level-filter:focus {
  outline: none;
  border-color: #667eea;
}

.logs-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-container,
.empty-container {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-container p {
  color: #2c3e50;
  font-size: 1.2rem;
  margin: 10px 0;
}

.loading-spinner {
  font-size: 3rem;
  margin-bottom: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.reset-btn {
  background: #3498db;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 15px;
  transition: background-color 0.3s ease;
}

.reset-btn:hover {
  background: #2980b9;
}

.logs-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
  max-height: 60vh;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
  border-left: 4px solid #667eea;
}

.log-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
  cursor: pointer;
}

.log-click-hint {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-top: 8px;
  opacity: 0.7;
}

.log-icon {
  font-size: 2rem;
  width: 50px;
  text-align: center;
  flex-shrink: 0;
}

.log-content {
  flex: 1;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  gap: 15px;
}

.log-title {
  font-weight: 600;
  color: #2c3e50;
  font-size: 1.1rem;
  flex: 1;
}

.log-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 5px;
  flex-shrink: 0;
}

.log-time {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.log-category {
  background: #667eea;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.log-details {
  color: #7f8c8d;
  font-size: 0.9rem;
  line-height: 1.4;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e0e0e0;
}

.page-btn {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  color: #2c3e50;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  min-width: 40px;
}

.page-btn:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #667eea;
}

.page-btn.active {
  background: #667eea;
  color: white;
  border-color: #667eea;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 5px;
}

@media (max-width: 768px) {
  .filter-options {
    flex-direction: column;
  }
  
  .log-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .log-meta {
    align-items: flex-start;
  }
  
  .pagination {
    flex-wrap: wrap;
  }
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

.modal-body {
  padding: 25px;
}

.detail-item {
  margin-bottom: 20px;
}

.detail-item label {
  display: block;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.detail-value {
  color: #34495e;
  font-size: 1rem;
  line-height: 1.5;
  word-break: break-word;
}

.category-badge {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.details-text {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid #667eea;
  font-family: 'Courier New', monospace;
  white-space: pre-wrap;
}

.modal-footer {
  padding: 20px 25px;
  border-top: 1px solid #e0e0e0;
  text-align: right;
}

.modal-btn {
  background: #667eea;
  border: none;
  color: white;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.modal-btn:hover {
  background: #5a6fd8;
}
</style> 