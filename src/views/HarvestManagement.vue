<template>
  <Layout ref="layoutRef">
    <div class="harvest-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>📦 출하량 관리</h1>
        <p class="page-subtitle">일별 출하량과 경매 정보를 관리합니다</p>
      </div>

      <!-- 시스템 상태 및 통계 -->
      <div class="system-section">
        <div class="system-status">
          <h3>📊 출하 현황</h3>
          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon">📦</div>
              <div class="status-info">
                <div class="status-label">총 출하량</div>
                <div class="status-value">{{ statistics?.totalQuantity || 0 }}BOX</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">💰</div>
              <div class="status-info">
                <div class="status-label">총 매출</div>
                <div class="status-value">{{ formatCurrency(statistics?.totalRevenue || 0) }}</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📅</div>
              <div class="status-info">
                <div class="status-label">오늘 출하</div>
                <div class="status-value">{{ todayHarvests.length }}건</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 출하량 관리 섹션 -->
      <div class="harvest-section">
        <div class="section-header">
          <h3>📋 출하 기록</h3>
          <button @click="openAddModal" class="add-btn">
            <span>➕</span>
            새 출하 기록
          </button>
        </div>

        <!-- 필터 및 검색 -->
        <div class="filters">
          <div class="search-box">
            <input 
              v-model="searchKeyword" 
              type="text" 
              placeholder="작물명 또는 시장 검색..."
              class="search-input"
            >
            <span class="search-icon">🔍</span>
          </div>
          <select v-model="selectedGrade" class="grade-select">
            <option value="">전체 등급</option>
            <option value="특상">특상</option>
            <option value="상">상</option>
            <option value="중">중</option>
            <option value="하">하</option>
          </select>
          <select v-model="selectedMarket" class="market-select">
            <option value="">전체 시장</option>
            <option value="가락동">가락동</option>
            <option value="부산">부산</option>
            <option value="대구">대구</option>
            <option value="광주">광주</option>
            <option value="대전">대전</option>
          </select>
          <div class="date-filters">
            <button @click="filterByDate('today')" class="date-btn">📅 오늘</button>
            <button @click="filterByDate('week')" class="date-btn">📅 이번주</button>
            <button @click="filterByDate('month')" class="date-btn">📅 이번달</button>
          </div>
        </div>

        <!-- 로딩 상태 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>출하 기록을 불러오는 중...</p>
        </div>

        <!-- 에러 상태 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ error }}</p>
          <button @click="loadHarvests" class="retry-btn">다시 시도</button>
        </div>

        <!-- 출하 기록 목록 -->
        <div v-else class="harvest-list">
          <div 
            v-for="harvest in filteredHarvests" 
            :key="harvest.id"
            class="harvest-card"
            :class="{ inactive: !harvest.isActive }"
          >
            <div class="harvest-header">
              <div class="harvest-crop">
                <span class="crop-name">{{ harvest.crop?.cropName }}</span>
                <span class="greenhouse-number">{{ harvest.greenhouse?.greenhouseNumber }}</span>
              </div>
              <div class="harvest-date">{{ formatDate(harvest.harvestDate) }}</div>
            </div>
            <div class="harvest-details">
              <div class="detail-row">
                <div class="detail-item">
                  <span class="detail-label">수량:</span>
                  <span class="detail-value">{{ harvest.quantity }}BOX</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">단가:</span>
                  <span class="detail-value">{{ formatCurrency(harvest.unitPrice) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">총액:</span>
                  <span class="detail-value total-price">{{ formatCurrency(harvest.totalPrice) }}</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="detail-label">경매가:</span>
                  <span class="detail-value">{{ formatCurrency(harvest.auctionPrice) }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">등급:</span>
                  <span class="detail-value grade" :class="getGradeClass(harvest.grade)">{{ harvest.grade || '미정' }}</span>
                </div>
                <div class="detail-item">
                  <span class="detail-label">크기:</span>
                  <span class="detail-value">{{ harvest.size || '미정' }}</span>
                </div>
              </div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="detail-label">시장:</span>
                  <span class="detail-value">{{ harvest.market || '미정' }}</span>
                </div>
                <div class="detail-item" v-if="harvest.notes">
                  <span class="detail-label">비고:</span>
                  <span class="detail-value">{{ harvest.notes }}</span>
                </div>
              </div>
            </div>
            <div class="harvest-actions">
              <button @click="openEditModal(harvest)" class="edit-btn">✏️ 수정</button>
              <button @click="openQRModal(harvest)" class="qr-btn">📱 QR코드</button>
              <button @click="handleToggleStatus(harvest)" class="toggle-btn">
                {{ harvest.isActive ? '⏸️ 비활성화' : '▶️ 활성화' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 출하 기록이 없을 때 -->
        <div v-if="!loading && !error && filteredHarvests.length === 0" class="empty-state">
          <div class="empty-icon">📦</div>
          <p>등록된 출하 기록이 없습니다.</p>
          <button @click="openAddModal" class="add-first-btn">첫 번째 출하 기록 등록하기</button>
        </div>
      </div>
    </div>

    <!-- 출하 기록 등록/수정 모달 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? '출하 기록 수정' : '새 출하 기록 등록' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSaveHarvest">
            <div class="form-row">
              <div class="form-group">
                <label>작물 *</label>
                <select v-model="harvestForm.cropId" class="form-select" required>
                  <option value="">작물 선택</option>
                  <option v-for="crop in availableCrops" :key="crop.id" :value="crop.id">
                    {{ crop.cropName }} ({{ crop.productCode }})
                  </option>
                </select>
              </div>
              <div class="form-group">
                <label>비닐하우스</label>
                <select v-model="harvestForm.greenhouseId" class="form-select">
                  <option value="">비닐하우스 선택</option>
                  <option v-for="greenhouse in availableGreenhouses" :key="greenhouse.id" :value="greenhouse.id">
                    {{ greenhouse.greenhouseNumber }} ({{ greenhouse.currentCrop || '비어있음' }})
                  </option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>출하일 *</label>
                <input 
                  v-model="harvestForm.harvestDate" 
                  type="datetime-local" 
                  class="form-input"
                  required
                >
              </div>
              <div class="form-group">
                <label>수량 (BOX) *</label>
                <input 
                  v-model="harvestForm.quantity" 
                  type="number" 
                  min="1"
                  class="form-input"
                  required
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>단가 (원) *</label>
                <input 
                  v-model="harvestForm.unitPrice" 
                  type="number" 
                  min="0"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-group">
                <label>경매가 (원)</label>
                <input 
                  v-model="harvestForm.auctionPrice" 
                  type="number" 
                  min="0"
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>등급</label>
                <select v-model="harvestForm.grade" class="form-select">
                  <option value="">등급 선택</option>
                  <option value="특상">특상</option>
                  <option value="상">상</option>
                  <option value="중">중</option>
                  <option value="하">하</option>
                </select>
              </div>
              <div class="form-group">
                <label>크기</label>
                <input 
                  v-model="harvestForm.size" 
                  type="text" 
                  placeholder="예: 3L, 5L, 10kg"
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>시장</label>
                <select v-model="harvestForm.market" class="form-select">
                  <option value="">시장 선택</option>
                  <option value="가락동">가락동</option>
                  <option value="부산">부산</option>
                  <option value="대구">대구</option>
                  <option value="광주">광주</option>
                  <option value="대전">대전</option>
                </select>
              </div>
            </div>
            <div class="form-group">
              <label>비고</label>
              <textarea 
                v-model="harvestForm.notes" 
                placeholder="추가 정보를 입력하세요"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>
            <div class="form-actions">
              <button type="button" @click="closeModal" class="cancel-btn">취소</button>
              <button type="submit" class="save-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>{{ showEditModal ? '수정' : '등록' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- QR 코드 모달 -->
    <div v-if="showQRModal" class="modal-overlay" @click="closeQRModal">
      <div class="modal-content qr-modal" @click.stop>
        <div class="modal-header">
          <h3>QR 코드 - {{ editingHarvest?.crop?.cropName }}</h3>
          <button @click="closeQRModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div v-if="qrCodeLoading" class="qr-loading">
            <div class="loading-spinner">🔄</div>
            <p>QR 코드를 생성하는 중...</p>
          </div>
          <div v-else-if="qrCodeData" class="qr-content">
            <img :src="qrCodeData.qrCode" alt="출하 QR 코드" class="qr-image">
            <div class="qr-info">
              <p><strong>작물명:</strong> {{ qrCodeData.harvest?.crop?.cropName }}</p>
              <p><strong>출하일:</strong> {{ formatDate(qrCodeData.harvest?.harvestDate) }}</p>
              <p><strong>수량:</strong> {{ qrCodeData.harvest?.quantity }}BOX</p>
            </div>
            <button @click="downloadQRCode" class="download-btn">📥 다운로드</button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import Layout from '../components/Layout.vue'
import { useHarvestManagement } from '@/composables/useHarvestManagement'

const layoutRef = ref(null)

// Composable에서 상태와 함수 가져오기
const {
  harvests,
  statistics,
  loading,
  error,
  saving,
  availableCrops,
  availableGreenhouses,
  todayHarvests,
  loadHarvests,
  loadCrops,
  loadGreenhouses,
  saveHarvest: saveHarvestApi,
  toggleHarvestStatus: toggleHarvestStatusApi,
  generateHarvestQRCode: generateQRCodeApi
} = useHarvestManagement()

// 필터 상태
const searchKeyword = ref('')
const selectedGrade = ref('')
const selectedMarket = ref('')

// 모달 상태
const showAddModal = ref(false)
const showEditModal = ref(false)
const showQRModal = ref(false)
const editingHarvest = ref(null)

// QR 코드 관련
const qrCodeData = ref(null)
const qrCodeLoading = ref(false)

// 폼 데이터
const harvestForm = ref({
  cropId: '',
  greenhouseId: '',
  harvestDate: new Date().toISOString().slice(0, 16),
  quantity: 1,
  unitPrice: 0,
  auctionPrice: 0,
  grade: '상',
  size: '',
  market: '가락동',
  notes: ''
})

// 계산된 속성 (UI 로직)
const filteredHarvests = computed(() => {
  return harvests.value.filter(harvest => {
    const keyword = searchKeyword.value.toLowerCase()
    const gradeMatch = !selectedGrade.value || harvest.grade === selectedGrade.value
    const marketMatch = !selectedMarket.value || harvest.market === selectedMarket.value
    const keywordMatch = !keyword ||
      harvest.crop?.cropName.toLowerCase().includes(keyword) ||
      harvest.market?.toLowerCase().includes(keyword) ||
      (harvest.notes && harvest.notes.toLowerCase().includes(keyword))
      
    return gradeMatch && marketMatch && keywordMatch
  })
})

const resetForm = () => {
    harvestForm.value = {
        cropId: '',
        greenhouseId: '',
        harvestDate: new Date().toISOString().slice(0, 16),
        quantity: 1,
        unitPrice: 0,
        auctionPrice: 0,
        grade: '상',
        size: '',
        market: '가락동',
        notes: ''
    }
}

// 모달 관련 함수들
const openAddModal = () => {
    resetForm()
    showAddModal.value = true
}

const openEditModal = (harvest) => {
  editingHarvest.value = harvest
  harvestForm.value = {
    cropId: harvest.cropId || '',
    greenhouseId: harvest.greenhouseId || '',
    harvestDate: harvest.harvestDate ? harvest.harvestDate.slice(0, 16) : '',
    quantity: harvest.quantity || 1,
    unitPrice: harvest.unitPrice || 0,
    auctionPrice: harvest.auctionPrice || 0,
    grade: harvest.grade || '',
    size: harvest.size || '',
    market: harvest.market || '',
    notes: harvest.notes || ''
  }
  showEditModal.value = true
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingHarvest.value = null
}

const openQRModal = async (harvest) => {
  editingHarvest.value = harvest
  qrCodeLoading.value = true
  qrCodeData.value = null
  showQRModal.value = true
  
  const result = await generateQRCodeApi(harvest.id)
  if (result) {
      qrCodeData.value = result;
  } else {
      alert('QR 코드 생성에 실패했습니다.')
      closeQRModal()
  }
  qrCodeLoading.value = false
}

const closeQRModal = () => {
  showQRModal.value = false
  editingHarvest.value = null
  qrCodeData.value = null
}

// 데이터 처리 함수 (UI -> Composable 호출)
const handleSaveHarvest = async () => {
  const harvestData = {
    ...harvestForm.value,
    totalPrice: (harvestForm.value.quantity || 0) * (harvestForm.value.unitPrice || 0)
  }
  
  const success = await saveHarvestApi(harvestData, editingHarvest.value?.id)
  if (success) {
    alert(`출하 기록이 성공적으로 ${editingHarvest.value ? '수정' : '등록'}되었습니다.`)
    closeModal()
  } else {
    alert('출하 기록 저장에 실패했습니다: ' + error.value)
  }
}

const handleToggleStatus = async (harvest) => {
    if (confirm(`정말로 이 출하 기록을 ${harvest.isActive ? '비활성화' : '활성화'}하시겠습니까?`)) {
        // 현재 API는 DELETE만 있으므로, 실제 토글 API로 변경 필요
        const success = await toggleHarvestStatusApi(harvest.id);
        if(success) {
            alert('상태가 변경되었습니다.');
        } else {
            alert('상태 변경에 실패했습니다: ' + error.value);
        }
    }
}

// 유틸리티 함수
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleString('ko-KR')
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('ko-KR').format(amount || 0) + '원'
}

const getGradeClass = (grade) => {
  const gradeMap = { '특상': 'special', '상': 'high', '중': 'medium', '하': 'low' }
  return gradeMap[grade] || ''
}

const downloadQRCode = () => {
  if (!qrCodeData.value?.qrCode) return
  
  const link = document.createElement('a')
  link.href = qrCodeData.value.qrCode
  link.download = `${editingHarvest.value.crop.cropName}_출하_QR코드.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 날짜 필터 함수
const filterByDate = (period) => {
  const now = new Date();
  let startDate = new Date();

  if (period === 'today') {
    startDate.setHours(0, 0, 0, 0);
  } else if (period === 'week') {
    startDate.setDate(now.getDate() - now.getDay());
    startDate.setHours(0, 0, 0, 0);
  } else if (period === 'month') {
    startDate.setDate(1);
    startDate.setHours(0, 0, 0, 0);
  } else {
    loadHarvests(); // 전체 로드
    return;
  }
  
  const filtered = harvests.value.filter(h => new Date(h.harvestDate) >= startDate);
  // 이 부분은 현재 harvests ref를 직접 수정하는 대신,
  // filteredHarvests computed 속성에서 처리하도록 개선할 수 있습니다.
  // 지금은 임시로 harvests 값을 변경하는 방식을 유지합니다.
  // harvests.value = filtered; 
  // -> 이 방식은 원본 데이터를 잃게 하므로 좋지 않습니다.
  // 대신, 필터링을 위한 별도의 ref를 두는 것이 좋습니다. (추후 리팩토링)
  
  // 지금은 단순히 API를 다시 호출하는 방식으로 대체합니다.
  // 백엔드에 기간별 조회 API가 있다면 그것을 사용하는 것이 가장 좋습니다.
  // e.g. apiClient.get(`/harvests?period=${period}`)
  alert(`${period} 필터링은 아직 구현되지 않았습니다. 백엔드 지원이 필요합니다.`);
}

// 컴포넌트 마운트 시 데이터 로드
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/harvest')
  }
  
  loadHarvests()
  loadCrops()
  loadGreenhouses()
})

// watch(harvests, () => {
//     // 데이터가 변경될 때마다 통계를 다시 계산할 수 있습니다.
//     // calculateStatistics(); // useHarvestManagement에서 이미 처리됨
// });
</script>

<style scoped>
.harvest-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.harvest-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.grade-select,
.market-select {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 120px;
  cursor: pointer;
}

.date-filters {
  display: flex;
  gap: 10px;
}

.date-btn {
  padding: 12px 15px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.date-btn:hover {
  background: #2980b9;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  padding: 25px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.error-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
  opacity: 0.7;
}

.error-text {
  color: #856404;
  font-size: 0.95rem;
  margin-bottom: 0;
  flex: 1;
  text-align: center;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #2980b9;
}

.harvest-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.harvest-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.harvest-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.harvest-card.inactive {
  opacity: 0.6;
}

.harvest-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.harvest-crop {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.crop-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #2c3e50;
}

.greenhouse-number {
  font-size: 0.9rem;
  color: #7f8c8d;
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

.harvest-date {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.harvest-details {
  margin-bottom: 20px;
}

.detail-row {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
}

.detail-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.detail-label {
  font-size: 0.8rem;
  color: #7f8c8d;
  font-weight: bold;
}

.detail-value {
  font-size: 1rem;
  color: #2c3e50;
}

.total-price {
  font-weight: bold;
  color: #27ae60;
}

.grade.special { color: #e74c3c; font-weight: bold; }
.grade.high { color: #f39c12; font-weight: bold; }
.grade.medium { color: #3498db; font-weight: bold; }
.grade.low { color: #95a5a6; font-weight: bold; }

.harvest-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.edit-btn,
.qr-btn,
.toggle-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.edit-btn { background: #3498db; color: white; }
.qr-btn { background: #f39c12; color: white; }
.toggle-btn { background: #e74c3c; color: white; }

.edit-btn:hover,
.qr-btn:hover,
.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.add-first-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.qr-modal {
  max-width: 500px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.cancel-btn:hover,
.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.qr-loading {
  text-align: center;
  padding: 40px;
}

.qr-content {
  text-align: center;
}

.qr-image {
  max-width: 100%;
  height: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 20px;
}

.qr-info {
  margin-bottom: 20px;
}

.qr-info p {
  margin: 5px 0;
  color: #2c3e50;
}

.download-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
}

.download-btn:hover {
  background: #2980b9;
}

@media (max-width: 768px) {
  .harvest-list {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .date-filters {
    flex-wrap: wrap;
  }
  
  .harvest-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
}
</style> 