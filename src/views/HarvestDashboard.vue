<template>
  <Layout ref="layoutRef">
    <div class="harvest-dashboard">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>🌾 스마트 수확 관리</h1>
        <p class="page-subtitle">자동화된 수확 상태 관리 시스템</p>
      </div>

      <!-- 알림 권한 요청 -->
      <div v-if="isSupported && permission !== 'granted'" class="notification-permission">
        <div class="permission-banner">
          <div class="permission-content">
            <span class="permission-icon">🔔</span>
            <span class="permission-text">수확 완료 알림을 받으려면 알림 권한이 필요합니다.</span>
            <button @click="initializeNotifications" class="permission-btn">
              알림 권한 허용
            </button>
          </div>
        </div>
      </div>

      <!-- 시스템 상태 카드 -->
      <div class="system-status">
        <div class="status-cards">
          <div class="status-card">
            <div class="status-icon">📊</div>
            <div class="status-info">
              <div class="status-label">총 작물</div>
              <div class="status-value">{{ statistics?.totalCrops || 0 }}개</div>
            </div>
          </div>
          <div class="status-card">
            <div class="status-icon">✅</div>
            <div class="status-info">
              <div class="status-label">수확 완료</div>
              <div class="status-value">{{ statistics?.completedCount || 0 }}개</div>
            </div>
          </div>
          <div class="status-card">
            <div class="status-icon">🔄</div>
            <div class="status-info">
              <div class="status-label">수확 진행중</div>
              <div class="status-value">{{ statistics?.inProgressCount || 0 }}개</div>
            </div>
          </div>
          <div class="status-card">
            <div class="status-icon">⚠️</div>
            <div class="status-info">
              <div class="status-label">미완료</div>
              <div class="status-value">{{ statistics?.incompleteCount || 0 }}개</div>
            </div>
          </div>
          <div class="status-card">
            <div class="status-icon">🚨</div>
            <div class="status-info">
              <div class="status-label">오래된 미완료</div>
              <div class="status-value">{{ statistics?.veryOldCount || 0 }}개</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 메인 콘텐츠 -->
      <div class="dashboard-content">
        <!-- 차트 섹션 -->
        <div class="charts-section">
          <!-- 수확 진행률 차트 -->
          <div class="chart-container">
            <h3>📈 수확 진행률 현황</h3>
            <canvas ref="harvestProgressChart" width="400" height="300"></canvas>
          </div>

          <!-- 실패 원인 분석 -->
          <div class="chart-container">
            <h3>📊 수확 실패 원인 분석</h3>
            <canvas ref="failureReasonChart" width="400" height="300"></canvas>
          </div>
        </div>

        <!-- 알림 센터 -->
        <div class="alert-section">
          <div class="alert-container">
            <h3>🔔 수확 완료 알림 대상</h3>
            <div class="alert-list">
              <div v-if="reminderTargets.length === 0" class="no-alerts">
                <div class="no-alert-icon">✅</div>
                <p>수확 완료 알림 대상이 없습니다!</p>
              </div>
              <div 
                v-for="crop in reminderTargets" 
                :key="crop.id"
                class="alert-item"
              >
                <div class="alert-content">
                  <div class="crop-info">
                    <h4>{{ crop.cropName }}</h4>
                    <p>{{ crop.greenhouse?.greenhouseNumber || '온실 미지정' }}</p>
                    <p>예상 수확일: {{ formatDate(crop.expectedHarvestDate) }}</p>
                  </div>
                  <div class="alert-actions">
                    <button @click="handleAutoComplete(crop.id)" class="auto-complete-btn">
                      🤖 자동 완료
                    </button>
                    <button @click="handleManualComplete(crop)" class="manual-complete-btn">
                      ✏️ 수동 완료
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 수확 관리 테이블 -->
        <div class="table-section">
          <div class="table-container">
            <h3>📋 수확 관리 현황</h3>
            <div class="table-filters">
              <select v-model="statusFilter" class="status-filter">
                <option value="">전체 상태</option>
                <option value="NOT_STARTED">수확 시작 전</option>
                <option value="IN_PROGRESS">수확 진행중</option>
                <option value="COMPLETED">수확 완료</option>
                <option value="FAILED">수확 실패</option>
              </select>
              <button @click="loadHarvestData" class="refresh-btn">🔄 새로고침</button>
            </div>
            <table class="harvest-table">
              <thead>
                <tr>
                  <th>작물명</th>
                  <th>온실</th>
                  <th>상태</th>
                  <th>진행률</th>
                  <th>예상 수확일</th>
                  <th>실제 수확일</th>
                  <th>수확량</th>
                  <th>실패 사유</th>
                  <th>작업</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="crop in filteredCrops" :key="crop.id">
                  <td>{{ crop.cropName }}</td>
                  <td>{{ crop.greenhouse?.greenhouseNumber || '-' }}</td>
                  <td>
                    <span :class="getStatusClass(crop.harvestStatus)">
                      {{ getStatusText(crop.harvestStatus) }}
                    </span>
                  </td>
                  <td>
                    <div class="progress-bar">
                      <div class="progress-fill" :style="{ width: crop.harvestProgress + '%' }"></div>
                      <span class="progress-text">{{ crop.harvestProgress }}%</span>
                    </div>
                  </td>
                  <td>{{ formatDate(crop.expectedHarvestDate) }}</td>
                  <td>{{ formatDate(crop.actualHarvestDate) || '-' }}</td>
                  <td>{{ crop.actualYield ? crop.actualYield + 'kg' : '-' }}</td>
                  <td>{{ getFailureReasonText(crop.harvestFailureReason) || '-' }}</td>
                  <td>
                    <div class="action-buttons">
                      <button @click="openProgressModal(crop)" class="progress-btn">📊</button>
                                          <button @click="openCompleteModal(crop)" class="complete-btn">✅</button>
                    <button @click="openFailModal(crop)" class="fail-btn">❌</button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- 주간 리포트 -->
        <div class="report-section">
          <div class="report-container">
            <h3>📈 주간 수확 현황 리포트</h3>
            <div v-if="weeklyReport" class="report-content">
              <div class="report-stats">
                <div class="report-stat">
                  <span class="stat-label">수확 성공률</span>
                  <span class="stat-value">{{ weeklyReport.successRate }}%</span>
                </div>
                <div class="report-stat">
                  <span class="stat-label">총 수확량</span>
                  <span class="stat-value">{{ weeklyReport.totalYield }}kg</span>
                </div>
                <div class="report-stat">
                  <span class="stat-label">평균 수확량</span>
                  <span class="stat-value">{{ weeklyReport.averageYield }}kg</span>
                </div>
                <div class="report-stat">
                  <span class="stat-label">실패 건수</span>
                  <span class="stat-value">{{ weeklyReport.failureCount }}건</span>
                </div>
              </div>
              <div class="report-chart">
                <canvas ref="weeklyReportChart" width="400" height="300"></canvas>
              </div>
            </div>
            <div v-else class="loading-report">
              <div class="loading-spinner">🔄</div>
              <p>주간 리포트를 생성하는 중...</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 모달들 -->
      <!-- 수확 완료 모달 -->
      <div v-if="showCompleteModal" class="modal-overlay" @click="closeCompleteModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>수확 완료 처리</h3>
            <button @click="closeCompleteModal" class="close-btn">✕</button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitComplete">
              <div class="form-group">
                <label>실제 수확량 (kg)</label>
                <input 
                  v-model="completeForm.actualYield" 
                  type="number" 
                  step="0.1"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-actions">
                <button type="button" @click="closeCompleteModal" class="cancel-btn">취소</button>
                <button type="submit" class="submit-btn">완료 처리</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 수확 실패 모달 -->
      <div v-if="showFailModal" class="modal-overlay" @click="closeFailModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>수확 실패 처리</h3>
            <button @click="closeFailModal" class="close-btn">✕</button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitFail">
              <div class="form-group">
                <label>실패 사유</label>
                <select v-model="failForm.failureReason" class="form-select" required>
                  <option value="">실패 사유 선택</option>
                  <option value="DISEASE">질병</option>
                  <option value="PEST">해충</option>
                  <option value="WEATHER">기상 악화</option>
                  <option value="NUTRIENT_DEFICIENCY">영양 부족</option>
                  <option value="WATER_ISSUE">수분 문제</option>
                  <option value="EQUIPMENT_FAILURE">장비 고장</option>
                  <option value="HUMAN_ERROR">인적 오류</option>
                  <option value="OTHER">기타</option>
                </select>
              </div>
              <div class="form-group">
                <label>실패 상세 내용</label>
                <textarea 
                  v-model="failForm.failureNotes" 
                  class="form-textarea"
                  rows="3"
                  placeholder="실패 원인에 대한 상세 설명을 입력하세요"
                ></textarea>
              </div>
              <div class="form-actions">
                <button type="button" @click="closeFailModal" class="cancel-btn">취소</button>
                <button type="submit" class="submit-btn">실패 처리</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 진행률 업데이트 모달 -->
      <div v-if="showProgressModal" class="modal-overlay" @click="closeProgressModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>수확 진행률 업데이트</h3>
            <button @click="closeProgressModal" class="close-btn">✕</button>
          </div>
          <div class="modal-body">
            <form @submit.prevent="submitProgress">
              <div class="form-group">
                <label>진행률 (%)</label>
                <input 
                  v-model="progressForm.progress" 
                  type="number" 
                  min="0"
                  max="100"
                  class="form-input"
                  required
                >
              </div>
              <div class="form-actions">
                <button type="button" @click="closeProgressModal" class="cancel-btn">취소</button>
                <button type="submit" class="submit-btn">업데이트</button>
              </div>
            </form>
          </div>
        </div>
      </div>

      <!-- 로딩 상태 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">🔄</div>
        <p>데이터를 불러오는 중...</p>
      </div>

      <!-- 에러 상태 -->
      <div v-if="error" class="error-container">
        <div class="error-icon">⚠️</div>
        <p class="error-text">{{ error }}</p>
        <button @click="loadAllData" class="retry-btn">다시 시도</button>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import Layout from '../components/Layout.vue'
import Chart from 'chart.js/auto'
import { useHarvestManagement } from '../composables/useHarvestManagement'
import { useNotifications } from '../composables/useNotifications'

const layoutRef = ref(null)

// 수확 관리 composable 사용
const {
  loading,
  error,
  updateHarvestStatus,
  completeHarvest,
  failHarvest,
  updateProgress,
  autoCompleteHarvest,
  archiveCrop,
  getIncompleteStatistics,
  getWeeklyReport,
  getReminderTargets,
  getFailureReasons,
  getStatusText,
  getFailureReasonText,
  getStatusClass,
  formatDate,
  calculateProgress,
  isOverdue,
  isUrgentReminder,
  isAutoCompleteTarget
} = useHarvestManagement()

// 알림 composable 사용
const {
  isSupported,
  permission,
  subscription,
  showHarvestReminder,
  showAutoCompleteNotification,
  showHarvestFailureNotification,
  showWeeklyReportNotification,
  initializeNotifications
} = useNotifications()

// 상태 관리
const statusFilter = ref('')

// 차트 참조
const harvestProgressChart = ref(null)
const failureReasonChart = ref(null)
const weeklyReportChart = ref(null)

// 데이터
const statistics = ref({})
const reminderTargets = ref([])
const crops = ref([])
const weeklyReport = ref(null)

// 모달 상태
const showCompleteModal = ref(false)
const showFailModal = ref(false)
const showProgressModal = ref(false)
const selectedCrop = ref(null)

// 폼 데이터
const completeForm = ref({
  actualYield: ''
})

const failForm = ref({
  failureReason: '',
  failureNotes: ''
})

const progressForm = ref({
  progress: 0
})

// 차트 인스턴스
let harvestProgressChartInstance = null
let failureReasonChartInstance = null
let weeklyReportChartInstance = null

// 계산된 속성
const filteredCrops = computed(() => {
  if (!statusFilter.value) return crops.value
  return crops.value.filter(crop => crop.harvestStatus === statusFilter.value)
})

// API 호출 함수들
const loadAllData = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await Promise.all([
      loadStatistics(),
      loadReminderTargets(),
      loadHarvestData(),
      loadWeeklyReport()
    ])
    
    await nextTick()
    createCharts()
  } catch (err) {
    error.value = '데이터를 불러오는데 실패했습니다: ' + err.message
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const result = await getIncompleteStatistics()
    if (result.success) {
      statistics.value = result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('통계 로드 실패:', error)
    throw error
  }
}

const loadReminderTargets = async () => {
  try {
    const result = await getReminderTargets()
    if (result.success) {
      reminderTargets.value = result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('알림 대상 로드 실패:', error)
    reminderTargets.value = []
  }
}

const loadHarvestData = async () => {
  try {
    const response = await fetch('/api/crops')
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    const result = await response.json()
    if (result.success) {
      crops.value = result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('수확 데이터 로드 실패:', error)
    throw error
  }
}

const loadWeeklyReport = async () => {
  try {
    const result = await getWeeklyReport()
    if (result.success) {
      weeklyReport.value = result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('주간 리포트 로드 실패:', error)
    weeklyReport.value = null
  }
}

// 차트 생성 함수들
const createCharts = () => {
  setTimeout(() => {
    if (harvestProgressChart.value) createHarvestProgressChart()
    if (failureReasonChart.value) createFailureReasonChart()
    if (weeklyReportChart.value && weeklyReport.value) createWeeklyReportChart()
  }, 100)
}

const createHarvestProgressChart = () => {
  if (harvestProgressChartInstance) {
    harvestProgressChartInstance.destroy()
  }

  const ctx = harvestProgressChart.value.getContext('2d')
  const statusCounts = {
    'NOT_STARTED': crops.value.filter(c => c.harvestStatus === 'NOT_STARTED').length,
    'IN_PROGRESS': crops.value.filter(c => c.harvestStatus === 'IN_PROGRESS').length,
    'COMPLETED': crops.value.filter(c => c.harvestStatus === 'COMPLETED').length,
    'FAILED': crops.value.filter(c => c.harvestStatus === 'FAILED').length
  }

  harvestProgressChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['수확 시작 전', '수확 진행중', '수확 완료', '수확 실패'],
      datasets: [{
        data: [statusCounts.NOT_STARTED, statusCounts.IN_PROGRESS, statusCounts.COMPLETED, statusCounts.FAILED],
        backgroundColor: ['#95a5a6', '#f39c12', '#27ae60', '#e74c3c'],
        borderWidth: 2,
        borderColor: '#fff'
      }]
    },
        options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'bottom',
          labels: {
            font: {
              size: 11
            },
            padding: 15,
            usePointStyle: true,
            pointStyle: 'circle'
          }
        },
        title: {
          display: true,
          text: '수확 상태 분포',
          font: {
            size: 14
          },
          padding: {
            bottom: 10
          }
        }
      },
      layout: {
        padding: {
          bottom: 30,
          top: 10
        }
      }
    }
  })
}

const createFailureReasonChart = () => {
  if (failureReasonChartInstance) {
    failureReasonChartInstance.destroy()
  }

  const ctx = failureReasonChart.value.getContext('2d')
  const failedCrops = crops.value.filter(c => c.harvestStatus === 'FAILED')
  const failureReasons = {}
  
  failedCrops.forEach(crop => {
    const reason = crop.harvestFailureReason || 'UNKNOWN'
    failureReasons[reason] = (failureReasons[reason] || 0) + 1
  })

  const labels = Object.keys(failureReasons).map(key => getFailureReasonText(key))
  const data = Object.values(failureReasons)

  failureReasonChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: labels,
      datasets: [{
        label: '실패 건수',
        data: data,
        backgroundColor: 'rgba(231, 76, 60, 0.8)',
        borderColor: '#e74c3c',
        borderWidth: 1
      }]
    },
        options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        title: {
          display: true,
          text: '수확 실패 원인별 통계',
          font: {
            size: 14
          },
          padding: {
            bottom: 10
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            stepSize: 1,
            font: {
              size: 11
            }
          }
        },
        x: {
          ticks: {
            font: {
              size: 11
            }
          }
        }
      },
      layout: {
        padding: {
          bottom: 40,
          top: 10
        }
      }
    }
  })
}

const createWeeklyReportChart = () => {
  if (weeklyReportChartInstance) {
    weeklyReportChartInstance.destroy()
  }

  const ctx = weeklyReportChart.value.getContext('2d')
  
  weeklyReportChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: weeklyReport.value.dailyData?.map(d => d.date) || [],
      datasets: [{
        label: '일일 수확량',
        data: weeklyReport.value.dailyData?.map(d => d.yield) || [],
        borderColor: '#27ae60',
        backgroundColor: 'rgba(39, 174, 96, 0.1)',
        tension: 0.4
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
          labels: {
            font: {
              size: 11
            }
          }
        },
        title: {
          display: true,
          text: '주간 수확량 추이',
          font: {
            size: 14
          },
          padding: {
            bottom: 10
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: '수확량 (kg)',
            font: {
              size: 11
            }
          },
          ticks: {
            font: {
              size: 11
            }
          }
        },
        x: {
          ticks: {
            font: {
              size: 11
            }
          }
        }
      },
      layout: {
        padding: {
          bottom: 30,
          top: 10
        }
      }
    }
  })
}

// 액션 함수들
const handleAutoComplete = async (cropId) => {
  try {
    const result = await autoCompleteHarvest(cropId)
    if (result.success) {
      // 알림 표시
      try {
        const crop = crops.value.find(c => c.id === cropId)
        if (crop) {
          showAutoCompleteNotification(crop.cropName)
        }
      } catch (notificationError) {
        console.warn('알림 표시 실패:', notificationError)
      }
      
      alert('자동 완료 처리되었습니다!')
      await loadAllData()
    } else {
      alert('자동 완료 처리에 실패했습니다: ' + result.message)
    }
  } catch (error) {
    console.error('자동 완료 처리 실패:', error)
    alert('자동 완료 처리에 실패했습니다: ' + error.message)
  }
}

const handleManualComplete = (crop) => {
  selectedCrop.value = crop
  completeForm.value.actualYield = crop.expectedYield || ''
  showCompleteModal.value = true
}

const openCompleteModal = (crop) => {
  selectedCrop.value = crop
  completeForm.value.actualYield = crop.expectedYield || ''
  showCompleteModal.value = true
}

const openFailModal = (crop) => {
  selectedCrop.value = crop
  failForm.value.failureReason = ''
  failForm.value.failureNotes = ''
  showFailModal.value = true
}

const openProgressModal = (crop) => {
  selectedCrop.value = crop
  progressForm.value.progress = crop.harvestProgress || 0
  showProgressModal.value = true
}

// 모달 제출 함수들
const submitComplete = async () => {
  try {
    const result = await completeHarvest(selectedCrop.value.id, completeForm.value.actualYield)
    if (result.success) {
      alert('수확 완료 처리되었습니다!')
      closeCompleteModal()
      await loadAllData()
    } else {
      alert('수확 완료 처리에 실패했습니다: ' + result.message)
    }
  } catch (error) {
    console.error('수확 완료 처리 실패:', error)
    alert('수확 완료 처리에 실패했습니다: ' + error.message)
  }
}

const submitFail = async () => {
  try {
    const result = await failHarvest(selectedCrop.value.id, {
      failureReason: failForm.value.failureReason,
      failureNotes: failForm.value.failureNotes
    })
    if (result.success) {
      // 알림 표시
      try {
        const failureReasonText = getFailureReasonText(failForm.value.failureReason)
        showHarvestFailureNotification(selectedCrop.value.cropName, failureReasonText)
      } catch (notificationError) {
        console.warn('알림 표시 실패:', notificationError)
      }
      
      alert('수확 실패 처리되었습니다!')
      closeFailModal()
      await loadAllData()
    } else {
      alert('수확 실패 처리에 실패했습니다: ' + result.message)
    }
  } catch (error) {
    console.error('수확 실패 처리 실패:', error)
    alert('수확 실패 처리에 실패했습니다: ' + error.message)
  }
}

const submitProgress = async () => {
  try {
    const result = await updateProgress(selectedCrop.value.id, progressForm.value.progress)
    if (result.success) {
      alert('진행률이 업데이트되었습니다!')
      closeProgressModal()
      await loadAllData()
    } else {
      alert('진행률 업데이트에 실패했습니다: ' + result.message)
    }
  } catch (error) {
    console.error('진행률 업데이트 실패:', error)
    alert('진행률 업데이트에 실패했습니다: ' + error.message)
  }
}

// 모달 닫기 함수들
const closeCompleteModal = () => {
  showCompleteModal.value = false
  selectedCrop.value = null
  completeForm.value.actualYield = ''
}

const closeFailModal = () => {
  showFailModal.value = false
  selectedCrop.value = null
  failForm.value.failureReason = ''
  failForm.value.failureNotes = ''
}

const closeProgressModal = () => {
  showProgressModal.value = false
  selectedCrop.value = null
  progressForm.value.progress = 0
}

// 유틸리티 함수들은 composable에서 가져옴

// 컴포넌트 마운트
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/harvest-dashboard')
  }
  loadAllData()
})
</script>

<style scoped>
.harvest-dashboard {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.notification-permission {
  margin-bottom: 20px;
}

.permission-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 20px;
  color: white;
}

.permission-content {
  display: flex;
  align-items: center;
  gap: 15px;
  justify-content: space-between;
}

.permission-icon {
  font-size: 1.5rem;
}

.permission-text {
  flex: 1;
  font-size: 1rem;
}

.permission-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  padding: 10px 20px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.permission-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
}

.system-status {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.dashboard-content {
  display: grid;
  gap: 30px;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  height: 500px;
  position: relative;
  overflow: hidden;
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.alert-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.alert-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.no-alerts {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.no-alert-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.alert-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.alert-item {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 20px;
}

.alert-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.crop-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.crop-info p {
  margin: 5px 0;
  color: #7f8c8d;
  font-size: 0.9rem;
}

.alert-actions {
  display: flex;
  gap: 10px;
}

.auto-complete-btn,
.manual-complete-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.auto-complete-btn {
  background: #27ae60;
  color: white;
}

.auto-complete-btn:hover {
  background: #229954;
}

.manual-complete-btn {
  background: #3498db;
  color: white;
}

.manual-complete-btn:hover {
  background: #2980b9;
}

.table-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.table-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
}

.status-filter {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
}

.refresh-btn {
  padding: 8px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: #2980b9;
}

.harvest-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
}

.harvest-table th,
.harvest-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
  color: #2c3e50 !important;
}

.harvest-table th {
  background: #f8f9fa;
  font-weight: bold;
  color: #2c3e50 !important;
}

.harvest-table tr:hover {
  background: #f8f9fa;
}

.status-not-started {
  background: #95a5a6;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-in-progress {
  background: #f39c12;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-completed {
  background: #27ae60;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.status-failed {
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.progress-bar {
  position: relative;
  width: 100%;
  height: 20px;
  background: #ecf0f1;
  border-radius: 10px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #27ae60, #2ecc71);
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: bold;
  color: #2c3e50;
}

.action-buttons {
  display: flex;
  gap: 5px;
}

.progress-btn,
.complete-btn,
.fail-btn {
  padding: 6px 10px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.progress-btn {
  background: #3498db;
  color: white;
}

.progress-btn:hover {
  background: #2980b9;
}

.complete-btn {
  background: #27ae60;
  color: white;
}

.complete-btn:hover {
  background: #229954;
}

.fail-btn {
  background: #e74c3c;
  color: white;
}

.fail-btn:hover {
  background: #c0392b;
}

.report-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.report-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.report-content {
  display: grid;
  gap: 20px;
}

.report-chart {
  background: #ecf0f1;
  border-radius: 6px;
  padding: 20px;
  height: 300px;
  position: relative;
}

.report-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.report-stat {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-align: center;
}

.stat-label {
  display: block;
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 10px;
}

.stat-value {
  display: block;
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c3e50;
}

.loading-report {
  text-align: center;
  padding: 40px;
  color: #7f8c8d;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  color: #e74c3c;
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #27ae60;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 25px;
}

.cancel-btn,
.submit-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.cancel-btn:hover {
  background: #7f8c8d;
}

.submit-btn {
  background: #27ae60;
  color: white;
}

.submit-btn:hover {
  background: #229954;
}

/* 로딩 및 에러 상태 */
.loading-container,
.error-container {
  text-align: center;
  padding: 60px;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.error-text {
  color: #e74c3c;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.retry-btn {
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #2980b9;
}

@media (max-width: 768px) {
  .status-cards {
    grid-template-columns: 1fr;
  }
  
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .report-stats {
    grid-template-columns: 1fr;
  }
  
  .permission-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .alert-content {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
  
  .alert-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .harvest-table {
    font-size: 0.9rem;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 5px;
  }
}
</style> 