<template>
  <Layout ref="layoutRef">
    <div class="greenhouse-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>🏠 비닐하우스 관리</h1>
        <p class="page-subtitle">비닐하우스 정보를 관리합니다</p>
      </div>

      <!-- 시스템 상태 및 통계 -->
      <div class="system-section">
        <div class="system-status">
          <h3>📊 비닐하우스 현황</h3>
          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon">🏠</div>
              <div class="status-info">
                <div class="status-label">총 비닐하우스</div>
                <div class="status-value">{{ statistics?.totalGreenhouses || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">🌱</div>
              <div class="status-info">
                <div class="status-label">재배중</div>
                <div class="status-value">{{ statistics?.cultivatingGreenhouses || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📊</div>
              <div class="status-info">
                <div class="status-label">현황별 분포</div>
                <div class="status-value">{{ getStatusBreakdown() }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 비닐하우스 관리 섹션 -->
      <div class="greenhouse-section">
        <div class="section-header">
          <h3>📋 비닐하우스 목록</h3>
          <button @click="openAddModal" class="add-btn">
            <span>➕</span>
            새 비닐하우스 등록
          </button>
        </div>

        <!-- 필터 및 검색 -->
        <div class="filters">
          <div class="search-box">
            <input 
              v-model="searchKeyword" 
              type="text" 
              placeholder="비닐하우스 번호 또는 위치 검색..."
              class="search-input"
            >
            <span class="search-icon">🔍</span>
          </div>
          <select v-model="selectedStatus" class="status-select">
            <option value="">전체 현황</option>
            <option value="CULTIVATING">재배중</option>
            <option value="MAINTENANCE">정비중</option>
            <option value="EMPTY">비어있음</option>
          </select>
          <select v-model="selectedCrop" class="crop-select">
            <option value="">전체 작물</option>
            <option v-for="crop in availableCrops" :key="crop" :value="crop">{{ crop }}</option>
          </select>
        </div>

        <!-- 로딩 상태 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>비닐하우스 목록을 불러오는 중...</p>
        </div>

        <!-- 에러 상태 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ error }}</p>
          <button @click="loadGreenhouses" class="retry-btn">다시 시도</button>
        </div>

        <!-- 비닐하우스 목록 -->
        <div v-else class="greenhouse-grid" ref="greenhouseGrid">
          <div 
            v-for="greenhouse in filteredGreenhouses" 
            :key="greenhouse.id"
            class="greenhouse-card"
            :class="{ inactive: !greenhouse.isActive }"
          >
            <div class="greenhouse-header">
              <div class="greenhouse-number">{{ greenhouse.greenhouseNumber }}</div>
              <div class="greenhouse-status" :class="getStatusClass(greenhouse.status)">
                {{ getStatusText(greenhouse.status) }}
              </div>
            </div>
            <div class="greenhouse-info">
              <div class="info-row">
                <span class="info-label">재배 작물:</span>
                <span class="info-value">{{ greenhouse.currentCrop || '없음' }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">면적:</span>
                <span class="info-value">{{ greenhouse.area || 0 }}㎡</span>
              </div>
              <div class="info-row">
                <span class="info-label">위치:</span>
                <span class="info-value">{{ greenhouse.location || '미설정' }}</span>
              </div>
              <div class="info-row" v-if="greenhouse.description">
                <span class="info-label">설명:</span>
                <span class="info-value">{{ greenhouse.description }}</span>
              </div>
            </div>
            <div class="greenhouse-actions">
              <button @click="openEditModal(greenhouse)" class="edit-btn">✏️ 수정</button>
              <button @click="openStatusModal(greenhouse)" class="status-btn">🔄 현황변경</button>
              <button @click="handleToggleStatus(greenhouse)" class="toggle-btn">
                {{ greenhouse.isActive ? '⏸️ 비활성화' : '▶️ 활성화' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 비닐하우스가 없을 때 -->
        <div v-if="!loading && !error && filteredGreenhouses.length === 0" class="empty-state">
          <div class="empty-icon">🏠</div>
          <p>등록된 비닐하우스가 없습니다.</p>
          <button @click="openAddModal" class="add-first-btn">첫 번째 비닐하우스 등록하기</button>
        </div>
      </div>
    </div>

    <!-- 비닐하우스 등록/수정 모달 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? '비닐하우스 수정' : '새 비닐하우스 등록' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSaveGreenhouse">
            <div class="form-group" v-if="!showEditModal">
              <label>비닐하우스 번호 *</label>
              <input 
                v-model="greenhouseForm.greenhouseNumber" 
                type="text" 
                required
                placeholder="예: GH-001"
                class="form-input"
                :disabled="showEditModal"
              >
            </div>
            <div class="form-group">
              <label>현황 *</label>
              <select v-model="greenhouseForm.status" class="form-select" required>
                <option value="">현황 선택</option>
                <option value="CULTIVATING">재배중</option>
                <option value="MAINTENANCE">정비중</option>
                <option value="EMPTY">비어있음</option>
              </select>
            </div>
            <div class="form-group" v-if="greenhouseForm.status === 'CULTIVATING'">
              <label>재배 작물 *</label>
              <select 
                v-model="greenhouseForm.currentCrop" 
                required
                class="form-select"
                :disabled="loadingProductCodes"
              >
                <option value="">재배 작물을 선택하세요</option>
                <option 
                  v-for="crop in availableCrops" 
                  :key="crop" 
                  :value="crop"
                >
                  {{ crop }}
                </option>
              </select>
              <div v-if="loadingProductCodes" class="loading-text">작물 목록을 불러오는 중...</div>
            </div>
            <div class="form-group">
              <label>면적 (㎡)</label>
              <input 
                v-model="greenhouseForm.area" 
                type="number" 
                step="0.1"
                placeholder="면적을 입력하세요"
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>위치</label>
              <input 
                v-model="greenhouseForm.location" 
                type="text" 
                placeholder="위치 정보"
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>설명</label>
              <textarea 
                v-model="greenhouseForm.description" 
                placeholder="비닐하우스에 대한 설명을 입력하세요"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>
            <div class="form-actions">
              <button type="button" @click="closeModal" class="cancel-btn">취소</button>
              <button type="submit" class="save-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>{{ showEditModal ? '수정' : '등록' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 현황 변경 모달 -->
    <div v-if="showStatusModal" class="modal-overlay" @click="closeStatusModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>현황 변경 - {{ editingGreenhouse?.greenhouseNumber }}</h3>
          <button @click="closeStatusModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
                      <div class="form-group">
              <label>새로운 현황 *</label>
              <select v-model="statusForm.status" class="form-select" required>
                <option value="">현황 선택</option>
                <option value="CULTIVATING">재배중</option>
                <option value="MAINTENANCE">정비중</option>
                <option value="EMPTY">비어있음</option>
              </select>
            </div>
            <div class="form-group" v-if="statusForm.status === 'CULTIVATING'">
              <label>재배 작물 *</label>
              <select 
                v-model="statusForm.currentCrop" 
                required
                class="form-select"
                :disabled="loadingProductCodes"
              >
                <option value="">재배 작물을 선택하세요</option>
                <option 
                  v-for="crop in availableCrops" 
                  :key="crop" 
                  :value="crop"
                >
                  {{ crop }}
                </option>
              </select>
              <div v-if="loadingProductCodes" class="loading-text">작물 목록을 불러오는 중...</div>
            </div>
          <div class="form-actions">
            <button @click="closeStatusModal" class="cancel-btn">취소</button>
            <button @click="handleSaveStatus" class="save-btn" :disabled="saving">
              <span v-if="saving" class="loading-spinner">🔄</span>
              <span v-else>변경</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed, onUnmounted } from 'vue'
import Layout from '../components/Layout.vue'
import { useGreenhouseManagement } from '@/composables/useGreenhouseManagement'
import Sortable from 'sortablejs'

const layoutRef = ref(null)
const greenhouseGrid = ref(null)
let sortable = null

// Composable
const {
  greenhouses,
  statistics,
  availableCrops,
  loading,
  saving,
  error,
  loadGreenhouses,
  loadAvailableCrops,
  saveGreenhouse: saveGreenhouseApi,
  updateGreenhouseStatus: updateStatusApi,
  toggleGreenhouseStatus: toggleStatusApi
} = useGreenhouseManagement()

// 필터 상태
const searchKeyword = ref('')
const selectedStatus = ref('')
const selectedCrop = ref('')

// 모달 상태
const showAddModal = ref(false)
const showEditModal = ref(false)
const showStatusModal = ref(false)
const editingGreenhouse = ref(null)

// 폼 데이터
const greenhouseForm = ref({})
const statusForm = ref({})

// 계산된 속성
const filteredGreenhouses = computed(() => {
  return greenhouses.value.filter(g => {
    const statusMatch = !selectedStatus.value || g.status === selectedStatus.value
    const cropMatch = !selectedCrop.value || g.currentCrop === selectedCrop.value
    const keywordMatch = !searchKeyword.value ||
      g.greenhouseNumber.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      g.location?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    return statusMatch && cropMatch && keywordMatch
  })
})

const getStatusBreakdown = () => {
  const { statusBreakdown } = statistics.value
  if (!statusBreakdown) return '0/0/0'
  return `${statusBreakdown['CULTIVATING'] || 0}/${statusBreakdown['EMPTY'] || 0}/${statusBreakdown['MAINTENANCE'] || 0}`
}

// 유틸리티
const getStatusText = (status) => {
  const map = { 'CULTIVATING': '재배중', 'MAINTENANCE': '정비중', 'EMPTY': '비어있음' }
  return map[status] || status
}

const getStatusClass = (status) => {
  const map = { 'CULTIVATING': 'cultivating', 'MAINTENANCE': 'maintenance', 'EMPTY': 'empty' }
  return map[status] || ''
}

// 폼 및 모달 핸들러
const resetForms = () => {
    greenhouseForm.value = {
        greenhouseNumber: '',
        status: 'EMPTY',
        currentCrop: '',
        area: 0,
        location: '',
        description: ''
    };
    statusForm.value = {
        status: '',
        currentCrop: ''
    };
}

const openAddModal = () => {
    resetForms();
    showAddModal.value = true;
}

const openEditModal = (greenhouse) => {
  editingGreenhouse.value = greenhouse;
  greenhouseForm.value = { ...greenhouse };
  showEditModal.value = true;
}

const openStatusModal = (greenhouse) => {
  editingGreenhouse.value = greenhouse;
  statusForm.value = { status: greenhouse.status, currentCrop: greenhouse.currentCrop || '' };
  showStatusModal.value = true;
}

const closeModal = () => {
  showAddModal.value = false;
  showEditModal.value = false;
  editingGreenhouse.value = null;
}

const closeStatusModal = () => {
  showStatusModal.value = false;
  editingGreenhouse.value = null;
}


// 데이터 조작 핸들러
const handleSaveGreenhouse = async () => {
  const success = await saveGreenhouseApi(greenhouseForm.value, editingGreenhouse.value?.id)
  if (success) {
    alert(`비닐하우스가 성공적으로 ${editingGreenhouse.value ? '수정' : '등록'}되었습니다.`)
    closeModal()
  } else {
    alert('저장에 실패했습니다: ' + error.value)
  }
}

const handleSaveStatus = async () => {
  const success = await updateStatusApi(editingGreenhouse.value.id, statusForm.value)
  if (success) {
    alert('현황이 변경되었습니다.')
    closeStatusModal()
  } else {
    alert('현황 변경에 실패했습니다: ' + error.value)
  }
}

const handleToggleStatus = async (greenhouse) => {
    if (confirm(`'${greenhouse.greenhouseNumber}'를 ${greenhouse.isActive ? '비활성화' : '활성화'} 하시겠습니까?`)) {
        await toggleStatusApi(greenhouse.id);
    }
}


// Sortable.js 관련
const initSortable = () => {
  if (greenhouseGrid.value) {
    sortable = Sortable.create(greenhouseGrid.value, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      onEnd: () => {
        // 순서 변경 로직 (필요 시 API 연동)
      }
    })
  }
}

// 초기화
onMounted(async () => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/greenhouse')
  }
  await Promise.all([loadGreenhouses(), loadAvailableCrops()])
  initSortable()
})

onUnmounted(() => {
  if (sortable) {
    sortable.destroy()
  }
})
</script>

<style scoped>
.greenhouse-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.greenhouse-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.status-select,
.crop-select {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 120px;
  cursor: pointer;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  padding: 25px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  margin: 20px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
}

.error-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
  opacity: 0.7;
}

.error-text {
  color: #856404;
  font-size: 0.95rem;
  margin-bottom: 0;
  flex: 1;
  text-align: center;
}

.retry-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #2980b9;
}

.greenhouse-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.greenhouse-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.greenhouse-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.greenhouse-card.inactive {
  opacity: 0.6;
}

/* Sortable 스타일 */
.sortable-ghost {
  opacity: 0.5;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
}

.greenhouse-card {
  cursor: grab;
}

.greenhouse-card:active {
  cursor: grabbing;
}

.greenhouse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.greenhouse-number {
  font-size: 1.3rem;
  font-weight: bold;
  color: #2c3e50;
}

.greenhouse-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: bold;
}

.greenhouse-status.cultivating {
  background: #d4edda;
  color: #155724;
}

.greenhouse-status.empty {
  background: #f8d7da;
  color: #721c24;
}

.greenhouse-status.maintenance {
  background: #fff3cd;
  color: #856404;
}

.greenhouse-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-label {
  font-weight: bold;
  color: #7f8c8d;
  width: 100px;
  flex-shrink: 0;
}

.info-value {
  color: #2c3e50;
  flex: 1;
}

.greenhouse-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.edit-btn,
.status-btn,
.toggle-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.edit-btn { background: #3498db; color: white; }
.status-btn { background: #9b59b6; color: white; }
.toggle-btn { background: #e74c3c; color: white; }

.edit-btn:hover,
.status-btn:hover,
.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.add-first-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.cancel-btn:hover,
.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .greenhouse-grid {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .greenhouse-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style> 