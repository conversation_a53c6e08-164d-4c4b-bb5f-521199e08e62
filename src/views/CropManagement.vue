<template>
  <Layout ref="layoutRef">
    <div class="crop-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>🌱 작물 관리</h1>
        <p class="page-subtitle">재배 작물 정보를 관리합니다</p>
      </div>

      <!-- 페이지 에러 표시 -->
      <div v-if="pageError" class="page-error-container">
        <div class="error-card">
          <div class="error-icon">⚠️</div>
          <div class="error-content">
            <h3>페이지 로드 중 문제가 발생했습니다</h3>
            <p>{{ pageError }}</p>
            <button @click="retryPageLoad" class="retry-btn">🔄 다시 시도</button>
          </div>
        </div>
      </div>

      <!-- 시스템 상태 및 통계 -->
      <div v-else class="system-section">
        <div class="system-status">
          <h3>📊 작물 현황</h3>
          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon">🌱</div>
              <div class="status-info">
                <div class="status-label">총 작물</div>
                <div class="status-value">{{ statistics?.totalCrops || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📅</div>
              <div class="status-info">
                <div class="status-label">수확 예정</div>
                <div class="status-value">{{ statistics?.upcomingHarvests || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📊</div>
              <div class="status-info">
                <div class="status-label">카테고리별</div>
                <div class="status-value">{{ getCategoryBreakdown() }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 작물 관리 섹션 -->
      <div class="crop-section">
        <div class="section-header">
          <h3>📋 작물 목록</h3>
          <button @click="showAddModal = true" class="add-btn">
            <span>➕</span>
            새 작물 등록
          </button>
        </div>

        <!-- 필터 및 검색 -->
        <div class="filters">
          <div class="search-box">
            <input 
              v-model="searchKeyword" 
              type="text" 
              placeholder="작물명 또는 상품코드 검색..."
              class="search-input"
            >
            <span class="search-icon">🔍</span>
          </div>
          <select v-model="selectedCategory" class="category-select">
            <option value="">전체 카테고리</option>
            <option value="채소">채소</option>
            <option value="과일">과일</option>
            <option value="곡물">곡물</option>
            <option value="구황작물">구황작물</option>
          </select>
          <button @click="loadUpcomingHarvests" class="upcoming-btn">
            📅 수확 예정
          </button>
          <select v-model="selectedGreenhouse" @change="onGreenhouseChange" class="greenhouse-select">
            <option value="">전체 비닐하우스</option>
            <option v-for="greenhouse in availableGreenhouses" :key="greenhouse.id" :value="greenhouse.id">
              {{ greenhouse.greenhouseNumber }}
            </option>
          </select>
        </div>

        <!-- 에러 처리 컴포넌트 -->
        <ErrorHandler 
          v-if="hasError"
          :error="error"
          :can-retry="canRetry"
          :error-stats="errorStats"
          @retry="() => retry(loadCrops, 1000)"
          @close="clearError"
        />

        <!-- 로딩 상태 -->
        <div v-else-if="isLoading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>작물 목록을 불러오는 중...</p>
        </div>

        <!-- 작물 목록 -->
        <div v-if="!isLoading && !hasError && filteredCrops.length > 0" class="crop-grid">
          <div 
            v-for="crop in filteredCrops" 
            :key="crop.id"
            class="crop-card"
            :class="{ inactive: !crop.isActive }"
          >
            <div class="crop-header">
              <div class="crop-name">{{ crop.cropName }}</div>
              <div class="crop-code">{{ crop.productCode }}</div>
            </div>
            <div class="crop-info">
              <div class="info-row">
                <span class="info-label">카테고리:</span>
                <span class="info-value">{{ crop.category || '미분류' }}</span>
              </div>
              <div class="info-row" v-if="crop.plantingDate">
                <span class="info-label">재배 시작:</span>
                <span class="info-value">{{ formatDate(crop.plantingDate) }}</span>
              </div>
              <div class="info-row" v-if="crop.expectedHarvestDate">
                <span class="info-label">수확 예정:</span>
                <span class="info-value" :class="getHarvestDateClass(crop.expectedHarvestDate)">
                  {{ formatDate(crop.expectedHarvestDate) }}
                </span>
              </div>
              <div class="info-row" v-if="crop.harvestCycleDays">
                <span class="info-label">재배 기간:</span>
                <span class="info-value">{{ crop.harvestCycleDays }}일</span>
              </div>
              <div class="info-row" v-if="crop.description">
                <span class="info-label">설명:</span>
                <span class="info-value">{{ crop.description }}</span>
              </div>
              <div class="info-row" v-if="crop.greenhouses && crop.greenhouses.length > 0">
                <span class="info-label">할당 하우스:</span>
                <span class="info-value">{{ crop.greenhouses.map(g => g.greenhouseNumber).join(', ') }}</span>
              </div>
            </div>
            <div class="crop-actions">
              <button @click="editCrop(crop)" class="edit-btn">✏️ 수정</button>
              <button @click="assignGreenhouses(crop)" class="assign-btn">🏠 할당</button>
              <button @click="generateQRCode(crop)" class="qr-btn">📱 QR코드</button>
              <button @click="toggleCropStatus(crop)" class="toggle-btn">
                {{ crop.isActive ? '⏸️ 비활성화' : '▶️ 활성화' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 작물이 없을 때 -->
        <div v-if="!isLoading && !hasError && filteredCrops.length === 0" class="empty-state">
          <div class="empty-icon">🌱</div>
          <p>등록된 작물이 없습니다.</p>
          <button @click="showAddModal = true" class="add-first-btn">첫 번째 작물 등록하기</button>
        </div>
      </div>
    </div>

    <!-- 작물 등록/수정 모달 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? '작물 수정' : '새 작물 등록' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveCrop">
            <div class="form-group">
              <label>작물명 *</label>
              <select 
                v-model="cropForm.cropName" 
                required
                class="form-select"
                :disabled="loadingProductCodes"
                @change="onCropNameChange"
              >
                <option value="">작물명을 선택하세요</option>
                <option 
                  v-for="code in productCodes" 
                  :key="code" 
                  :value="code"
                >
                  {{ code }}
                </option>
              </select>
              <div v-if="loadingProductCodes" class="loading-text">작물 목록을 불러오는 중...</div>
            </div>
            <div class="form-group">
              <label>상품코드 *</label>
              <div class="code-input-group">
                <input 
                  v-model="cropForm.productCode" 
                  type="text" 
                  required
                  placeholder="자동 생성된 코드"
                  class="form-input"
                  :disabled="showEditModal"
                  readonly
                >
                <button 
                  v-if="!showEditModal" 
                  @click="generateProductCode" 
                  type="button" 
                  class="generate-btn"
                  :disabled="validating || !cropForm.cropName"
                >
                  <span v-if="validating" class="loading-spinner">🔄</span>
                  <span v-else>🎲 코드 생성</span>
                </button>
              </div>
              <small class="form-help">
                <strong>📝 사용법:</strong> 먼저 작물명을 선택한 후 "🎲 코드 생성" 버튼을 클릭하세요.
              </small>
            </div>
            <div class="form-group">
              <label>카테고리</label>
              <select v-model="cropForm.category" class="form-select">
                <option value="">카테고리 선택</option>
                <option value="채소">채소</option>
                <option value="과일">과일</option>
                <option value="곡물">곡물</option>
                <option value="구황작물">구황작물</option>
              </select>
            </div>
            <div class="form-group">
              <label>재배 시작일</label>
              <input 
                v-model="cropForm.plantingDate" 
                type="datetime-local" 
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>수확 예정일</label>
              <input 
                v-model="cropForm.expectedHarvestDate" 
                type="datetime-local" 
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>재배 기간 (일)</label>
              <input 
                v-model="cropForm.harvestCycleDays" 
                type="number" 
                min="1"
                placeholder="재배 기간을 일 단위로 입력"
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>설명</label>
              <textarea 
                v-model="cropForm.description" 
                placeholder="작물에 대한 설명을 입력하세요"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>
            <div class="form-group">
              <label>비닐하우스 할당</label>
              <div class="greenhouse-assignment">
                <div v-if="loadingGreenhouses" class="loading-text">비닐하우스 목록을 불러오는 중...</div>
                <div v-else class="greenhouse-checkboxes">
                  <label 
                    v-for="greenhouse in availableGreenhouses" 
                    :key="greenhouse.id"
                    class="greenhouse-checkbox"
                  >
                    <input 
                      type="checkbox" 
                      :value="greenhouse.id"
                      v-model="cropForm.greenhouseIds"
                    >
                    <span class="greenhouse-info">
                      <strong>{{ greenhouse.greenhouseNumber }}</strong>
                      <span class="greenhouse-status">{{ getStatusText(greenhouse.status) }}</span>
                      <span class="greenhouse-crop" v-if="greenhouse.currentCrop">
                        ({{ greenhouse.currentCrop }})
                      </span>
                    </span>
                  </label>
                </div>
                <div v-if="availableGreenhouses.length === 0" class="no-greenhouses">
                  등록된 비닐하우스가 없습니다.
                </div>
              </div>
            </div>
            <div class="form-actions">
              <button type="button" @click="closeModal" class="cancel-btn">취소</button>
              <button type="submit" class="save-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>{{ showEditModal ? '수정' : '등록' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 비닐하우스 할당 모달 -->
    <div v-if="showAssignModal" class="modal-overlay" @click="closeAssignModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>비닐하우스 할당 - {{ editingCrop?.cropName }}</h3>
          <button @click="closeAssignModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="greenhouse-list">
            <h4>할당할 비닐하우스 선택</h4>
            <div v-if="loadingGreenhouses" class="loading-text">비닐하우스 목록을 불러오는 중...</div>
            <div v-else class="greenhouse-checkboxes">
              <label 
                v-for="greenhouse in availableGreenhouses" 
                :key="greenhouse.id"
                class="greenhouse-checkbox"
              >
                <input 
                  type="checkbox" 
                  :value="greenhouse.id"
                  v-model="selectedGreenhouseIds"
                >
                <span class="greenhouse-info">
                  <strong>{{ greenhouse.greenhouseNumber }}</strong>
                  <span class="greenhouse-status">{{ getStatusText(greenhouse.status) }}</span>
                  <span class="greenhouse-crop" v-if="greenhouse.currentCrop">
                    ({{ greenhouse.currentCrop }})
                  </span>
                </span>
              </label>
            </div>
          </div>
          <div class="form-actions">
            <button @click="closeAssignModal" class="cancel-btn">취소</button>
            <button @click="saveGreenhouseAssignment" class="save-btn" :disabled="saving">
              <span v-if="saving" class="loading-spinner">🔄</span>
              <span v-else>할당</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- QR 코드 모달 -->
    <div v-if="showQRModal" class="modal-overlay" @click="closeQRModal">
      <div class="modal-content qr-modal" @click.stop>
        <div class="modal-header">
          <h3>QR 코드 - {{ editingCrop?.cropName }}</h3>
          <button @click="closeQRModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div v-if="qrCodeLoading" class="qr-loading">
            <div class="loading-spinner">🔄</div>
            <p>QR 코드를 생성하는 중...</p>
          </div>
          <div v-else-if="qrCodeData" class="qr-content">
            <img :src="qrCodeData.qrCode" alt="작물 QR 코드" class="qr-image">
            <div class="qr-info">
              <p><strong>작물명:</strong> {{ qrCodeData.crop?.cropName }}</p>
              <p><strong>상품코드:</strong> {{ qrCodeData.crop?.productCode }}</p>
            </div>
            <button @click="downloadQRCode" class="download-btn">📥 다운로드</button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import Layout from '../components/Layout.vue'
import apiClient from '@/utils/axios'
import { useApiError } from '@/composables/useApiError'
import ErrorHandler from '@/components/ErrorHandler.vue'

const layoutRef = ref(null)

// API 에러 처리
const { 
  error, 
  isLoading, 
  hasError, 
  canRetry, 
  errorStats,
  handleApiError, 
  retry, 
  clearError,
  setLoading 
} = useApiError()

// 상태 관리
const crops = ref([])
const statistics = ref({
  totalCrops: 0,
  upcomingHarvests: 0,
  categories: {}
})
const saving = ref(false)
const validating = ref(false)

// 필터 상태
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedGreenhouse = ref('')

// 모달 상태
const showAddModal = ref(false)
const showEditModal = ref(false)
const showAssignModal = ref(false)
const showQRModal = ref(false)
const editingCrop = ref(null)

// 비닐하우스 할당 관련
const availableGreenhouses = ref([])
const selectedGreenhouseIds = ref([])
const loadingGreenhouses = ref(false)

// QR 코드 관련
const qrCodeData = ref(null)
const qrCodeLoading = ref(false)

// 상품코드 목록
const productCodes = ref([])
const loadingProductCodes = ref(false)

// 페이지 에러 상태
const pageError = ref('')

// 폼 데이터
const cropForm = ref({
  cropName: '',
  productCode: '',
  category: '',
  description: '',
  plantingDate: '',
  expectedHarvestDate: '',
  harvestCycleDays: '',
  greenhouseIds: []
})

// 필터링된 작물 목록
const filteredCrops = computed(() => {
  let filtered = crops.value

  if (selectedCategory.value) {
    filtered = filtered.filter(crop => crop.category === selectedCategory.value)
  }

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(crop => 
      crop.cropName.toLowerCase().includes(keyword) ||
      crop.productCode.toLowerCase().includes(keyword) ||
      (crop.description && crop.description.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

// API 호출 함수들
const loadCrops = async () => {
  try {
    const response = await apiClient.get('/crops')
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success && Array.isArray(response.data.data)) {
      crops.value = response.data.data
      console.log('작물 목록 로드 완료:', crops.value.length, '개')
    } else {
      console.warn('작물 목록 API 응답 형식 오류:', response.data)
      crops.value = []
    }
  } catch (err) {
    handleApiError(err, '작물 목록 로드')
    crops.value = []
  }
}

const loadStatistics = async () => {
  try {
    const response = await apiClient.get('/crops/statistics')
    if (response.data && response.data.statistics) {
      statistics.value = response.data.statistics
    }
  } catch (err) {
    console.error('통계 로드 실패:', err)
  }
}

const calculateStatistics = () => {
  const totalCrops = crops.value.length
  const upcomingHarvests = crops.value.filter(crop => {
    if (!crop.expectedHarvestDate) return false
    const harvestDate = new Date(crop.expectedHarvestDate)
    const now = new Date()
    const diffTime = harvestDate - now
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays >= 0 && diffDays <= 7
  }).length
  
  const categories = {}
  crops.value.forEach(crop => {
    const category = crop.category || '미분류'
    categories[category] = (categories[category] || 0) + 1
  })
  
  statistics.value = {
    totalCrops,
    upcomingHarvests,
    categories
  }
}

const loadUpcomingHarvests = async () => {
  try {
    const response = await apiClient.get('/crops/upcoming-harvests')
    crops.value = response.data
  } catch (err) {
    handleApiError(err, '수확 예정 작물 로드')
    crops.value = []
  }
}

const loadCropsByGreenhouse = async (greenhouseId) => {
  try {
    const response = await apiClient.get(`/crops/greenhouse/${greenhouseId}`)
    crops.value = response.data
  } catch (err) {
    handleApiError(err, '비닐하우스별 작물 로드')
    crops.value = []
  }
}

const onGreenhouseChange = () => {
  if (selectedGreenhouse.value) {
    loadCropsByGreenhouse(selectedGreenhouse.value)
  } else {
    loadCrops()
  }
}

const loadGreenhouses = async () => {
  loadingGreenhouses.value = true
  try {
    const response = await apiClient.get('/greenhouses')
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success && Array.isArray(response.data.data)) {
      availableGreenhouses.value = response.data.data.filter(g => g.isActive)
    } else {
      availableGreenhouses.value = []
    }
  } catch (err) {
    console.error('비닐하우스 목록 로드 실패:', err)
    availableGreenhouses.value = []
  } finally {
    loadingGreenhouses.value = false
  }
}

const loadProductCodes = async () => {
  loadingProductCodes.value = true
  try {
    const response = await apiClient.get('/crops/product-codes')
    console.log('상품코드 API 응답:', response.data)
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success && Array.isArray(response.data.data)) {
      const codes = response.data.data
      productCodes.value = codes
      console.log('상품코드 목록 로드 완료:', productCodes.value.length, '개')
    } else {
      console.warn('상품코드 목록 API 응답 형식 오류:', response.data)
      throw new Error('API 응답 형식 오류')
    }
  } catch (err) {
    console.error('상품코드 목록 로드 실패:', err)
    // 에러 발생 시 명확한 에러 메시지와 함께 에러를 다시 던짐
    throw new Error(`상품코드 API 호출 실패: ${err.message}`)
  } finally {
    loadingProductCodes.value = false
  }
}

const generateProductCode = async () => {
  if (!cropForm.value.cropName) {
    alert('먼저 작물명을 선택해주세요.')
    return
  }
  
  validating.value = true
  try {
    const requestData = { cropName: cropForm.value.cropName }
    const response = await apiClient.post('/crops/generate-code', requestData)
    
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success) {
      cropForm.value.productCode = response.data.data || response.data.code
      console.log('상품코드 생성 완료:', cropForm.value.productCode)
    } else {
      alert('상품코드 생성에 실패했습니다: ' + (response.data.message || response.data.error))
    }
  } catch (err) {
    console.error('상품코드 생성 실패:', err)
    alert('상품코드 생성에 실패했습니다. 다시 시도해주세요.')
  } finally {
    validating.value = false
  }
}

const onCropNameChange = () => {
  console.log('작물명 변경됨:', cropForm.value.cropName)
}

// 유틸리티 함수들
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getHarvestDateClass = (dateString) => {
  if (!dateString) return ''
  const harvestDate = new Date(dateString)
  const now = new Date()
  const diffTime = harvestDate - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'overdue'
  if (diffDays <= 3) return 'urgent'
  if (diffDays <= 7) return 'soon'
  return ''
}

const getStatusText = (status) => {
  const statusMap = {
    'CULTIVATING': '재배중',
    'EMPTY': '비어있음',
    'MAINTENANCE': '정비중'
  }
  return statusMap[status] || status
}

const getCategoryBreakdown = () => {
  if (!statistics.value.categories) return '0/0/0/0'
  const { categories } = statistics.value
  return `${categories['채소'] || 0}/${categories['과일'] || 0}/${categories['곡물'] || 0}/${categories['구황작물'] || 0}`
}

// 페이지 다시 로드
const retryPageLoad = async () => {
  pageError.value = ''
  try {
    await loadCrops()
    await loadGreenhouses()
    await loadProductCodes()
  } catch (err) {
    pageError.value = err.message
  }
}

// 모달 관련 함수들
const editCrop = (crop) => {
  editingCrop.value = crop
  cropForm.value = {
    cropName: crop.cropName,
    productCode: crop.productCode,
    category: crop.category || '',
    description: crop.description || '',
    plantingDate: crop.plantingDate ? crop.plantingDate.slice(0, 16) : '',
    expectedHarvestDate: crop.expectedHarvestDate ? crop.expectedHarvestDate.slice(0, 16) : '',
    harvestCycleDays: crop.harvestCycleDays || ''
  }
  showEditModal.value = true
}

const assignGreenhouses = async (crop) => {
  // crop이 없거나 id가 없는 경우 처리
  if (!crop || !crop.id) {
    console.error('작물 정보가 없습니다:', crop)
    alert('작물 정보를 찾을 수 없습니다.')
    return
  }
  
  console.log('비닐하우스 할당 모달 열기:', {
    cropId: crop.id,
    cropName: crop.cropName,
    currentGreenhouses: crop.greenhouses
  })
  
  editingCrop.value = crop
  selectedGreenhouseIds.value = crop.greenhouses?.map(g => g.id) || []
  await loadGreenhouses()
  showAssignModal.value = true
}

const generateQRCode = async (crop) => {
  editingCrop.value = crop
  qrCodeLoading.value = true
  qrCodeData.value = null
  showQRModal.value = true
  
  try {
    const response = await apiClient.get(`/qrcode/crop/${crop.id}/detailed`)
    if (response.data.success) {
      qrCodeData.value = response.data
    }
  } catch (err) {
    console.error('QR 코드 생성 실패:', err)
    alert('QR 코드 생성에 실패했습니다.')
  } finally {
    qrCodeLoading.value = false
  }
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingCrop.value = null
  cropForm.value = {
    cropName: '',
    productCode: '',
    category: '',
    description: '',
    plantingDate: '',
    expectedHarvestDate: '',
    harvestCycleDays: '',
    greenhouseIds: []
  }
}

const closeAssignModal = () => {
  showAssignModal.value = false
  editingCrop.value = null
  selectedGreenhouseIds.value = []
}

const closeQRModal = () => {
  showQRModal.value = false
  editingCrop.value = null
  qrCodeData.value = null
}

// 저장 함수들
const saveCrop = async () => {
  saving.value = true
  
  try {
    const cropData = {
      cropName: cropForm.value.cropName,
      productCode: cropForm.value.productCode,
      category: cropForm.value.category,
      description: cropForm.value.description,
      plantingDate: cropForm.value.plantingDate,
      expectedHarvestDate: cropForm.value.expectedHarvestDate,
      harvestCycleDays: cropForm.value.harvestCycleDays
    }
    
    if (showEditModal.value) {
      const response = await apiClient.put(`/crops/${editingCrop.value.id}`, cropData)
      alert('작물이 성공적으로 수정되었습니다.')
    } else {
      const response = await apiClient.post('/crops', cropData)
      
      // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
      if (response.data.success && cropForm.value.greenhouseIds.length > 0) {
        const newCropId = response.data.data?.id || response.data.crop?.id
        if (newCropId) {
          try {
            await apiClient.post(`/crops/${newCropId}/assign-greenhouses`, cropForm.value.greenhouseIds)
            console.log('새 작물 비닐하우스 할당 완료:', newCropId)
          } catch (assignErr) {
            console.error('비닐하우스 할당 실패:', assignErr)
            alert('작물은 등록되었지만 비닐하우스 할당에 실패했습니다.')
          }
        } else {
          console.error('새 작물 ID를 찾을 수 없습니다:', response.data)
        }
      }
      
      alert('작물이 성공적으로 등록되었습니다.')
    }
    
    closeModal()
    await loadCrops()
    calculateStatistics()
  } catch (err) {
    console.error('작물 저장 실패:', err)
    const errorMsg = err.response?.data?.error || err.message
    alert('작물 저장에 실패했습니다: ' + errorMsg)
  } finally {
    saving.value = false
  }
}

const saveGreenhouseAssignment = async () => {
  // editingCrop이 없거나 id가 없는 경우 처리
  if (!editingCrop.value || !editingCrop.value.id) {
    console.error('작물 정보가 없습니다:', editingCrop.value)
    alert('작물 정보를 찾을 수 없습니다. 다시 시도해주세요.')
    return
  }
  
  saving.value = true
  
  try {
    console.log('비닐하우스 할당 시작:', {
      cropId: editingCrop.value.id,
      cropName: editingCrop.value.cropName,
      selectedGreenhouseIds: selectedGreenhouseIds.value
    })
    
    const response = await apiClient.post(`/crops/${editingCrop.value.id}/assign-greenhouses`, selectedGreenhouseIds.value)
    
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success) {
      const updatedCrop = response.data.data || response.data.crop
      const cropIndex = crops.value.findIndex(crop => crop.id === updatedCrop.id)
      if (cropIndex !== -1) {
        crops.value[cropIndex] = updatedCrop
      }
      
      alert('비닐하우스가 성공적으로 할당되었습니다.')
      closeAssignModal()
    } else {
      alert('비닐하우스 할당에 실패했습니다: ' + (response.data.message || response.data.error))
    }
  } catch (err) {
    console.error('비닐하우스 할당 실패:', err)
    const errorMsg = err.response?.data?.error || err.message
    alert('비닐하우스 할당에 실패했습니다: ' + errorMsg)
  } finally {
    saving.value = false
  }
}

const toggleCropStatus = async (crop) => {
  try {
    await apiClient.delete(`/crops/${crop.id}`)
    await loadCrops()
    calculateStatistics()
  } catch (err) {
    console.error('작물 상태 변경 실패:', err)
    alert('작물 상태 변경에 실패했습니다.')
  }
}

const downloadQRCode = () => {
  if (!qrCodeData.value?.qrCode) return
  
  const link = document.createElement('a')
  link.href = qrCodeData.value.qrCode
  link.download = `${editingCrop.value.cropName}_QR코드.png`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 컴포넌트 마운트 시 데이터 로드
onMounted(async () => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/crop')
  }
  
  try {
    await loadCrops()
    await loadGreenhouses()
    await loadProductCodes()
  } catch (err) {
    console.error('작물 관리 페이지 초기화 실패:', err)
    pageError.value = err.message
  }
})
</script>

<style scoped>
.crop-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.page-error-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.error-card {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.error-content {
  flex: 1;
  text-align: center;
}

.error-content h3 {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 1.3rem;
}

.error-content p {
  margin: 0 0 15px 0;
  color: #856404;
  font-size: 1rem;
  line-height: 1.5;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e67e22;
  transform: translateY(-2px);
}

.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.crop-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.category-select,
.greenhouse-select,
.upcoming-btn {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 150px;
  cursor: pointer;
}

.upcoming-btn {
  background: #3498db;
  color: white;
  border: none;
}

.upcoming-btn:hover {
  background: #2980b9;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.crop-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.crop-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.crop-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.crop-card.inactive {
  opacity: 0.6;
}

.crop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.crop-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #2c3e50;
}

.crop-code {
  font-size: 0.9rem;
  color: #7f8c8d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.crop-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-label {
  font-weight: bold;
  color: #7f8c8d;
  width: 100px;
  flex-shrink: 0;
}

.info-value {
  color: #2c3e50;
  flex: 1;
}

.info-value.urgent {
  color: #e74c3c;
  font-weight: bold;
}

.info-value.soon {
  color: #f39c12;
  font-weight: bold;
}

.info-value.overdue {
  color: #e74c3c;
  text-decoration: line-through;
}

.crop-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.edit-btn,
.assign-btn,
.qr-btn,
.toggle-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.edit-btn { background: #3498db; color: white; }
.assign-btn { background: #9b59b6; color: white; }
.qr-btn { background: #f39c12; color: white; }
.toggle-btn { background: #e74c3c; color: white; }

.edit-btn:hover,
.assign-btn:hover,
.qr-btn:hover,
.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state p {
  color: #2c3e50;
  font-size: 1.2rem;
  margin: 10px 0;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.add-first-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.qr-modal {
  max-width: 500px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.code-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 5px;
  background: #f8f9fa;
}

.code-input-group .form-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 10px;
}

.generate-btn {
  padding: 12px 20px;
  background: #9b59b6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 120px;
}

.generate-btn:hover:not(:disabled) {
  background: #8e44ad;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.generate-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.form-help {
  color: #7f8c8d;
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.cancel-btn:hover,
.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.greenhouse-list h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.greenhouse-assignment {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  background: #f8f9fa;
}

.no-greenhouses {
  text-align: center;
  color: #7f8c8d;
  padding: 20px;
  font-style: italic;
}

.loading-text {
  text-align: center;
  color: #7f8c8d;
  padding: 20px;
}

.greenhouse-checkboxes {
  max-height: 300px;
  overflow-y: auto;
}

.greenhouse-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.greenhouse-checkbox:hover {
  background: #f8f9fa;
}

.greenhouse-checkbox input[type="checkbox"] {
  margin: 0;
}

.greenhouse-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.greenhouse-status {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.greenhouse-crop {
  font-size: 0.8rem;
  color: #27ae60;
}

.qr-loading {
  text-align: center;
  padding: 40px;
}

.qr-content {
  text-align: center;
}

.qr-image {
  max-width: 100%;
  height: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 20px;
}

.qr-info {
  margin-bottom: 20px;
}

.qr-info p {
  margin: 5px 0;
  color: #2c3e50;
}

.download-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
}

.download-btn:hover {
  background: #2980b9;
}

@media (max-width: 768px) {
  .crop-grid {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .crop-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .code-input-group {
    flex-direction: column;
  }
}
</style> 