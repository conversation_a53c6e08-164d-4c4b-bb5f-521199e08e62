<template>
  <Layout ref="layoutRef">
    <div class="crop-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>🌱 작물 관리</h1>
        <p class="page-subtitle">재배 작물 정보를 관리합니다</p>
      </div>

      <!-- 페이지 에러 표시 -->
      <div v-if="pageError" class="page-error-container">
        <div class="error-card">
          <div class="error-icon">⚠️</div>
          <div class="error-content">
            <h3>페이지 로드 중 문제가 발생했습니다</h3>
            <p>{{ pageError }}</p>
            <button @click="retryPageLoad" class="retry-btn">🔄 다시 시도</button>
          </div>
        </div>
      </div>

      <!-- 시스템 상태 및 통계 -->
      <div v-else class="system-section">
        <div class="system-status">
          <h3>📊 작물 현황</h3>
          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon">🌱</div>
              <div class="status-info">
                <div class="status-label">총 작물</div>
                <div class="status-value">{{ statistics?.totalCrops || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📅</div>
              <div class="status-info">
                <div class="status-label">수확 예정</div>
                <div class="status-value">{{ statistics?.upcomingHarvests || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📊</div>
              <div class="status-info">
                <div class="status-label">카테고리별</div>
                <div class="status-value">{{ getCategoryBreakdown() }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 작물 관리 섹션 -->
      <div class="crop-section">
        <div class="section-header">
          <h3>📋 작물 목록</h3>
          <button @click="openAddModal" class="add-btn">
            <span>➕</span>
            새 작물 등록
          </button>
        </div>

        <!-- 필터 및 검색 -->
        <div class="filters">
          <div class="search-box">
            <input 
              v-model="searchKeyword" 
              type="text" 
              placeholder="작물명 또는 상품코드 검색..."
              class="search-input"
            >
            <span class="search-icon">🔍</span>
          </div>
          <select v-model="selectedCategory" class="category-select">
            <option value="">전체 카테고리</option>
            <option value="채소">채소</option>
            <option value="과일">과일</option>
            <option value="곡물">곡물</option>
            <option value="구황작물">구황작물</option>
          </select>
          <button @click="loadUpcomingHarvests" class="upcoming-btn">
            📅 수확 예정
          </button>
          <select v-model="selectedGreenhouse" @change="onGreenhouseFilterChange" class="greenhouse-select">
            <option value="">전체 비닐하우스</option>
            <option v-for="greenhouse in availableGreenhouses" :key="greenhouse.id" :value="greenhouse.id">
              {{ greenhouse.greenhouseNumber }}
            </option>
          </select>
        </div>

        <!-- 에러 처리 컴포넌트 -->
        <ErrorHandler 
          v-if="composableError"
          :error="composableError"
          :can-retry="composableError.canRetry"
          :error-stats="composableError.errorStats"
          @retry="() => retry(loadCrops, 1000)"
          @close="clearError"
        />

        <!-- 로딩 상태 -->
        <div v-else-if="loading.isLoading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>작물 목록을 불러오는 중...</p>
        </div>

        <!-- 작물 목록 -->
        <div v-if="!loading.isLoading && !composableError && filteredCrops.length > 0" class="crop-grid">
          <div 
            v-for="crop in filteredCrops" 
            :key="crop.id"
            class="crop-card"
            :class="{ inactive: !crop.isActive }"
          >
            <div class="crop-header">
              <div class="crop-name">{{ crop.cropName }}</div>
              <div class="crop-code">{{ crop.productCode }}</div>
            </div>
            <div class="crop-info">
              <div class="info-row">
                <span class="info-label">카테고리:</span>
                <span class="info-value">{{ crop.category || '미분류' }}</span>
              </div>
              <div class="info-row" v-if="crop.plantingDate">
                <span class="info-label">재배 시작:</span>
                <span class="info-value">{{ formatDate(crop.plantingDate) }}</span>
              </div>
              <div class="info-row" v-if="crop.expectedHarvestDate">
                <span class="info-label">수확 예정:</span>
                <span class="info-value" :class="getHarvestDateClass(crop.expectedHarvestDate)">
                  {{ formatDate(crop.expectedHarvestDate) }}
                </span>
              </div>
              <div class="info-row" v-if="crop.harvestCycleDays">
                <span class="info-label">재배 기간:</span>
                <span class="info-value">{{ crop.harvestCycleDays }}일</span>
              </div>
              <div class="info-row" v-if="crop.description">
                <span class="info-label">설명:</span>
                <span class="info-value">{{ crop.description }}</span>
              </div>
              <div class="info-row" v-if="crop.greenhouses && crop.greenhouses.length > 0">
                <span class="info-label">할당 하우스:</span>
                <span class="info-value">{{ crop.greenhouses.map(g => g.greenhouseNumber).join(', ') }}</span>
              </div>
            </div>
            <div class="crop-actions">
              <button @click="openEditModal(crop)" class="edit-btn">✏️ 수정</button>
              <button @click="openAssignModal(crop)" class="assign-btn">🏠 할당</button>
              <button @click="handleGenerateQRCode(crop)" class="qr-btn">📱 QR코드</button>
              <button @click="handleToggleStatus(crop)" class="toggle-btn">
                {{ crop.isActive ? '⏸️ 비활성화' : '▶️ 활성화' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 작물이 없을 때 -->
        <div v-if="!loading.isLoading && !composableError && filteredCrops.length === 0" class="empty-state">
          <div class="empty-icon">🌱</div>
          <p>등록된 작물이 없습니다.</p>
          <button @click="openAddModal" class="add-first-btn">첫 번째 작물 등록하기</button>
        </div>
      </div>
    </div>

    <!-- 작물 등록/수정 모달 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? '작물 수정' : '새 작물 등록' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="handleSaveCrop">
            <div class="form-group">
              <label>작물명 *</label>
              <select 
                v-model="cropForm.cropName" 
                required
                class="form-select"
                :disabled="loading.isLoading"
                @change="handleGenerateCode"
              >
                <option value="">작물명을 선택하세요</option>
                <option 
                  v-for="code in productCodes" 
                  :key="code" 
                  :value="code"
                >
                  {{ code }}
                </option>
              </select>
              <div v-if="loading.isLoading" class="loading-text">작물 목록을 불러오는 중...</div>
            </div>
            <div class="form-group">
              <label>상품코드 *</label>
              <div class="code-input-group">
                <input 
                  v-model="cropForm.productCode" 
                  type="text" 
                  required
                  placeholder="자동 생성된 코드"
                  class="form-input"
                  :disabled="showEditModal"
                  readonly
                >
                <button 
                  v-if="!showEditModal" 
                  @click="handleGenerateCode" 
                  type="button" 
                  class="generate-btn"
                  :disabled="validatingCode || !cropForm.cropName"
                >
                  <span v-if="validatingCode" class="loading-spinner">🔄</span>
                  <span v-else>🎲 코드 생성</span>
                </button>
              </div>
              <small class="form-help">
                <strong>📝 사용법:</strong> 먼저 작물명을 선택한 후 "🎲 코드 생성" 버튼을 클릭하세요.
              </small>
            </div>
            <div class="form-group">
              <label>카테고리</label>
              <select v-model="cropForm.category" class="form-select">
                <option value="">카테고리 선택</option>
                <option value="채소">채소</option>
                <option value="과일">과일</option>
                <option value="곡물">곡물</option>
                <option value="구황작물">구황작물</option>
              </select>
            </div>
            <div class="form-group">
              <label>재배 시작일</label>
              <input 
                v-model="cropForm.plantingDate" 
                type="datetime-local" 
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>수확 예정일</label>
              <input 
                v-model="cropForm.expectedHarvestDate" 
                type="datetime-local" 
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>재배 기간 (일)</label>
              <input 
                v-model="cropForm.harvestCycleDays" 
                type="number" 
                min="1"
                placeholder="재배 기간을 일 단위로 입력"
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>설명</label>
              <textarea 
                v-model="cropForm.description" 
                placeholder="작물에 대한 설명을 입력하세요"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>
            <div class="form-group">
              <label>비닐하우스 할당</label>
              <div class="greenhouse-assignment">
                <div v-if="loading.isLoading" class="loading-text">비닐하우스 목록을 불러오는 중...</div>
                <div v-else class="greenhouse-checkboxes">
                  <label 
                    v-for="greenhouse in availableGreenhouses" 
                    :key="greenhouse.id"
                    class="greenhouse-checkbox"
                  >
                    <input 
                      type="checkbox" 
                      :value="greenhouse.id"
                      v-model="selectedGreenhouseIds"
                    >
                    <span class="greenhouse-info">
                      <strong>{{ greenhouse.greenhouseNumber }}</strong>
                      <span class="greenhouse-status">{{ getStatusText(greenhouse.status) }}</span>
                      <span class="greenhouse-crop" v-if="greenhouse.currentCrop">
                        ({{ greenhouse.currentCrop }})
                      </span>
                    </span>
                  </label>
                </div>
                <div v-if="availableGreenhouses.length === 0" class="no-greenhouses">
                  등록된 비닐하우스가 없습니다.
                </div>
              </div>
            </div>
            <div class="form-actions">
              <button type="button" @click="closeModal" class="cancel-btn">취소</button>
              <button type="submit" class="save-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>{{ showEditModal ? '수정' : '등록' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 비닐하우스 할당 모달 -->
    <div v-if="showAssignModal" class="modal-overlay" @click="closeAssignModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>비닐하우스 할당 - {{ editingCrop?.cropName }}</h3>
          <button @click="closeAssignModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div class="greenhouse-list">
            <h4>할당할 비닐하우스 선택</h4>
            <div v-if="loading.isLoading" class="loading-text">비닐하우스 목록을 불러오는 중...</div>
            <div v-else class="greenhouse-checkboxes">
              <label 
                v-for="greenhouse in availableGreenhouses" 
                :key="greenhouse.id"
                class="greenhouse-checkbox"
              >
                <input 
                  type="checkbox" 
                  :value="greenhouse.id"
                  v-model="selectedGreenhouseIds"
                >
                <span class="greenhouse-info">
                  <strong>{{ greenhouse.greenhouseNumber }}</strong>
                  <span class="greenhouse-status">{{ getStatusText(greenhouse.status) }}</span>
                  <span class="greenhouse-crop" v-if="greenhouse.currentCrop">
                    ({{ greenhouse.currentCrop }})
                  </span>
                </span>
              </label>
            </div>
          </div>
          <div class="form-actions">
            <button @click="closeAssignModal" class="cancel-btn">취소</button>
            <button @click="handleAssignGreenhouses" class="save-btn" :disabled="saving">
              <span v-if="saving" class="loading-spinner">🔄</span>
              <span v-else>할당</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- QR 코드 모달 -->
    <div v-if="showQRModal" class="modal-overlay" @click="closeQRModal">
      <div class="modal-content qr-modal" @click.stop>
        <div class="modal-header">
          <h3>QR 코드 - {{ editingCrop?.cropName }}</h3>
          <button @click="closeQRModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div v-if="qrCodeLoading" class="qr-loading">
            <div class="loading-spinner">🔄</div>
            <p>QR 코드를 생성하는 중...</p>
          </div>
          <div v-else-if="qrCodeData" class="qr-content">
            <img :src="qrCodeData.qrCode" alt="작물 QR 코드" class="qr-image">
            <div class="qr-info">
              <p><strong>작물명:</strong> {{ qrCodeData.crop?.cropName }}</p>
              <p><strong>상품코드:</strong> {{ qrCodeData.crop?.productCode }}</p>
            </div>
            <button @click="downloadQRCode" class="download-btn">📥 다운로드</button>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import Layout from '../components/Layout.vue'
import ErrorHandler from '@/components/ErrorHandler.vue'
import { useCropManagement } from '@/composables/useCropManagement'
import apiClient from '@/utils/axios' // QR코드, 상품코드 생성 등 일부 직접호출용

const layoutRef = ref(null)

// Composable
const {
  crops,
  statistics,
  availableGreenhouses,
  productCodes,
  loading,
  saving,
  error: composableError,
  loadCrops,
  loadGreenhouses,
  loadProductCodes,
  saveCrop: saveCropApi,
  toggleCropStatus: toggleCropStatusApi,
  assignGreenhousesToCrop
} = useCropManagement()

// 로컬 상태
const pageError = ref('')
const searchKeyword = ref('')
const selectedCategory = ref('')
const selectedGreenhouse = ref('')

// 모달 상태
const showAddModal = ref(false)
const showEditModal = ref(false)
const showAssignModal = ref(false)
const showQRModal = ref(false)
const editingCrop = ref(null)

// 폼 상태
const cropForm = ref({})
const selectedGreenhouseIds = ref([])
const validatingCode = ref(false)

// QR 코드 상태
const qrCodeData = ref(null)
const qrCodeLoading = ref(false)


// 계산된 속성
const filteredCrops = computed(() => {
  return crops.value.filter(crop => {
    const keywordMatch = !searchKeyword.value || 
      crop.cropName.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      crop.productCode.toLowerCase().includes(searchKeyword.value.toLowerCase())
    
    const categoryMatch = !selectedCategory.value || crop.category === selectedCategory.value

    // 비닐하우스 필터링은 현재 프론트에서 처리. 백엔드 지원 시 변경 가능.
    const greenhouseMatch = !selectedGreenhouse.value || 
      crop.greenhouses?.some(g => g.id === selectedGreenhouse.value)

    return keywordMatch && categoryMatch && greenhouseMatch
  })
})

const getCategoryBreakdown = () => {
    const { categories } = statistics.value;
    if (!categories) return '0/0/0/0';
    return `${categories['채소'] || 0}/${categories['과일'] || 0}/${categories['곡물'] || 0}/${categories['구황작물'] || 0}`;
}


// 유틸리티 함수
const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('ko-KR')
}

const getHarvestDateClass = (dateString) => {
  if (!dateString) return ''
  const diffDays = (new Date(dateString) - new Date()) / (1000 * 60 * 60 * 24)
  if (diffDays < 0) return 'overdue'
  if (diffDays <= 3) return 'urgent'
  if (diffDays <= 7) return 'soon'
  return ''
}

const getStatusText = (status) => {
  const statusMap = { 'CULTIVATING': '재배중', 'EMPTY': '비어있음', 'MAINTENANCE': '정비중' }
  return statusMap[status] || status
}


// 폼 및 모달 핸들러
const resetForm = () => {
  cropForm.value = {
    cropName: '',
    productCode: '',
    category: '채소',
    description: '',
    plantingDate: new Date().toISOString().slice(0, 10),
    expectedHarvestDate: '',
    harvestCycleDays: 90,
    greenhouseIds: []
  }
}

const openAddModal = () => {
    resetForm();
    showAddModal.value = true;
}

const openEditModal = (crop) => {
  editingCrop.value = crop;
  cropForm.value = { ...crop };
  showEditModal.value = true;
}

const openAssignModal = (crop) => {
  editingCrop.value = crop;
  selectedGreenhouseIds.value = crop.greenhouses?.map(g => g.id) || [];
  showAssignModal.value = true;
}

const closeModal = () => {
  showAddModal.value = false;
  showEditModal.value = false;
  editingCrop.value = null;
}

const closeAssignModal = () => {
  showAssignModal.value = false;
  editingCrop.value = null;
  selectedGreenhouseIds.value = [];
}

const closeQRModal = () => {
    showQRModal.value = false;
    qrCodeData.value = null;
    editingCrop.value = null;
}

// 데이터 조작 핸들러
const handleSaveCrop = async () => {
  const isEdit = !!editingCrop.value;
  const newCrop = await saveCropApi(cropForm.value, editingCrop.value?.id);
  
  if (newCrop) {
    if (!isEdit && cropForm.value.greenhouseIds?.length > 0) {
      await assignGreenhousesToCrop(newCrop.id, cropForm.value.greenhouseIds);
    }
    alert(`작물이 성공적으로 ${isEdit ? '수정' : '등록'}되었습니다.`);
    closeModal();
  } else {
    alert('작물 저장에 실패했습니다.');
  }
}

const handleAssignGreenhouses = async () => {
    if(!editingCrop.value) return;
    const success = await assignGreenhousesToCrop(editingCrop.value.id, selectedGreenhouseIds.value);
    if(success) {
        alert('비닐하우스가 할당되었습니다.');
        closeAssignModal();
    } else {
        alert('비닐하우스 할당에 실패했습니다.');
    }
}

const handleToggleStatus = async (crop) => {
    if (confirm(`'${crop.cropName}' 작물을 ${crop.isActive ? '비활성화' : '활성화'} 하시겠습니까?`)) {
        await toggleCropStatusApi(crop.id);
    }
}

const handleGenerateCode = async () => {
    if (!cropForm.value.cropName) {
        alert('먼저 작물명을 선택해주세요.');
        return;
    }
    validatingCode.value = true;
    try {
        const response = await apiClient.post('/crops/generate-code', { cropName: cropForm.value.cropName });
        if (response.data.success) {
            cropForm.value.productCode = response.data.data;
        } else {
            alert('상품코드 생성 실패: ' + response.data.message);
        }
    } catch (err) {
        alert('상품코드 생성 중 오류 발생');
    } finally {
        validatingCode.value = false;
    }
}

const handleGenerateQRCode = async (crop) => {
    editingCrop.value = crop;
    qrCodeLoading.value = true;
    showQRModal.value = true;
    try {
        const response = await apiClient.get(`/qrcode/crop/${crop.id}/detailed`);
        if (response.data.success) {
            qrCodeData.value = response.data;
        } else {
            throw new Error('QR 데이터 로드 실패');
        }
    } catch (err) {
        alert('QR코드 생성에 실패했습니다.');
        closeQRModal();
    } finally {
        qrCodeLoading.value = false;
    }
}

const downloadQRCode = () => {
  if (!qrCodeData.value?.qrCode) return;
  const link = document.createElement('a');
  link.href = qrCodeData.value.qrCode;
  link.download = `${editingCrop.value.cropName}_QR.png`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
}

const onGreenhouseFilterChange = () => {
    // 이 기능은 현재 프론트엔드 computed에서 처리.
    // 데이터가 많아지면 백엔드 API 호출 방식으로 변경해야 함.
    // 예: loadCrops({ greenhouseId: selectedGreenhouse.value })
}

// 초기화
const initializePage = async () => {
    pageError.value = '';
    try {
        await Promise.all([
            loadCrops(),
            loadGreenhouses(),
            loadProductCodes()
        ]);
    } catch (err) {
        pageError.value = '페이지 초기화 중 오류가 발생했습니다. 잠시 후 다시 시도해주세요.';
    }
}

onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/crop');
  }
  initializePage();
});

watch(composableError, (newError) => {
    if(newError) {
        // useApiError composable과 연동할 경우 여기에 로직 추가
        console.error("Composable Error:", newError);
    }
})
</script>

<style scoped>
.crop-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.page-error-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.error-card {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.error-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.error-content {
  flex: 1;
  text-align: center;
}

.error-content h3 {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 1.3rem;
}

.error-content p {
  margin: 0 0 15px 0;
  color: #856404;
  font-size: 1rem;
  line-height: 1.5;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e67e22;
  transform: translateY(-2px);
}

.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.crop-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.category-select,
.greenhouse-select,
.upcoming-btn {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 150px;
  cursor: pointer;
}

.upcoming-btn {
  background: #3498db;
  color: white;
  border: none;
}

.upcoming-btn:hover {
  background: #2980b9;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.crop-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.crop-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.crop-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.crop-card.inactive {
  opacity: 0.6;
}

.crop-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.crop-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #2c3e50;
}

.crop-code {
  font-size: 0.9rem;
  color: #7f8c8d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.crop-info {
  margin-bottom: 20px;
}

.info-row {
  display: flex;
  margin-bottom: 8px;
}

.info-label {
  font-weight: bold;
  color: #7f8c8d;
  width: 100px;
  flex-shrink: 0;
}

.info-value {
  color: #2c3e50;
  flex: 1;
}

.info-value.urgent {
  color: #e74c3c;
  font-weight: bold;
}

.info-value.soon {
  color: #f39c12;
  font-weight: bold;
}

.info-value.overdue {
  color: #e74c3c;
  text-decoration: line-through;
}

.crop-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.edit-btn,
.assign-btn,
.qr-btn,
.toggle-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.edit-btn { background: #3498db; color: white; }
.assign-btn { background: #9b59b6; color: white; }
.qr-btn { background: #f39c12; color: white; }
.toggle-btn { background: #e74c3c; color: white; }

.edit-btn:hover,
.assign-btn:hover,
.qr-btn:hover,
.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-state p {
  color: #2c3e50;
  font-size: 1.2rem;
  margin: 10px 0;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.add-first-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
}

.qr-modal {
  max-width: 500px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.code-input-group {
  display: flex;
  gap: 10px;
  align-items: center;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 5px;
  background: #f8f9fa;
}

.code-input-group .form-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 10px;
}

.generate-btn {
  padding: 12px 20px;
  background: #9b59b6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: bold;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 120px;
}

.generate-btn:hover:not(:disabled) {
  background: #8e44ad;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.generate-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.form-help {
  color: #7f8c8d;
  font-size: 0.85rem;
  margin-top: 5px;
  display: block;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.cancel-btn:hover,
.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.greenhouse-list h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.greenhouse-assignment {
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 15px;
  background: #f8f9fa;
}

.no-greenhouses {
  text-align: center;
  color: #7f8c8d;
  padding: 20px;
  font-style: italic;
}

.loading-text {
  text-align: center;
  color: #7f8c8d;
  padding: 20px;
}

.greenhouse-checkboxes {
  max-height: 300px;
  overflow-y: auto;
}

.greenhouse-checkbox {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.greenhouse-checkbox:hover {
  background: #f8f9fa;
}

.greenhouse-checkbox input[type="checkbox"] {
  margin: 0;
}

.greenhouse-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.greenhouse-status {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.greenhouse-crop {
  font-size: 0.8rem;
  color: #27ae60;
}

.qr-loading {
  text-align: center;
  padding: 40px;
}

.qr-content {
  text-align: center;
}

.qr-image {
  max-width: 100%;
  height: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 20px;
}

.qr-info {
  margin-bottom: 20px;
}

.qr-info p {
  margin: 5px 0;
  color: #2c3e50;
}

.download-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
}

.download-btn:hover {
  background: #2980b9;
}

@media (max-width: 768px) {
  .crop-grid {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .crop-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
  
  .code-input-group {
    flex-direction: column;
  }
}
</style> 