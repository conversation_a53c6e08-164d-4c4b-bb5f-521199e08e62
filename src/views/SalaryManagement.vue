<template>
  <Layout ref="layoutRef">
    <div class="salary-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>💰 급여 관리</h1>
        <p class="page-subtitle">직원 급여 계산 및 관리</p>
      </div>

      <!-- 시스템 상태 및 통계 -->
      <div class="system-section">
        <div class="system-status">
          <h3>📊 급여 현황</h3>
          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon">👥</div>
              <div class="status-info">
                <div class="status-label">총 직원</div>
                <div class="status-value">{{ monthlyStatus?.totalEmployees || 0 }}명</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">💰</div>
              <div class="status-info">
                <div class="status-label">총 기본급</div>
                <div class="status-value">{{ formatCurrency(monthlyStatus?.totalBaseSalary || 0) }}</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📈</div>
              <div class="status-info">
                <div class="status-label">예상 총 지급</div>
                <div class="status-value">{{ formatCurrency(monthlyStatus?.estimatedTotalPay || 0) }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 급여 관리 섹션 -->
      <div class="salary-section">
        <div class="section-header">
          <h3>📋 급여 계산</h3>
          <div class="header-actions">
            <button @click="loadSalaryAlerts" class="alert-btn">
              🔔 급여일 알림
            </button>
          </div>
        </div>

        <!-- 직원 선택 -->
        <div class="employee-selection">
          <div class="selection-header">
            <h4>직원 선택</h4>
            <button @click="loadAllEmployees" class="load-all-btn">전체 직원 로드</button>
          </div>
          <div class="employee-list">
            <div 
              v-for="employee in employees" 
              :key="employee.id"
              class="employee-item"
              :class="{ selected: selectedEmployee?.id === employee.id }"
              @click="selectEmployee(employee)"
            >
              <div class="employee-info">
                <div class="employee-name">{{ employee.name }}</div>
                <div class="employee-details">
                  {{ employee.department }} | {{ employee.position }} | {{ formatCurrency(employee.baseSalary) }}
                </div>
              </div>
              <button @click="calculateSalary(employee)" class="calculate-btn">계산</button>
            </div>
          </div>
        </div>

        <!-- 급여 입력 폼 -->
        <div v-if="selectedEmployee" class="salary-input-form">
          <h4>급여 정보 입력</h4>
          <div class="form-card">
            <div class="form-header">
              <div class="employee-info">
                <div class="employee-name">{{ selectedEmployee.name }}</div>
                <div class="employee-number">{{ selectedEmployee.employeeNumber }}</div>
              </div>
              <div class="period-selector">
                <select v-model="selectedYear" class="year-select">
                  <option v-for="year in availableYears" :key="year" :value="year">{{ year }}년</option>
                </select>
                <select v-model="selectedMonth" class="month-select">
                  <option v-for="month in 12" :key="month" :value="month">{{ month }}월</option>
                </select>
              </div>
            </div>
            
            <div class="form-grid">
              <!-- 기본 정보 -->
              <div class="form-group">
                <label>기본급</label>
                <input 
                  v-model="salaryForm.baseSalary" 
                  type="number" 
                  class="form-input"
                  readonly
                >
              </div>
              
              <!-- 근무 시간 입력 -->
              <div class="form-group">
                <label>초과근무 시간</label>
                <input 
                  v-model="salaryForm.overtimeHours" 
                  type="number" 
                  min="0"
                  step="0.5"
                  class="form-input"
                  placeholder="시간"
                >
              </div>
              
              <div class="form-group">
                <label>야간근무 시간</label>
                <input 
                  v-model="salaryForm.nightShiftHours" 
                  type="number" 
                  min="0"
                  step="0.5"
                  class="form-input"
                  placeholder="시간"
                >
              </div>
              
              <div class="form-group">
                <label>휴일근무 시간</label>
                <input 
                  v-model="salaryForm.holidayHours" 
                  type="number" 
                  min="0"
                  step="0.5"
                  class="form-input"
                  placeholder="시간"
                >
              </div>
              
              <!-- 추가 급여 -->
              <div class="form-group">
                <label>상여금</label>
                <input 
                  v-model="salaryForm.bonusAmount" 
                  type="number" 
                  min="0"
                  class="form-input"
                  placeholder="금액"
                >
              </div>
              
              <div class="form-group">
                <label>공제액</label>
                <input 
                  v-model="salaryForm.deductionAmount" 
                  type="number" 
                  min="0"
                  class="form-input"
                  placeholder="금액"
                >
              </div>
            </div>
            
            <div class="form-actions">
              <button @click="calculateSalaryFromForm" class="calculate-btn">급여 계산</button>
              <button @click="saveSalary" class="save-btn">급여 저장</button>
            </div>
          </div>
        </div>

        <!-- 급여 계산 결과 -->
        <div v-if="salaryResult" class="salary-result">
          <h4>급여 계산 결과</h4>
          <div class="result-card">
            <div class="result-header">
              <div class="employee-info">
                <div class="employee-name">{{ salaryResult.employeeName }}</div>
                <div class="employee-number">{{ salaryResult.employeeNumber }}</div>
              </div>
              <div class="period-info">
                {{ salaryResult.year }}년 {{ salaryResult.month }}월
              </div>
            </div>
            <div class="salary-breakdown">
              <div class="breakdown-item">
                <span class="item-label">기본급:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.baseSalary) }}</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">초과근무:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.overtimePay) }}</span>
                <span class="item-detail">({{ salaryResult.overtimeHours }}시간)</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">야간근무:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.nightShiftPay) }}</span>
                <span class="item-detail">({{ salaryResult.nightShiftHours }}시간)</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">휴일근무:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.holidayPay) }}</span>
                <span class="item-detail">({{ salaryResult.holidayHours }}시간)</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">상여금:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.bonusAmount) }}</span>
              </div>
              <div class="breakdown-item total">
                <span class="item-label">총 급여:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.totalSalary) }}</span>
              </div>
              <div class="breakdown-item">
                <span class="item-label">공제액:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.deductionAmount) }}</span>
              </div>
              <div class="breakdown-item net">
                <span class="item-label">실수령액:</span>
                <span class="item-value">{{ formatCurrency(salaryResult.netSalary) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 급여 알림 -->
        <div v-if="salaryAlerts.length > 0" class="salary-alerts">
          <h4>급여일 알림</h4>
          <div class="alerts-list">
            <div 
              v-for="alert in salaryAlerts" 
              :key="alert.id"
              class="alert-item"
              :class="alert.type"
            >
              <div class="alert-icon">
                {{ alert.type === 'urgent' ? '🚨' : '⚠️' }}
              </div>
              <div class="alert-content">
                <div class="alert-title">{{ alert.title }}</div>
                <div class="alert-message">{{ alert.message }}</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 로딩 상태 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>데이터를 불러오는 중...</p>
        </div>

        <!-- 에러 상태 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ error }}</p>
          <button @click="loadMonthlyStatus" class="retry-btn">다시 시도</button>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import Layout from '../components/Layout.vue'
import apiClient from '@/utils/axios'

const layoutRef = ref(null)

// 상태 관리
const employees = ref([])
const monthlyStatus = ref({})
const salaryResult = ref(null)
const salaryAlerts = ref([])
const loading = ref(false)
const error = ref('')

// 선택 상태
const selectedEmployee = ref(null)
const selectedYear = ref(new Date().getFullYear())
const selectedMonth = ref(new Date().getMonth() + 1)

// 급여 입력 폼
const salaryForm = ref({
  baseSalary: 0,
  overtimeHours: 0,
  nightShiftHours: 0,
  holidayHours: 0,
  bonusAmount: 0,
  deductionAmount: 0
})

// 사용 가능한 연도 목록
const availableYears = computed(() => {
  const currentYear = new Date().getFullYear()
  const years = []
  for (let i = currentYear - 2; i <= currentYear + 1; i++) {
    years.push(i)
  }
  return years
})

// 직원 선택 시 폼 초기화
watch(selectedEmployee, (newEmployee) => {
  if (newEmployee) {
    salaryForm.value = {
      baseSalary: newEmployee.baseSalary || 0,
      overtimeHours: 0,
      nightShiftHours: 0,
      holidayHours: 0,
      bonusAmount: 0,
      deductionAmount: 0
    }
    // 기존 급여 데이터가 있으면 로드
    loadExistingSalary(newEmployee.id)
  }
})

// API 호출 함수들
const loadAllEmployees = async () => {
  loading.value = true
  try {
    const response = await apiClient.get('/employees')
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success && Array.isArray(response.data.data)) {
      employees.value = response.data.data.filter(emp => emp.isActive)
      console.log('직원 목록 로드 완료:', employees.value.length, '명')
    } else {
      employees.value = []
    }
  } catch (err) {
    console.error('직원 목록 로드 실패:', err)
    console.error('에러 타입:', err.type)
    console.error('에러 메시지:', err.message)
    console.error('에러 상세:', err.details)
    error.value = err.message || '직원 목록을 불러오는데 실패했습니다.'
    employees.value = []
  } finally {
    loading.value = false
  }
}

const loadMonthlyStatus = async () => {
  loading.value = true
  try {
    // API 문서에 따라 current 엔드포인트 사용
    const response = await apiClient.get('/salary/monthly-status/current')
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success) {
      monthlyStatus.value = response.data.data
    } else {
      monthlyStatus.value = {}
    }
  } catch (err) {
    console.error('월별 급여 현황 로드 실패:', err)
    console.error('에러 타입:', err.type)
    console.error('에러 메시지:', err.message)
    console.error('에러 상세:', err.details)
    error.value = err.message || '월별 급여 현황을 불러오는데 실패했습니다.'
    monthlyStatus.value = {}
  } finally {
    loading.value = false
  }
}

const loadExistingSalary = async (employeeId) => {
  try {
    const response = await apiClient.get(`/salary/${employeeId}?year=${selectedYear.value}&month=${selectedMonth.value}`)
    if (response.data.success && response.data.data) {
      const existingSalary = response.data.data
      salaryForm.value = {
        baseSalary: existingSalary.baseSalary || 0,
        overtimeHours: existingSalary.overtimeHours || 0,
        nightShiftHours: existingSalary.nightShiftHours || 0,
        holidayHours: existingSalary.holidayHours || 0,
        bonusAmount: existingSalary.bonusAmount || 0,
        deductionAmount: existingSalary.deductionAmount || 0
      }
    }
  } catch (err) {
    console.log('기존 급여 데이터가 없습니다.')
  }
}

const calculateSalary = async (employee) => {
  loading.value = true
  try {
    // API 문서에 따라 year, month 파라미터 사용
    const response = await apiClient.get(`/salary/calculate/${employee.id}?year=${selectedYear.value}&month=${selectedMonth.value}`)
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success) {
      salaryResult.value = response.data.data
      selectedEmployee.value = employee
      console.log('급여 계산 완료:', salaryResult.value)
    } else {
      alert('급여 계산에 실패했습니다: ' + (response.data.message || response.data.error))
    }
  } catch (err) {
    console.error('급여 계산 실패:', err)
    const errorMsg = err.response?.data?.error || err.response?.data?.message || err.message
    alert('급여 계산에 실패했습니다: ' + errorMsg)
  } finally {
    loading.value = false
  }
}

const calculateSalaryFromForm = async () => {
  if (!selectedEmployee.value) {
    alert('직원을 선택해주세요.')
    return
  }
  
  loading.value = true
  try {
    const salaryData = {
      employeeId: selectedEmployee.value.id,
      year: selectedYear.value,
      month: selectedMonth.value,
      baseSalary: parseInt(salaryForm.value.baseSalary) || 0,
      overtimeHours: parseFloat(salaryForm.value.overtimeHours) || 0,
      nightShiftHours: parseFloat(salaryForm.value.nightShiftHours) || 0,
      holidayHours: parseFloat(salaryForm.value.holidayHours) || 0,
      bonusAmount: parseInt(salaryForm.value.bonusAmount) || 0,
      deductionAmount: parseInt(salaryForm.value.deductionAmount) || 0
    }
    
    const response = await apiClient.post('/salary/calculate', salaryData)
    if (response.data.success) {
      salaryResult.value = response.data.data
      console.log('급여 계산 완료:', salaryResult.value)
    } else {
      alert('급여 계산에 실패했습니다: ' + (response.data.message || response.data.error))
    }
  } catch (err) {
    console.error('급여 계산 실패:', err)
    const errorMsg = err.response?.data?.error || err.response?.data?.message || err.message
    alert('급여 계산에 실패했습니다: ' + errorMsg)
  } finally {
    loading.value = false
  }
}

const saveSalary = async () => {
  if (!selectedEmployee.value || !salaryResult.value) {
    alert('직원을 선택하고 급여를 계산해주세요.')
    return
  }
  
  loading.value = true
  try {
    const salaryData = {
      employeeId: selectedEmployee.value.id,
      year: selectedYear.value,
      month: selectedMonth.value,
      baseSalary: salaryResult.value.baseSalary,
      overtimeHours: salaryResult.value.overtimeHours,
      nightShiftHours: salaryResult.value.nightShiftHours,
      holidayHours: salaryResult.value.holidayHours,
      overtimePay: salaryResult.value.overtimePay,
      nightShiftPay: salaryResult.value.nightShiftPay,
      holidayPay: salaryResult.value.holidayPay,
      bonusAmount: salaryResult.value.bonusAmount,
      deductionAmount: salaryResult.value.deductionAmount,
      totalSalary: salaryResult.value.totalSalary,
      netSalary: salaryResult.value.netSalary
    }
    
    const response = await apiClient.post('/salary', salaryData)
    if (response.data.success) {
      alert('급여가 성공적으로 저장되었습니다.')
      await loadMonthlyStatus() // 통계 업데이트
    } else {
      alert('급여 저장에 실패했습니다: ' + (response.data.message || response.data.error))
    }
  } catch (err) {
    console.error('급여 저장 실패:', err)
    const errorMsg = err.response?.data?.error || err.response?.data?.message || err.message
    alert('급여 저장에 실패했습니다: ' + errorMsg)
  } finally {
    loading.value = false
  }
}

const loadSalaryAlerts = async () => {
  try {
    const response = await apiClient.get('/salary/alerts')
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success && Array.isArray(response.data.data)) {
      salaryAlerts.value = response.data.data
    } else {
      salaryAlerts.value = []
    }
  } catch (err) {
    console.error('급여 알림 로드 실패:', err)
    salaryAlerts.value = []
  }
}

// 유틸리티 함수들
const formatCurrency = (amount) => {
  if (!amount) return '0원'
  return new Intl.NumberFormat('ko-KR').format(amount) + '원'
}

const selectEmployee = (employee) => {
  selectedEmployee.value = employee
}

// 컴포넌트 마운트 시 데이터 로드
onMounted(async () => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/salary')
  }
  
  // 서버 연결 확인 (활동 로그 API 사용)
  try {
    await apiClient.get('/activity-logs?page=0&size=1')
    console.log('✅ 서버 연결 확인됨')
  } catch (err) {
    console.error('❌ 서버 연결 실패:', err.message)
    error.value = '백엔드 서버에 연결할 수 없습니다. 서버가 실행 중인지 확인해주세요.'
    return
  }
  
  await Promise.all([
    loadAllEmployees(),
    loadMonthlyStatus()
  ])
})
</script>

<style scoped>
.salary-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.salary-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
}

.alert-btn {
  padding: 8px 15px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.alert-btn:hover {
  background: #c0392b;
}

.employee-selection {
  margin-bottom: 30px;
}

.selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.selection-header h4 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.load-all-btn {
  padding: 8px 15px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.load-all-btn:hover {
  background: #2980b9;
}

.employee-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.employee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.employee-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.employee-item.selected {
  border-color: #27ae60;
  background: #d5f4e6;
}

.employee-info {
  flex: 1;
}

.employee-name {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.employee-details {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.calculate-btn {
  padding: 6px 12px;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.calculate-btn:hover {
  background: #229954;
}

/* 급여 입력 폼 스타일 */
.salary-input-form {
  margin-bottom: 30px;
}

.salary-input-form h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.form-card {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border: 1px solid #e9ecef;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #dee2e6;
}

.period-selector {
  display: flex;
  gap: 10px;
}

.year-select,
.month-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.form-input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #27ae60;
}

.form-input[readonly] {
  background: #f8f9fa;
  color: #6c757d;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
}

.save-btn {
  padding: 10px 20px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.save-btn:hover {
  background: #c0392b;
}

/* 급여 결과 스타일 */
.salary-result {
  margin-bottom: 30px;
}

.salary-result h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.result-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #dee2e6;
}

.period-info {
  font-size: 1.1rem;
  color: #7f8c8d;
  font-weight: bold;
}

.salary-breakdown {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.breakdown-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.breakdown-item.total {
  border-top: 2px solid #27ae60;
  border-bottom: 2px solid #27ae60;
  padding: 12px 0;
  font-weight: bold;
  font-size: 1.1rem;
}

.breakdown-item.net {
  border-top: 2px solid #e74c3c;
  padding: 12px 0;
  font-weight: bold;
  font-size: 1.2rem;
  color: #e74c3c;
}

.item-label {
  color: #2c3e50;
  font-weight: 500;
}

.item-value {
  font-weight: bold;
  color: #2c3e50;
}

.item-detail {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-left: 10px;
}

/* 급여 알림 스타일 */
.salary-alerts {
  margin-bottom: 30px;
}

.salary-alerts h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.alerts-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.alert-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  border-radius: 8px;
  border-left: 4px solid;
}

.alert-item.urgent {
  background: #fdf2f2;
  border-left-color: #e74c3c;
}

.alert-item.warning {
  background: #fef9e7;
  border-left-color: #f39c12;
}

.alert-icon {
  font-size: 1.5rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.alert-message {
  font-size: 0.9rem;
  color: #7f8c8d;
}

/* 로딩 및 에러 상태 */
.loading-container,
.error-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 15px;
}

.error-text {
  color: #e74c3c;
  margin-bottom: 20px;
}

.retry-btn {
  padding: 10px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #2980b9;
}
</style> 