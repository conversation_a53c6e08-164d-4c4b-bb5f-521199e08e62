<template>
  <Layout ref="layoutRef">
    <div class="harvest-test">
      <div class="test-header">
        <h1>🌾 수확 관리 시스템 테스트</h1>
        <p>스마트팜 수확 상태 관리 시스템의 각 기능을 테스트해보세요</p>
      </div>

      <!-- 테스트 섹션들 -->
      <div class="test-sections">
        <!-- 기본 수확 관리 테스트 -->
        <div class="test-section">
          <h3>📊 기본 수확 관리 테스트</h3>
          <div class="test-cards">
            <div class="test-card">
              <h4>수확 상태 업데이트</h4>
              <div class="test-form">
                <input v-model="testCropId" type="number" placeholder="작물 ID" class="test-input">
                <select v-model="testStatus" class="test-select">
                  <option value="NOT_STARTED">수확 시작 전</option>
                  <option value="IN_PROGRESS">수확 진행중</option>
                  <option value="COMPLETED">수확 완료</option>
                  <option value="FAILED">수확 실패</option>
                </select>
                <button @click="testUpdateStatus" class="test-btn">테스트</button>
              </div>
            </div>

            <div class="test-card">
              <h4>수확 완료 처리</h4>
              <div class="test-form">
                <input v-model="testCropId" type="number" placeholder="작물 ID" class="test-input">
                <input v-model="testYield" type="number" step="0.1" placeholder="실제 수확량" class="test-input">
                <button @click="testComplete" class="test-btn">테스트</button>
              </div>
            </div>

            <div class="test-card">
              <h4>수확 실패 처리</h4>
              <div class="test-form">
                <input v-model="testCropId" type="number" placeholder="작물 ID" class="test-input">
                <select v-model="testFailureReason" class="test-select">
                  <option value="DISEASE">질병</option>
                  <option value="PEST">해충</option>
                  <option value="WEATHER">기상 악화</option>
                  <option value="NUTRIENT_DEFICIENCY">영양 부족</option>
                  <option value="WATER_ISSUE">수분 문제</option>
                  <option value="EQUIPMENT_FAILURE">장비 고장</option>
                  <option value="HUMAN_ERROR">인적 오류</option>
                  <option value="OTHER">기타</option>
                </select>
                <button @click="testFail" class="test-btn">테스트</button>
              </div>
            </div>

            <div class="test-card">
              <h4>진행률 업데이트</h4>
              <div class="test-form">
                <input v-model="testCropId" type="number" placeholder="작물 ID" class="test-input">
                <input v-model="testProgress" type="number" min="0" max="100" placeholder="진행률 %" class="test-input">
                <button @click="testUpdateProgress" class="test-btn">테스트</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 자동 수확 관리 테스트 -->
        <div class="test-section">
          <h3>🤖 자동 수확 관리 테스트</h3>
          <div class="test-cards">
            <div class="test-card">
              <h4>자동 완료 처리</h4>
              <div class="test-form">
                <input v-model="testCropId" type="number" placeholder="작물 ID" class="test-input">
                <button @click="testAutoComplete" class="test-btn">테스트</button>
              </div>
            </div>

            <div class="test-card">
              <h4>작물 아카이브</h4>
              <div class="test-form">
                <input v-model="testCropId" type="number" placeholder="작물 ID" class="test-input">
                <button @click="testArchive" class="test-btn">테스트</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 데이터 조회 테스트 -->
        <div class="test-section">
          <h3>📋 데이터 조회 테스트</h3>
          <div class="test-cards">
            <div class="test-card">
              <h4>미완료 통계 조회</h4>
              <button @click="testIncompleteStats" class="test-btn">테스트</button>
              <div v-if="testResults.incompleteStats" class="test-result">
                <pre>{{ JSON.stringify(testResults.incompleteStats, null, 2) }}</pre>
              </div>
            </div>

            <div class="test-card">
              <h4>알림 대상 조회</h4>
              <button @click="testReminderTargets" class="test-btn">테스트</button>
              <div v-if="testResults.reminderTargets" class="test-result">
                <pre>{{ JSON.stringify(testResults.reminderTargets, null, 2) }}</pre>
              </div>
            </div>

            <div class="test-card">
              <h4>주간 리포트 조회</h4>
              <button @click="testWeeklyReport" class="test-btn">테스트</button>
              <div v-if="testResults.weeklyReport" class="test-result">
                <pre>{{ JSON.stringify(testResults.weeklyReport, null, 2) }}</pre>
              </div>
            </div>

            <div class="test-card">
              <h4>실패 사유 목록</h4>
              <button @click="testFailureReasons" class="test-btn">테스트</button>
              <div v-if="testResults.failureReasons" class="test-result">
                <pre>{{ JSON.stringify(testResults.failureReasons, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>

        <!-- 알림 테스트 -->
        <div class="test-section">
          <h3>🔔 알림 테스트</h3>
          <div v-if="!isSupported" class="test-card">
            <h4>브라우저 지원 안됨</h4>
            <p>이 브라우저는 웹 푸시 알림을 지원하지 않습니다.</p>
          </div>
          <div v-else-if="permission !== 'granted'" class="test-card">
            <h4>알림 권한 필요</h4>
            <p>알림 테스트를 위해 권한이 필요합니다.</p>
            <button @click="initializeNotifications" class="test-btn">알림 권한 허용</button>
          </div>
          <div v-else class="test-cards">
            <div class="test-card">
              <h4>수확 완료 알림</h4>
              <button @click="testHarvestReminder" class="test-btn">테스트</button>
            </div>

            <div class="test-card">
              <h4>자동 완료 알림</h4>
              <button @click="testAutoCompleteNotification" class="test-btn">테스트</button>
            </div>

            <div class="test-card">
              <h4>수확 실패 알림</h4>
              <button @click="testHarvestFailureNotification" class="test-btn">테스트</button>
            </div>

            <div class="test-card">
              <h4>주간 리포트 알림</h4>
              <button @click="testWeeklyReportNotification" class="test-btn">테스트</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 테스트 결과 -->
      <div class="test-results">
        <h3>📊 테스트 결과</h3>
        <div v-if="testResults.lastTest" class="last-test-result">
          <h4>마지막 테스트 결과:</h4>
          <div class="result-status" :class="testResults.lastTest.success ? 'success' : 'error'">
            {{ testResults.lastTest.success ? '✅ 성공' : '❌ 실패' }}
          </div>
          <div class="result-message">{{ testResults.lastTest.message }}</div>
          <div v-if="testResults.lastTest.data" class="result-data">
            <pre>{{ JSON.stringify(testResults.lastTest.data, null, 2) }}</pre>
          </div>
        </div>
      </div>

      <!-- 네비게이션 -->
      <div class="test-navigation">
        <button @click="goToDashboard" class="nav-btn">🌾 수확 관리 대시보드로 이동</button>
        <button @click="goToHome" class="nav-btn">🏠 홈으로 이동</button>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import { useHarvestManagement } from '../composables/useHarvestManagement'
import { useNotifications } from '../composables/useNotifications'

const layoutRef = ref(null)

// 수확 관리 composable 사용
const {
  loading,
  error,
  updateHarvestStatus,
  completeHarvest,
  failHarvest,
  updateProgress,
  autoCompleteHarvest,
  archiveCrop,
  getIncompleteStatistics,
  getWeeklyReport,
  getReminderTargets,
  getFailureReasons
} = useHarvestManagement()

// 알림 composable 사용
const {
  isSupported,
  permission,
  showHarvestReminder,
  showAutoCompleteNotification,
  showHarvestFailureNotification,
  showWeeklyReportNotification,
  initializeNotifications
} = useNotifications()

// 테스트 데이터
const testCropId = ref('')
const testStatus = ref('NOT_STARTED')
const testYield = ref('')
const testFailureReason = ref('DISEASE')
const testProgress = ref('')

// 테스트 결과
const testResults = ref({
  lastTest: null,
  incompleteStats: null,
  reminderTargets: null,
  weeklyReport: null,
  failureReasons: null
})

// 테스트 함수들
const runTest = async (testName, testFunction) => {
  try {
    const result = await testFunction()
    testResults.value.lastTest = {
      success: true,
      message: `${testName} 테스트 성공`,
      data: result
    }
    return result
  } catch (error) {
    testResults.value.lastTest = {
      success: false,
      message: `${testName} 테스트 실패: ${error.message}`,
      data: null
    }
    throw error
  }
}

const testUpdateStatus = async () => {
  if (!testCropId.value) {
    alert('작물 ID를 입력해주세요')
    return
  }
  
  await runTest('수확 상태 업데이트', () => 
    updateHarvestStatus(testCropId.value, {
      harvestStatus: testStatus.value,
      harvestProgress: testStatus.value === 'COMPLETED' ? 100 : 0
    })
  )
}

const testComplete = async () => {
  if (!testCropId.value || !testYield.value) {
    alert('작물 ID와 수확량을 입력해주세요')
    return
  }
  
  await runTest('수확 완료 처리', () => 
    completeHarvest(testCropId.value, testYield.value)
  )
}

const testFail = async () => {
  if (!testCropId.value) {
    alert('작물 ID를 입력해주세요')
    return
  }
  
  await runTest('수확 실패 처리', () => 
    failHarvest(testCropId.value, {
      failureReason: testFailureReason.value,
      failureNotes: '테스트를 위한 실패 처리'
    })
  )
}

const testUpdateProgress = async () => {
  if (!testCropId.value || !testProgress.value) {
    alert('작물 ID와 진행률을 입력해주세요')
    return
  }
  
  await runTest('진행률 업데이트', () => 
    updateProgress(testCropId.value, testProgress.value)
  )
}

const testAutoComplete = async () => {
  if (!testCropId.value) {
    alert('작물 ID를 입력해주세요')
    return
  }
  
  await runTest('자동 완료 처리', () => 
    autoCompleteHarvest(testCropId.value)
  )
}

const testArchive = async () => {
  if (!testCropId.value) {
    alert('작물 ID를 입력해주세요')
    return
  }
  
  await runTest('작물 아카이브', () => 
    archiveCrop(testCropId.value)
  )
}

const testIncompleteStats = async () => {
  const result = await runTest('미완료 통계 조회', getIncompleteStatistics)
  testResults.value.incompleteStats = result
}

const testReminderTargets = async () => {
  const result = await runTest('알림 대상 조회', getReminderTargets)
  testResults.value.reminderTargets = result
}

const testWeeklyReport = async () => {
  const result = await runTest('주간 리포트 조회', getWeeklyReport)
  testResults.value.weeklyReport = result
}

const testFailureReasons = async () => {
  const result = await runTest('실패 사유 목록', getFailureReasons)
  testResults.value.failureReasons = result
}

// 알림 테스트 함수들
const testHarvestReminder = async () => {
  try {
    showHarvestReminder(5)
    testResults.value.lastTest = {
      success: true,
      message: '수확 완료 알림 테스트 성공',
      data: null
    }
  } catch (error) {
    testResults.value.lastTest = {
      success: false,
      message: '수확 완료 알림 테스트 실패: ' + error.message,
      data: null
    }
  }
}

const testAutoCompleteNotification = async () => {
  try {
    showAutoCompleteNotification('토마토')
    testResults.value.lastTest = {
      success: true,
      message: '자동 완료 알림 테스트 성공',
      data: null
    }
  } catch (error) {
    testResults.value.lastTest = {
      success: false,
      message: '자동 완료 알림 테스트 실패: ' + error.message,
      data: null
    }
  }
}

const testHarvestFailureNotification = async () => {
  try {
    showHarvestFailureNotification('오이', '질병')
    testResults.value.lastTest = {
      success: true,
      message: '수확 실패 알림 테스트 성공',
      data: null
    }
  } catch (error) {
    testResults.value.lastTest = {
      success: false,
      message: '수확 실패 알림 테스트 실패: ' + error.message,
      data: null
    }
  }
}

const testWeeklyReportNotification = async () => {
  try {
    showWeeklyReportNotification(85, 1250)
    testResults.value.lastTest = {
      success: true,
      message: '주간 리포트 알림 테스트 성공',
      data: null
    }
  } catch (error) {
    testResults.value.lastTest = {
      success: false,
      message: '주간 리포트 알림 테스트 실패: ' + error.message,
      data: null
    }
  }
}

// 네비게이션
const goToDashboard = () => {
  window.location.href = '/harvest-dashboard'
}

const goToHome = () => {
  window.location.href = '/'
}

// 컴포넌트 마운트
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/harvest-test')
  }
})
</script>

<style scoped>
.harvest-test {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
}

.test-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.test-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.test-sections {
  display: grid;
  gap: 30px;
  margin-bottom: 30px;
}

.test-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.test-section h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.test-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.test-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #27ae60;
}

.test-card h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.test-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.test-input,
.test-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
}

.test-input:focus,
.test-select:focus {
  outline: none;
  border-color: #27ae60;
}

.test-btn {
  padding: 10px 16px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.test-btn:hover {
  background: #2980b9;
}

.test-result {
  margin-top: 15px;
  background: #ecf0f1;
  border-radius: 6px;
  padding: 15px;
  max-height: 200px;
  overflow-y: auto;
}

.test-result pre {
  margin: 0;
  font-size: 0.8rem;
  color: #2c3e50;
  white-space: pre-wrap;
  word-break: break-all;
}

.test-results {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.test-results h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.last-test-result {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.last-test-result h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.result-status {
  font-weight: bold;
  margin-bottom: 10px;
  padding: 8px 12px;
  border-radius: 6px;
  display: inline-block;
}

.result-status.success {
  background: #d4edda;
  color: #155724;
}

.result-status.error {
  background: #f8d7da;
  color: #721c24;
}

.result-message {
  margin-bottom: 15px;
  color: #2c3e50;
}

.result-data {
  background: #ecf0f1;
  border-radius: 6px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

.result-data pre {
  margin: 0;
  font-size: 0.8rem;
  color: #2c3e50;
  white-space: pre-wrap;
  word-break: break-all;
}

.test-navigation {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.nav-btn {
  padding: 12px 24px;
  background: #27ae60;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background: #229954;
}

@media (max-width: 768px) {
  .test-cards {
    grid-template-columns: 1fr;
  }
  
  .test-navigation {
    flex-direction: column;
  }
  
  .nav-btn {
    width: 100%;
  }
}
</style> 