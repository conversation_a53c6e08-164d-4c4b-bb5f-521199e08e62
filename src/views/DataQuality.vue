<template>
  <Layout ref="layoutRef">
    <div class="data-quality-container">
      <!-- 페이지 헤더 -->
      <div class="main-title">
        <h1>🔍 데이터 품질 관리</h1>
        <p class="subtitle">경매 데이터의 품질을 모니터링하고 테스트 데이터를 정리합니다</p>
      </div>

      <!-- 알림 메시지 -->
      <div v-if="error" class="alert alert-error" @click="clearError">
        <div class="alert-icon">⚠️</div>
        <div class="alert-content">
          <h4>오류 발생</h4>
          <p>{{ error }}</p>
        </div>
        <button class="alert-close">×</button>
      </div>

      <div v-if="success" class="alert alert-success" @click="clearSuccess">
        <div class="alert-icon">✅</div>
        <div class="alert-content">
          <h4>성공</h4>
          <p>{{ success }}</p>
        </div>
        <button class="alert-close">×</button>
      </div>

      <!-- 품질 상태 대시보드 -->
      <div class="quality-dashboard">
        <div class="dashboard-header">
          <h2>📊 데이터 품질 대시보드</h2>
          <button @click="checkQualityStatus" :disabled="loading" class="btn btn-primary">
            <span v-if="loading" class="loading-spinner">🔄</span>
            {{ loading ? '확인 중...' : '품질 상태 확인' }}
          </button>
        </div>

        <div v-if="qualityStatus.totalCount > 0" class="quality-cards">
          <!-- 품질 점수 카드 -->
          <div class="quality-score-card" :style="{ borderColor: qualityLevel.color }">
            <div class="score-header">
              <div class="score-icon">{{ qualityLevel.icon }}</div>
              <div class="score-content">
                <div class="score-title">품질 점수</div>
                <div class="score-value" :style="{ color: qualityLevel.color }">
                  {{ qualityScore }}/100
                </div>
                <div class="score-level">{{ qualityLevel.level }}</div>
              </div>
            </div>
          </div>

          <!-- 전체 데이터 카드 -->
          <div class="quality-card">
            <div class="card-icon">📊</div>
            <div class="card-content">
              <div class="card-title">전체 데이터</div>
              <div class="card-value">{{ qualityStatus.totalCount.toLocaleString() }}건</div>
            </div>
          </div>

          <!-- 빈 품목명 카드 -->
          <div class="quality-card" :class="{ 'warning': qualityStatus.emptyPumNmRatio > 1 }">
            <div class="card-icon">❓</div>
            <div class="card-content">
              <div class="card-title">빈 품목명</div>
              <div class="card-value">{{ qualityStatus.emptyPumNmCount }}건</div>
              <div class="card-ratio">({{ qualityStatus.emptyPumNmRatio.toFixed(2) }}%)</div>
            </div>
          </div>

          <!-- Product ID 누락 카드 -->
          <div class="quality-card" :class="{ 'warning': qualityStatus.nullProductIdRatio > 5 }">
            <div class="card-icon">🔗</div>
            <div class="card-content">
              <div class="card-title">Product ID 누락</div>
              <div class="card-value">{{ qualityStatus.nullProductIdCount }}건</div>
              <div class="card-ratio">({{ qualityStatus.nullProductIdRatio.toFixed(2) }}%)</div>
            </div>
          </div>

          <!-- 잘못된 날짜 카드 -->
          <div class="quality-card" :class="{ 'warning': qualityStatus.invalidDateRatio > 1 }">
            <div class="card-icon">📅</div>
            <div class="card-content">
              <div class="card-title">잘못된 날짜</div>
              <div class="card-value">{{ qualityStatus.invalidDateCount }}건</div>
              <div class="card-ratio">({{ qualityStatus.invalidDateRatio.toFixed(2) }}%)</div>
            </div>
          </div>
        </div>

        <!-- 초기 상태 -->
        <div v-else class="initial-state">
          <div class="initial-icon">🔍</div>
          <h3>품질 상태를 확인해보세요</h3>
          <p>위의 "품질 상태 확인" 버튼을 클릭하여 데이터 품질을 분석합니다.</p>
        </div>
      </div>

      <!-- 정리 작업 섹션 -->
      <div class="cleanup-section">
        <div class="section-header">
          <h2>🧹 데이터 정리 작업</h2>
          <p>테스트 데이터와 불완전한 데이터를 정리합니다</p>
        </div>

        <div class="cleanup-options">
          <!-- 개별 정리 옵션 -->
          <div class="cleanup-option">
            <div class="option-header">
              <h3>📝 공백 데이터 정리</h3>
              <p>빈 품목명이나 null 값을 가진 데이터를 정리합니다</p>
            </div>
            <div class="option-actions">
              <button @click="cleanupEmptyData" :disabled="loading" class="btn btn-warning">
                <span v-if="loading" class="loading-spinner">🔄</span>
                {{ loading ? '정리 중...' : '공백 데이터 정리' }}
              </button>
            </div>
          </div>

          <div class="cleanup-option">
            <div class="option-header">
              <h3>🧪 테스트 데이터 정리</h3>
              <p>"test", "테스트", "dummy" 등의 테스트 데이터를 정리합니다</p>
            </div>
            <div class="option-actions">
              <button @click="cleanupTestData" :disabled="loading" class="btn btn-danger">
                <span v-if="loading" class="loading-spinner">🔄</span>
                {{ loading ? '정리 중...' : '테스트 데이터 정리' }}
              </button>
            </div>
          </div>

          <div class="cleanup-option">
            <div class="option-header">
              <h3>🔗 Product ID 연동</h3>
              <p>누락된 Product ID를 자동으로 연동합니다</p>
            </div>
            <div class="option-actions">
              <button @click="syncProductIds" :disabled="loading" class="btn btn-info">
                <span v-if="loading" class="loading-spinner">🔄</span>
                {{ loading ? '연동 중...' : 'Product ID 연동' }}
              </button>
            </div>
          </div>

          <!-- 전체 정리 프로세스 -->
          <div class="cleanup-option full-cleanup">
            <div class="option-header">
              <h3>🚀 전체 정리 프로세스</h3>
              <p>모든 정리 작업을 순차적으로 실행합니다 (권장)</p>
            </div>
            <div class="option-actions">
              <button @click="runFullCleanup" :disabled="loading" class="btn btn-success">
                <span v-if="loading" class="loading-spinner">🔄</span>
                {{ loading ? '전체 정리 중...' : '전체 정리 프로세스 실행' }}
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 정리 결과 표시 -->
      <div v-if="cleanupResult.totalDeleted > 0" class="cleanup-results">
        <div class="section-header">
          <h2>📋 최근 정리 결과</h2>
        </div>
        
        <div class="results-grid">
          <div class="result-card">
            <div class="result-icon">🧪</div>
            <div class="result-content">
              <div class="result-title">테스트 데이터</div>
              <div class="result-value">{{ cleanupResult.testDataDeleted }}건 삭제</div>
            </div>
          </div>
          
          <div class="result-card">
            <div class="result-icon">📏</div>
            <div class="result-content">
              <div class="result-title">잘못된 길이</div>
              <div class="result-value">{{ cleanupResult.invalidLengthDeleted }}건 삭제</div>
            </div>
          </div>
          
          <div class="result-card">
            <div class="result-icon">🔍</div>
            <div class="result-content">
              <div class="result-title">패턴 테스트</div>
              <div class="result-value">{{ cleanupResult.patternTestDeleted }}건 삭제</div>
            </div>
          </div>
          
          <div class="result-card total">
            <div class="result-icon">📊</div>
            <div class="result-content">
              <div class="result-title">총 삭제</div>
              <div class="result-value">{{ cleanupResult.totalDeleted }}건</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 도움말 섹션 -->
      <div class="help-section">
        <div class="section-header">
          <h2>💡 품질 관리 가이드</h2>
        </div>
        
        <div class="help-content">
          <div class="help-item">
            <h4>📊 품질 점수 기준</h4>
            <ul>
              <li><strong>90-100점 (EXCELLENT):</strong> 매우 우수한 데이터 품질</li>
              <li><strong>80-89점 (GOOD):</strong> 양호한 데이터 품질</li>
              <li><strong>70-79점 (WARNING):</strong> 주의가 필요한 품질</li>
              <li><strong>60-69점 (POOR):</strong> 개선이 필요한 품질</li>
              <li><strong>60점 미만 (CRITICAL):</strong> 즉시 정리가 필요한 품질</li>
            </ul>
          </div>
          
          <div class="help-item">
            <h4>🧹 정리 작업 설명</h4>
            <ul>
              <li><strong>공백 데이터 정리:</strong> 빈 품목명, null 값 등</li>
              <li><strong>테스트 데이터 정리:</strong> "test", "테스트", "dummy" 등</li>
              <li><strong>Product ID 연동:</strong> 누락된 Product ID 자동 매핑</li>
              <li><strong>전체 정리 프로세스:</strong> 모든 작업을 순차 실행</li>
            </ul>
          </div>
          
          <div class="help-item">
            <h4>⚠️ 주의사항</h4>
            <ul>
              <li>정리 작업은 되돌릴 수 없으므로 신중하게 실행하세요</li>
              <li>정리 전에 반드시 데이터 백업을 권장합니다</li>
              <li>정리 후 품질 상태를 다시 확인하세요</li>
              <li>정기적인 품질 점검을 통해 데이터를 관리하세요</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import { useDataQuality } from '../composables/useDataQuality'

const layoutRef = ref(null)

// Composables
const {
  loading,
  error,
  success,
  qualityStatus,
  cleanupResult,
  qualityScore,
  qualityLevel,
  checkQualityStatus,
  cleanupTestData,
  cleanupEmptyData,
  syncProductIds,
  runFullCleanup,
  resetState
} = useDataQuality()

// 메시지 초기화
const clearError = () => {
  error.value = ''
}

const clearSuccess = () => {
  success.value = ''
}

// 초기화
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/data-quality')
  }
  
  // 페이지 로드 시 자동으로 품질 상태 확인
  checkQualityStatus()
})
</script>

<style scoped>
.data-quality-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
}

.main-title h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

/* 알림 메시지 */
.alert {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alert:hover {
  transform: translateY(-2px);
}

.alert-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert-icon {
  font-size: 1.5rem;
}

.alert-content {
  flex: 1;
}

.alert-content h4 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
}

.alert-content p {
  margin: 0;
  font-size: 0.9rem;
}

.alert-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.alert-close:hover {
  opacity: 1;
}

/* 품질 대시보드 */
.quality-dashboard {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.dashboard-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.quality-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.quality-score-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 25px;
  border: 3px solid;
  color: white;
}

.score-header {
  display: flex;
  align-items: center;
  gap: 15px;
}

.score-icon {
  font-size: 3rem;
}

.score-content {
  flex: 1;
}

.score-title {
  font-size: 0.9rem;
  opacity: 0.9;
  margin-bottom: 5px;
}

.score-value {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 5px;
}

.score-level {
  font-size: 1rem;
  opacity: 0.9;
}

.quality-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;
}

.quality-card.warning {
  border-left-color: #f39c12;
  background: #fff8e1;
}

.quality-card .card-icon {
  font-size: 2rem;
}

.quality-card .card-content {
  flex: 1;
}

.quality-card .card-title {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.quality-card .card-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 3px;
}

.quality-card .card-ratio {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.initial-state {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.initial-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.initial-state h3 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.initial-state p {
  margin: 0;
  font-size: 1rem;
}

/* 정리 작업 섹션 */
.cleanup-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.section-header {
  margin-bottom: 25px;
}

.section-header h2 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.section-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 0.9rem;
}

.cleanup-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.cleanup-option {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background: #f8f9fa;
}

.cleanup-option.full-cleanup {
  border-color: #27ae60;
  background: #f0f8f0;
}

.option-header h3 {
  font-size: 1.1rem;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.option-header p {
  color: #7f8c8d;
  margin: 0 0 15px 0;
  font-size: 0.9rem;
}

.option-actions {
  display: flex;
  gap: 10px;
}

/* 정리 결과 */
.cleanup-results {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.result-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.result-card.total {
  background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%);
  color: white;
}

.result-icon {
  font-size: 1.5rem;
}

.result-content {
  flex: 1;
}

.result-title {
  font-size: 0.8rem;
  opacity: 0.8;
  margin-bottom: 3px;
}

.result-value {
  font-size: 1.1rem;
  font-weight: bold;
}

/* 도움말 섹션 */
.help-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.help-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.help-item h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.help-item ul {
  margin: 0;
  padding-left: 20px;
  color: #7f8c8d;
}

.help-item li {
  margin-bottom: 5px;
  font-size: 0.9rem;
  line-height: 1.4;
}

.help-item strong {
  color: #2c3e50;
}

/* 버튼 스타일 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #e67e22;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: #138496;
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #229954;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .quality-cards {
    grid-template-columns: 1fr;
  }
  
  .results-grid {
    grid-template-columns: 1fr;
  }
  
  .help-content {
    grid-template-columns: 1fr;
  }
  
  .option-actions {
    flex-direction: column;
  }
}
</style> 