<template>
  <Layout ref="layoutRef">
    <div class="statistics-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>📊 농장 운영 통계</h1>
        <p class="page-subtitle">농장의 모든 데이터를 한눈에 확인하세요</p>
      </div>

      <!-- 연도 선택 -->
      <div class="year-selector">
        <label>연도 선택:</label>
        <select v-model="selectedYear" @change="loadAllStatistics" class="year-select">
          <option v-for="year in availableYears" :key="year" :value="year">{{ year }}년</option>
        </select>
      </div>

      <!-- 로딩 상태 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">🔄</div>
        <p>통계 데이터를 불러오는 중...</p>
      </div>

      <!-- 에러 상태 -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">⚠️</div>
        <p class="error-text">{{ error }}</p>
        <button @click="loadAllStatistics" class="retry-btn">다시 시도</button>
      </div>

      <!-- 통계 대시보드 -->
      <div v-else class="statistics-dashboard">
        <!-- 요약 카드 -->
        <div class="summary-cards">
          <div class="summary-card revenue">
            <div class="card-icon">💰</div>
            <div class="card-content">
              <div class="card-title">총 매출</div>
              <div class="card-value">{{ formatCurrency(dashboardData.summary?.totalRevenue || 0) }}</div>
              <div class="card-trend" :class="getTrendClass(dashboardData.summary?.profitTrend)">
                {{ getTrendIcon(dashboardData.summary?.profitTrend) }} {{ getTrendText(dashboardData.summary?.profitTrend) }}
              </div>
            </div>
          </div>

          <div class="summary-card expenses">
            <div class="card-icon">💸</div>
            <div class="card-content">
              <div class="card-title">총 지출</div>
              <div class="card-value">{{ formatCurrency(dashboardData.summary?.totalExpenses || 0) }}</div>
              <div class="card-trend">월 평균 {{ formatCurrency((dashboardData.summary?.totalExpenses || 0) / 12) }}</div>
            </div>
          </div>

          <div class="summary-card profit">
            <div class="card-icon">📈</div>
            <div class="card-content">
              <div class="card-title">순이익</div>
              <div class="card-value" :class="{ negative: (dashboardData.summary?.netProfit || 0) < 0 }">
                {{ formatCurrency(dashboardData.summary?.netProfit || 0) }}
              </div>
              <div class="card-trend">
                수익률 {{ formatPercentage(dashboardData.summary?.profitMargin || 0) }}
              </div>
            </div>
          </div>

          <div class="summary-card productivity">
            <div class="card-icon">🌾</div>
            <div class="card-content">
              <div class="card-title">생산성</div>
              <div class="card-value">{{ dashboardData.summary?.totalHarvests || 0 }}회 수확</div>
              <div class="card-trend">
                {{ formatQuantity(dashboardData.summary?.totalQuantity || 0) }}kg
              </div>
            </div>
          </div>
        </div>

        <!-- 차트 섹션 -->
        <div class="charts-section">
          <!-- 월별 매출/지출/순이익 차트 -->
          <div class="chart-container">
            <h3>📊 월별 재무 현황</h3>
            <canvas ref="financialChart" width="400" height="200"></canvas>
          </div>

          <!-- 작물별 성과 차트 -->
          <div class="chart-container">
            <h3>🌾 작물별 성과</h3>
            <canvas ref="cropPerformanceChart" width="400" height="200"></canvas>
          </div>

          <!-- 온실 활용도 차트 -->
          <div class="chart-container">
            <h3>🏠 온실 활용도</h3>
            <canvas ref="greenhouseChart" width="400" height="200"></canvas>
          </div>

          <!-- 직원 생산성 차트 -->
          <div class="chart-container">
            <h3>👷 부서별 생산성</h3>
            <canvas ref="employeeChart" width="400" height="200"></canvas>
          </div>
        </div>

        <!-- 상세 통계 테이블 -->
        <div class="detailed-stats">
          <h3>📋 상세 통계</h3>
          
          <!-- 작물 가격 추이 -->
          <div class="stats-section">
            <h4>작물 가격 추이</h4>
            <div class="table-container">
              <table class="stats-table">
                <thead>
                  <tr>
                    <th>작물명</th>
                    <th>평균가격</th>
                    <th>최고가</th>
                    <th>최저가</th>
                    <th>변동성</th>
                    <th>트렌드</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="crop in cropPriceTrends" :key="crop.cropName">
                    <td>{{ crop.cropName }}</td>
                    <td>{{ formatCurrency(crop.averagePrice) }}</td>
                    <td>{{ formatCurrency(crop.maxPrice) }}</td>
                    <td>{{ formatCurrency(crop.minPrice) }}</td>
                    <td>{{ formatCurrency(crop.priceVolatility) }}</td>
                    <td>
                      <span :class="getPriceTrendClass(crop.priceTrend)">
                        {{ getPriceTrendText(crop.priceTrend) }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- 온실 현황 -->
          <div class="stats-section">
            <h4>온실 현황</h4>
            <div class="greenhouse-grid">
              <div 
                v-for="greenhouse in dashboardData.greenhouseUtilization?.greenhouseStatus" 
                :key="greenhouse.greenhouseNumber"
                class="greenhouse-card"
                :class="getGreenhouseStatusClass(greenhouse.status)"
              >
                <div class="greenhouse-header">
                  <h5>{{ greenhouse.greenhouseNumber }}</h5>
                  <span class="status-badge">{{ getGreenhouseStatusText(greenhouse.status) }}</span>
                </div>
                <div class="greenhouse-details">
                  <p><strong>현재 작물:</strong> {{ greenhouse.currentCrop || '없음' }}</p>
                  <p><strong>면적:</strong> {{ greenhouse.area }}㎡</p>
                  <p><strong>위치:</strong> {{ greenhouse.location }}</p>
                  <p><strong>생산성:</strong> {{ formatQuantity(greenhouse.productivity) }}kg</p>
                </div>
              </div>
            </div>
          </div>

          <!-- 직원 생산성 -->
          <div class="stats-section">
            <h4>부서별 직원 생산성</h4>
            <div class="table-container">
              <table class="stats-table">
                <thead>
                  <tr>
                    <th>부서</th>
                    <th>직원수</th>
                    <th>총 급여</th>
                    <th>생산성 지수</th>
                    <th>성과</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="dept in dashboardData.employeeProductivity?.departmentPerformance" :key="dept.department">
                    <td>{{ dept.department }}</td>
                    <td>{{ dept.employeeCount }}명</td>
                    <td>{{ formatCurrency(dept.totalSalary) }}</td>
                    <td>{{ dept.productivity.toFixed(2) }}</td>
                    <td>
                      <span :class="getPerformanceClass(dept.performance)">
                        {{ getPerformanceText(dept.performance) }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import Layout from '../components/Layout.vue'
import Chart from 'chart.js/auto'

const layoutRef = ref(null)

// 상태 관리
const loading = ref(false)
const error = ref('')
const selectedYear = ref(new Date().getFullYear())
const availableYears = ref([2023, 2024, 2025])

// 차트 참조
const financialChart = ref(null)
const cropPerformanceChart = ref(null)
const greenhouseChart = ref(null)
const employeeChart = ref(null)

// 데이터
const dashboardData = ref({
  summary: {},
  topPerformingCrops: [],
  monthlyTrends: [],
  greenhouseUtilization: {},
  employeeProductivity: {}
})
const cropPriceTrends = ref([])
const financialSummary = ref({})

// 차트 인스턴스
let financialChartInstance = null
let cropPerformanceChartInstance = null
let greenhouseChartInstance = null
let employeeChartInstance = null

// API 호출 함수들
const loadAllStatistics = async () => {
  loading.value = true
  error.value = ''
  
  try {
    await Promise.all([
      loadDashboardData(),
      loadCropPriceTrends(),
      loadFinancialSummary()
    ])
    
    await nextTick()
    createCharts()
  } catch (err) {
    error.value = '통계 데이터를 불러오는데 실패했습니다: ' + err.message
  } finally {
    loading.value = false
  }
}

const loadDashboardData = async () => {
  try {
    const response = await fetch(`/api/statistics/dashboard?year=${selectedYear.value}`)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    const result = await response.json()
    if (result.success) {
      dashboardData.value = result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('대시보드 데이터 로드 실패:', error)
    error.value = '대시보드 데이터를 불러오는데 실패했습니다.'
    dashboardData.value = {}
  }
}

const loadCropPriceTrends = async () => {
  try {
    const response = await fetch(`/api/statistics/crop-price-trends?year=${selectedYear.value}`)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    const result = await response.json()
    if (result.success) {
      cropPriceTrends.value = result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('작물 가격 추이 로드 실패:', error)
    error.value = '작물 가격 추이를 불러오는데 실패했습니다.'
    cropPriceTrends.value = []
  }
}

const loadFinancialSummary = async () => {
  try {
    const response = await fetch(`/api/statistics/financial-summary?year=${selectedYear.value}`)
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }
    const result = await response.json()
    if (result.success) {
      financialSummary.value = result.data
    } else {
      throw new Error(result.message)
    }
  } catch (error) {
    console.error('재무 요약 로드 실패:', error)
    error.value = '재무 요약을 불러오는데 실패했습니다.'
    financialSummary.value = {}
  }
}

// 차트 생성 함수들
const createCharts = () => {
  // DOM이 완전히 렌더링될 때까지 대기
  setTimeout(() => {
    if (financialChart.value) createFinancialChart()
    if (cropPerformanceChart.value) createCropPerformanceChart()
    if (greenhouseChart.value) createGreenhouseChart()
    if (employeeChart.value) createEmployeeChart()
  }, 100)
}

const createFinancialChart = () => {
  if (financialChartInstance) {
    financialChartInstance.destroy()
  }

  if (!financialChart.value) return

  const ctx = financialChart.value.getContext('2d')
  const monthlyData = financialSummary.value.monthlyData || []

  financialChartInstance = new Chart(ctx, {
    type: 'line',
    data: {
      labels: monthlyData.map(d => `${d.month}월`),
      datasets: [
        {
          label: '매출',
          data: monthlyData.map(d => d.revenue),
          borderColor: '#27ae60',
          backgroundColor: 'rgba(39, 174, 96, 0.1)',
          tension: 0.4
        },
        {
          label: '지출',
          data: monthlyData.map(d => d.expenses),
          borderColor: '#e74c3c',
          backgroundColor: 'rgba(231, 76, 60, 0.1)',
          tension: 0.4
        },
        {
          label: '순이익',
          data: monthlyData.map(d => d.netProfit),
          borderColor: '#3498db',
          backgroundColor: 'rgba(52, 152, 219, 0.1)',
          tension: 0.4
        }
      ]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: '월별 재무 현황'
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return formatCurrency(value)
            }
          }
        }
      }
    }
  })
}

const createCropPerformanceChart = () => {
  if (cropPerformanceChartInstance) {
    cropPerformanceChartInstance.destroy()
  }

  if (!cropPerformanceChart.value) return

  const ctx = cropPerformanceChart.value.getContext('2d')
  const crops = dashboardData.value.topPerformingCrops || []

  cropPerformanceChartInstance = new Chart(ctx, {
    type: 'bar',
    data: {
      labels: crops.map(c => c.cropName),
      datasets: [
        {
          label: '총 매출',
          data: crops.map(c => c.totalRevenue),
          backgroundColor: 'rgba(39, 174, 96, 0.8)',
          borderColor: '#27ae60',
          borderWidth: 1
        },
        {
          label: '수익률 (%)',
          data: crops.map(c => c.profitMargin),
          backgroundColor: 'rgba(52, 152, 219, 0.8)',
          borderColor: '#3498db',
          borderWidth: 1,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: '작물별 성과'
        }
      },
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return formatCurrency(value)
            }
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          beginAtZero: true,
          ticks: {
            callback: function(value) {
              return value + '%'
            }
          },
          grid: {
            drawOnChartArea: false,
          },
        }
      }
    }
  })
}

const createGreenhouseChart = () => {
  if (greenhouseChartInstance) {
    greenhouseChartInstance.destroy()
  }

  if (!greenhouseChart.value) return

  const ctx = greenhouseChart.value.getContext('2d')
  const utilization = dashboardData.value.greenhouseUtilization || {}
  const total = utilization.totalGreenhouses || 0
  const active = utilization.activeGreenhouses || 0
  const inactive = total - active

  greenhouseChartInstance = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['활성 온실', '비활성 온실'],
      datasets: [{
        data: [active, inactive],
        backgroundColor: [
          'rgba(39, 174, 96, 0.8)',
          'rgba(149, 165, 166, 0.8)'
        ],
        borderColor: [
          '#27ae60',
          '#95a5a6'
        ],
        borderWidth: 2
      }]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'bottom',
        },
        title: {
          display: true,
          text: `온실 활용도: ${utilization.utilizationRate || 0}%`
        }
      }
    }
  })
}

const createEmployeeChart = () => {
  if (employeeChartInstance) {
    employeeChartInstance.destroy()
  }

  if (!employeeChart.value) return

  const ctx = employeeChart.value.getContext('2d')
  const departments = dashboardData.value.employeeProductivity?.departmentPerformance || []

  employeeChartInstance = new Chart(ctx, {
    type: 'radar',
    data: {
      labels: departments.map(d => d.department),
      datasets: [
        {
          label: '생산성 지수',
          data: departments.map(d => d.productivity),
          backgroundColor: 'rgba(52, 152, 219, 0.2)',
          borderColor: '#3498db',
          borderWidth: 2,
          pointBackgroundColor: '#3498db',
          pointBorderColor: '#fff',
          pointHoverBackgroundColor: '#fff',
          pointHoverBorderColor: '#3498db'
        }
      ]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'top',
        },
        title: {
          display: true,
          text: '부서별 생산성'
        }
      },
      scales: {
        r: {
          beginAtZero: true,
          ticks: {
            stepSize: 1
          }
        }
      }
    }
  })
}

// 유틸리티 함수들
const formatCurrency = (amount) => {
  if (!amount) return '0원'
  return new Intl.NumberFormat('ko-KR').format(amount) + '원'
}

const formatPercentage = (value) => {
  if (!value) return '0%'
  return value.toFixed(2) + '%'
}

const formatQuantity = (quantity) => {
  if (!quantity) return '0'
  return new Intl.NumberFormat('ko-KR').format(quantity)
}

const getTrendClass = (trend) => {
  if (trend === 'UP') return 'trend-up'
  if (trend === 'DOWN') return 'trend-down'
  return 'trend-stable'
}

const getTrendIcon = (trend) => {
  if (trend === 'UP') return '📈'
  if (trend === 'DOWN') return '📉'
  return '➡️'
}

const getTrendText = (trend) => {
  if (trend === 'UP') return '상승'
  if (trend === 'DOWN') return '하락'
  return '안정'
}

const getPriceTrendClass = (trend) => {
  if (trend === 'UP') return 'trend-up'
  if (trend === 'DOWN') return 'trend-down'
  return 'trend-stable'
}

const getPriceTrendText = (trend) => {
  if (trend === 'UP') return '상승'
  if (trend === 'DOWN') return '하락'
  return '안정'
}

const getGreenhouseStatusClass = (status) => {
  // 상태값을 대소문자 구분 없이 처리
  const statusUpper = status?.toUpperCase()
  
  if (statusUpper === 'CULTIVATING' || statusUpper === 'ACTIVE' || statusUpper === 'OPERATIONAL') {
    return 'status-active'
  }
  if (statusUpper === 'HARVESTING' || statusUpper === 'HARVEST') {
    return 'status-harvesting'
  }
  if (statusUpper === 'MAINTENANCE' || statusUpper === 'REPAIR') {
    return 'status-maintenance'
  }
  if (statusUpper === 'INACTIVE' || statusUpper === 'DISABLED' || statusUpper === 'OFFLINE') {
    return 'status-inactive'
  }
  
  // 기본값: 상태가 있으면 활성으로 처리
  return status ? 'status-active' : 'status-inactive'
}

const getGreenhouseStatusText = (status) => {
  // 상태값을 대소문자 구분 없이 처리
  const statusUpper = status?.toUpperCase()
  
  if (statusUpper === 'CULTIVATING' || statusUpper === 'ACTIVE' || statusUpper === 'OPERATIONAL') {
    return '재배중'
  }
  if (statusUpper === 'HARVESTING' || statusUpper === 'HARVEST') {
    return '수확중'
  }
  if (statusUpper === 'MAINTENANCE' || statusUpper === 'REPAIR') {
    return '정비중'
  }
  if (statusUpper === 'INACTIVE' || statusUpper === 'DISABLED' || statusUpper === 'OFFLINE') {
    return '비활성'
  }
  
  // 기본값: 상태가 있으면 재배중으로 표시
  return status ? '재배중' : '비활성'
}

const getPerformanceClass = (performance) => {
  if (performance === 'EXCELLENT') return 'performance-excellent'
  if (performance === 'GOOD') return 'performance-good'
  if (performance === 'AVERAGE') return 'performance-average'
  return 'performance-poor'
}

const getPerformanceText = (performance) => {
  if (performance === 'EXCELLENT') return '우수'
  if (performance === 'GOOD') return '양호'
  if (performance === 'AVERAGE') return '보통'
  return '미흡'
}

// 컴포넌트 마운트
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/stats')
  }
  loadAllStatistics()
})
</script>

<style scoped>
.statistics-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.year-selector {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 30px;
  justify-content: center;
}

.year-selector label {
  font-weight: bold;
  color: #2c3e50;
}

.year-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  cursor: pointer;
}

.loading-container {
  text-align: center;
  padding: 60px;
}

.loading-spinner {
  font-size: 3rem;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  padding: 40px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin: 20px 0;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.error-text {
  color: #856404;
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e67e22;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-5px);
}

.summary-card.revenue {
  border-left: 4px solid #27ae60;
}

.summary-card.expenses {
  border-left: 4px solid #e74c3c;
}

.summary-card.profit {
  border-left: 4px solid #3498db;
}

.summary-card.productivity {
  border-left: 4px solid #f39c12;
}

.card-icon {
  font-size: 2.5rem;
}

.card-content {
  flex: 1;
}

.card-title {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.card-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.card-value.negative {
  color: #e74c3c;
}

.card-trend {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.trend-up {
  color: #27ae60;
}

.trend-down {
  color: #e74c3c;
}

.trend-stable {
  color: #f39c12;
}

.charts-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.chart-container {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.chart-container h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.detailed-stats {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.detailed-stats h3 {
  margin: 0 0 30px 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.stats-section {
  margin-bottom: 40px;
}

.stats-section h4 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.table-container {
  overflow-x: auto;
}

.stats-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 20px;
}

.stats-table th,
.stats-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
  color: #2c3e50 !important;
}

.stats-table th {
  background: #f8f9fa;
  font-weight: bold;
  color: #2c3e50 !important;
}

.stats-table tr:hover {
  background: #f8f9fa;
}

.greenhouse-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.greenhouse-card {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #95a5a6;
}

.greenhouse-card.status-active {
  border-left-color: #27ae60;
  background: #f0fff4;
}

.greenhouse-card.status-harvesting {
  border-left-color: #f39c12;
  background: #fffbf0;
}

.greenhouse-card.status-maintenance {
  border-left-color: #3498db;
  background: #f0f8ff;
}

.greenhouse-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.greenhouse-header h5 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.status-badge {
  background: #27ae60;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.greenhouse-details p {
  margin: 5px 0;
  color: #2c3e50;
}

.performance-excellent {
  color: #27ae60;
  font-weight: bold;
}

.performance-good {
  color: #3498db;
  font-weight: bold;
}

.performance-average {
  color: #f39c12;
  font-weight: bold;
}

.performance-poor {
  color: #e74c3c;
  font-weight: bold;
}

@media (max-width: 768px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .summary-cards {
    grid-template-columns: 1fr;
  }
  
  .greenhouse-grid {
    grid-template-columns: 1fr;
  }
}
</style> 