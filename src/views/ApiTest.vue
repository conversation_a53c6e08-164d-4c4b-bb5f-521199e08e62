<template>
  <Layout ref="layoutRef">
    <div class="api-test-container">
      <div class="main-title">
        <h1>🧪 API 연결 테스트</h1>
        <p>백엔드 API 엔드포인트 연결 상태를 확인합니다</p>
      </div>

      <div class="test-controls">
        <button @click="runTests" :disabled="testing" class="test-btn">
          <span v-if="testing" class="spinner">🔄</span>
          <span v-else>🚀</span>
          {{ testing ? '테스트 중...' : '전체 테스트 실행' }}
        </button>
        <button @click="clearResults" :disabled="testing" class="clear-btn">
          🗑️ 결과 지우기
        </button>
      </div>

      <div v-if="testResults.length > 0" class="test-summary">
        <div class="summary-card success">
          <h3>✅ 성공</h3>
          <span class="count">{{ getSuccessCount() }}</span>
        </div>
        <div class="summary-card error">
          <h3>❌ 실패</h3>
          <span class="count">{{ getErrorCount() }}</span>
        </div>
        <div class="summary-card total">
          <h3>📊 전체</h3>
          <span class="count">{{ testResults.length }}</span>
        </div>
      </div>

      <div v-if="testResults.length > 0" class="test-results">
        <h2>📋 테스트 결과</h2>
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index" 
            :class="['result-item', result.status.toLowerCase()]"
          >
            <div class="result-header">
              <span class="status-icon">
                {{ result.status === 'SUCCESS' ? '✅' : '❌' }}
              </span>
              <span class="test-name">{{ result.name }}</span>
              <span class="method-badge">{{ result.method }}</span>
              <span class="duration">{{ result.duration }}</span>
              <span class="timestamp">{{ result.timestamp }}</span>
            </div>
            
            <div class="result-details">
              <div class="url">
                <strong>URL:</strong> {{ result.url }}
              </div>
              <div class="status-code">
                <strong>Status:</strong> {{ result.statusCode }}
              </div>
              
              <div v-if="result.status === 'SUCCESS'" class="success-data">
                <strong>응답 데이터:</strong>
                <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
              </div>
              
              <div v-if="result.status === 'ERROR'" class="error-data">
                <div class="error-message">
                  <strong>에러:</strong> {{ result.error }}
                </div>
                <div v-if="result.details" class="error-details">
                  <strong>상세:</strong>
                  <pre>{{ JSON.stringify(result.details, null, 2) }}</pre>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div v-if="testResults.length === 0 && !testing" class="empty-state">
        <h3>🎯 API 테스트를 시작하세요</h3>
        <p>위의 "전체 테스트 실행" 버튼을 클릭하여 백엔드 API 연결을 확인할 수 있습니다.</p>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import Layout from '../components/Layout.vue'
import { useApiTest } from '@/composables/useApiTest'

const layoutRef = ref(null)

// API 테스트 composable
const {
  testResults,
  testing,
  runAllTests,
  clearResults,
  getSuccessCount,
  getErrorCount
} = useApiTest()

const runTests = async () => {
  try {
    await runAllTests()
  } catch (error) {
    console.error('테스트 실행 중 오류:', error)
  }
}

onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/api-test')
  }
})
</script>

<style scoped>
.api-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
}

.main-title h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
}

.main-title p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.test-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
}

.test-btn, .clear-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.test-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.test-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
}

.test-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.clear-btn {
  background: #95a5a6;
  color: white;
}

.clear-btn:hover:not(:disabled) {
  background: #7f8c8d;
}

.spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.test-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.summary-card.success {
  border-left: 4px solid #27ae60;
}

.summary-card.error {
  border-left: 4px solid #e74c3c;
}

.summary-card.total {
  border-left: 4px solid #3498db;
}

.summary-card h3 {
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.summary-card .count {
  font-size: 2rem;
  font-weight: bold;
}

.test-results {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.test-results h2 {
  margin: 0 0 20px 0;
  color: #2c3e50;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.result-item {
  border: 1px solid #ecf0f1;
  border-radius: 8px;
  overflow: hidden;
}

.result-item.success {
  border-left: 4px solid #27ae60;
}

.result-item.error {
  border-left: 4px solid #e74c3c;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  font-weight: 600;
}

.status-icon {
  font-size: 1.2rem;
}

.test-name {
  flex: 1;
  color: #2c3e50;
}

.method-badge {
  background: #3498db;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.duration {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.timestamp {
  color: #95a5a6;
  font-size: 0.8rem;
}

.result-details {
  padding: 15px;
}

.result-details > div {
  margin-bottom: 10px;
}

.url, .status-code {
  font-size: 0.9rem;
}

.success-data pre, .error-details pre {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 0.8rem;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.error-message {
  color: #e74c3c;
  font-weight: 600;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-state h3 {
  margin: 0 0 15px 0;
  font-size: 1.5rem;
}
</style>
