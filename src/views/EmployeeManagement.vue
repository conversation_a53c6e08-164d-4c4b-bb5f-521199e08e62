<template>
  <Layout ref="layoutRef">
    <div class="employee-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>👷 직원 관리</h1>
        <p class="page-subtitle">직원 정보를 관리합니다</p>
      </div>

      <!-- 시스템 상태 및 통계 -->
      <div class="system-section">
        <div class="system-status">
          <h3>📊 직원 현황</h3>
          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon">👥</div>
              <div class="status-info">
                <div class="status-label">총 직원</div>
                <div class="status-value">{{ statistics?.totalActiveEmployees || 0 }}명</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">⚠️</div>
              <div class="status-info">
                <div class="status-label">비자 만료 예정</div>
                <div class="status-value">{{ statistics?.expiringVisasCount || 0 }}명</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">🚨</div>
              <div class="status-info">
                <div class="status-label">긴급 만료</div>
                <div class="status-value">{{ statistics?.urgentExpiringVisasCount || 0 }}명</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 직원 관리 섹션 -->
      <div class="employee-section">
        <div class="section-header">
          <h3>📋 직원 목록</h3>
          <button @click="showAddModal = true" class="add-btn">
            <span>➕</span>
            새 직원 등록
          </button>
        </div>

        <!-- 필터 및 검색 -->
        <div class="filters">
          <div class="search-box">
            <input 
              v-model="searchKeyword" 
              type="text" 
              placeholder="직원명 또는 전화번호 검색..."
              class="search-input"
              @keyup.enter="searchEmployees"
            >
            <span class="search-icon">🔍</span>
          </div>
          <select v-model="selectedDepartment" class="department-select">
            <option value="">전체 부서</option>
            <option value="농작업">농작업</option>
            <option value="포장">포장</option>
            <option value="운송">운송</option>
            <option value="관리">관리</option>
          </select>
          <select v-model="selectedNationality" class="nationality-select">
            <option value="">전체 국적</option>
            <option value="베트남">베트남</option>
            <option value="캄보디아">캄보디아</option>
            <option value="네팔">네팔</option>
            <option value="미얀마">미얀마</option>
            <option value="태국">태국</option>
          </select>
          <button @click="loadExpiringVisas" class="visa-btn">
            🛂 비자 만료 예정
          </button>
        </div>

        <!-- 로딩 상태 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>직원 목록을 불러오는 중...</p>
        </div>

        <!-- 에러 상태 -->
        <div v-else-if="error" class="error-container">
          <div class="error-icon">⚠️</div>
          <p class="error-text">{{ error }}</p>
          <button @click="loadEmployees" class="retry-btn">다시 시도</button>
        </div>

        <!-- 직원 목록 -->
        <div v-else class="employee-grid">
          <div 
            v-for="employee in filteredEmployees" 
            :key="employee.id"
            class="employee-card"
            :class="{ inactive: !employee.isActive }"
          >
            <div class="employee-header">
              <div class="employee-name">{{ employee.name }}</div>
              <div class="employee-number">{{ employee.employeeNumber }}</div>
            </div>
            <div class="employee-info">
              <div class="info-row">
                <span class="info-label">국적:</span>
                <span class="info-value">{{ employee.nationality }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">부서:</span>
                <span class="info-value">{{ employee.department }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">직책:</span>
                <span class="info-value">{{ employee.position }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">입사일:</span>
                <span class="info-value">{{ formatDate(employee.hireDate) }}</span>
              </div>
              <div class="info-row">
                <span class="info-label">비자 만료:</span>
                <span class="info-value" :class="getVisaExpiryClass(employee.visaExpiryDate)">
                  {{ formatDate(employee.visaExpiryDate) }}
                </span>
              </div>
              <div class="info-row">
                <span class="info-label">기본급:</span>
                <span class="info-value">{{ formatCurrency(employee.baseSalary) }}</span>
              </div>
            </div>
            <div class="employee-actions">
              <button @click="editEmployee(employee)" class="edit-btn">✏️ 수정</button>
              <button @click="viewSalary(employee)" class="salary-btn">💰 급여</button>
              <button @click="toggleEmployeeStatus(employee)" class="toggle-btn">
                {{ employee.isActive ? '⏸️ 비활성화' : '▶️ 활성화' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 직원이 없을 때 -->
        <div v-if="!loading && !error && filteredEmployees.length === 0" class="empty-state">
          <div class="empty-icon">👷</div>
          <p>등록된 직원이 없습니다.</p>
          <button @click="showAddModal = true" class="add-first-btn">첫 번째 직원 등록하기</button>
        </div>
      </div>
    </div>

    <!-- 직원 등록/수정 모달 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? '직원 수정' : '새 직원 등록' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveEmployee">
            <div class="form-row">
              <div class="form-group">
                <label>이름 *</label>
                <input 
                  v-model="employeeForm.name" 
                  type="text" 
                  required
                  placeholder="직원 이름"
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>영문명</label>
                <input 
                  v-model="employeeForm.englishName" 
                  type="text" 
                  placeholder="영문 이름"
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>국적 *</label>
                <select v-model="employeeForm.nationality" class="form-select" required>
                  <option value="">국적 선택</option>
                  <option value="베트남">베트남</option>
                  <option value="캄보디아">캄보디아</option>
                  <option value="네팔">네팔</option>
                  <option value="미얀마">미얀마</option>
                  <option value="태국">태국</option>
                </select>
              </div>
              <div class="form-group">
                <label>성별</label>
                <select v-model="employeeForm.gender" class="form-select">
                  <option value="">성별 선택</option>
                  <option value="MALE">남성</option>
                  <option value="FEMALE">여성</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>생년월일</label>
                <input 
                  v-model="employeeForm.birthDate" 
                  type="date" 
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>전화번호 *</label>
                <input 
                  v-model="employeeForm.phone" 
                  type="tel" 
                  required
                  placeholder="010-1234-5678"
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>이메일</label>
                <input 
                  v-model="employeeForm.email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>부서 *</label>
                <select v-model="employeeForm.department" class="form-select" required>
                  <option value="">부서 선택</option>
                  <option value="농작업">농작업</option>
                  <option value="포장">포장</option>
                  <option value="운송">운송</option>
                  <option value="관리">관리</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>직책 *</label>
                <input 
                  v-model="employeeForm.position" 
                  type="text" 
                  required
                  placeholder="직책"
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>고용 형태</label>
                <select v-model="employeeForm.workType" class="form-select">
                  <option value="">고용 형태 선택</option>
                  <option value="FULL_TIME">정규직</option>
                  <option value="PART_TIME">파트타임</option>
                  <option value="CONTRACT">계약직</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>기본급 *</label>
                <input 
                  v-model="employeeForm.baseSalary" 
                  type="number" 
                  required
                  min="0"
                  placeholder="기본급"
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>급여일</label>
                <input 
                  v-model="employeeForm.salaryDay" 
                  type="number" 
                  min="1"
                  max="31"
                  placeholder="25"
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>입사일 *</label>
                <input 
                  v-model="employeeForm.hireDate" 
                  type="date" 
                  required
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>외국인등록번호</label>
                <input 
                  v-model="employeeForm.alienRegistrationNumber" 
                  type="text" 
                  placeholder="외국인등록번호"
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>여권번호</label>
                <input 
                  v-model="employeeForm.passportNumber" 
                  type="text" 
                  placeholder="여권번호"
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>비자 종류</label>
                <select v-model="employeeForm.visaType" class="form-select">
                  <option value="">비자 종류 선택</option>
                  <option value="E-9">E-9 (비전문취업)</option>
                  <option value="E-7">E-7 (특정활동)</option>
                  <option value="F-4">F-4 (재외동포)</option>
                </select>
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>비자 발급일</label>
                <input 
                  v-model="employeeForm.visaIssueDate" 
                  type="date" 
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>비자 만료일</label>
                <input 
                  v-model="employeeForm.visaExpiryDate" 
                  type="date" 
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-row">
              <div class="form-group">
                <label>입국일</label>
                <input 
                  v-model="employeeForm.entryDate" 
                  type="date" 
                  class="form-input"
                >
              </div>
              <div class="form-group">
                <label>예상 출국일</label>
                <input 
                  v-model="employeeForm.expectedExitDate" 
                  type="date" 
                  class="form-input"
                >
              </div>
            </div>
            <div class="form-actions">
              <button type="button" @click="closeModal" class="cancel-btn">취소</button>
              <button type="submit" class="save-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>{{ showEditModal ? '수정' : '등록' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import apiClient from '@/utils/axios'

const layoutRef = ref(null)
const router = useRouter()

const employees = ref([])
const statistics = ref({
  totalActiveEmployees: 0,
  expiringVisasCount: 0,
  urgentExpiringVisasCount: 0
})
const loading = ref(false)
const error = ref('')
const saving = ref(false)

const searchKeyword = ref('')
const selectedDepartment = ref('')
const selectedNationality = ref('')

const showAddModal = ref(false)
const showEditModal = ref(false)
const editingEmployee = ref(null)

const employeeForm = ref({
  name: '',
  englishName: '',
  nationality: '',
  birthDate: '',
  gender: '',
  phone: '',
  email: '',
  alienRegistrationNumber: '',
  passportNumber: '',
  visaType: '',
  visaIssueDate: '',
  visaExpiryDate: '',
  entryDate: '',
  expectedExitDate: '',
  hireDate: '',
  position: '',
  department: '',
  workType: '',
  baseSalary: '',
  salaryDay: ''
})

const filteredEmployees = computed(() => {
  let filtered = employees.value

  if (selectedDepartment.value) {
    filtered = filtered.filter(employee => employee.department === selectedDepartment.value)
  }

  if (selectedNationality.value) {
    filtered = filtered.filter(employee => employee.nationality === selectedNationality.value)
  }

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(employee => 
      employee.name.toLowerCase().includes(keyword) ||
      employee.phone.includes(keyword) ||
      (employee.email && employee.email.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

const loadEmployees = async () => {
  loading.value = true
  error.value = ''
  
  try {
    const response = await apiClient.get('/employees/active')
    if (response.data.success && Array.isArray(response.data.data)) {
      employees.value = response.data.data
    } else {
      employees.value = []
    }
    await loadStatistics()
  } catch (err) {
    error.value = '직원 목록을 불러오는데 실패했습니다.'
    employees.value = []
  } finally {
    loading.value = false
  }
}

const loadStatistics = async () => {
  try {
    const response = await apiClient.get('/employees/statistics')
    if (response.data.success) {
      statistics.value = response.data.data
    }
  } catch (err) {
    // 통계 로드 실패 시 무시
  }
}

const searchEmployees = async () => {
  if (!searchKeyword.value.trim()) {
    await loadEmployees()
    return
  }
  
  loading.value = true
  try {
    const response = await apiClient.get(`/employees/search?keyword=${encodeURIComponent(searchKeyword.value)}`)
    if (response.data.success && Array.isArray(response.data.data)) {
      employees.value = response.data.data
    } else {
      employees.value = []
    }
  } catch (err) {
    error.value = '직원 검색에 실패했습니다.'
    employees.value = []
  } finally {
    loading.value = false
  }
}

const loadExpiringVisas = async () => {
  loading.value = true
  try {
    const response = await apiClient.get('/employees/expiring-visas')
    if (response.data.success && Array.isArray(response.data.data)) {
      employees.value = response.data.data
    } else {
      employees.value = []
    }
  } catch (err) {
    error.value = '비자 만료 예정자 목록을 불러오는데 실패했습니다.'
    employees.value = []
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('ko-KR')
}

const formatCurrency = (amount) => {
  if (!amount) return '0원'
  return new Intl.NumberFormat('ko-KR').format(amount) + '원'
}

const getVisaExpiryClass = (dateString) => {
  if (!dateString) return ''
  const expiryDate = new Date(dateString)
  const now = new Date()
  const diffTime = expiryDate - now
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  if (diffDays < 0) return 'expired'
  if (diffDays <= 30) return 'urgent'
  if (diffDays <= 90) return 'warning'
  return ''
}

const editEmployee = (employee) => {
  editingEmployee.value = employee
  employeeForm.value = { ...employee }
  showEditModal.value = true
}

const viewSalary = (employee) => {
  router.push(`/salary?employeeId=${employee.id}`)
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingEmployee.value = null
  employeeForm.value = {
    name: '',
    englishName: '',
    nationality: '',
    birthDate: '',
    gender: '',
    phone: '',
    email: '',
    alienRegistrationNumber: '',
    passportNumber: '',
    visaType: '',
    visaIssueDate: '',
    visaExpiryDate: '',
    entryDate: '',
    expectedExitDate: '',
    hireDate: '',
    position: '',
    department: '',
    workType: '',
    baseSalary: '',
    salaryDay: ''
  }
}

const saveEmployee = async () => {
  saving.value = true
  
  try {
    // 데이터 형식 변환 및 검증
    const employeeData = {
      name: employeeForm.value.name,
      englishName: employeeForm.value.englishName || null,
      nationality: employeeForm.value.nationality,
      birthDate: employeeForm.value.birthDate || null,
      gender: employeeForm.value.gender || null,
      phone: employeeForm.value.phone,
      email: employeeForm.value.email || null,
      alienRegistrationNumber: employeeForm.value.alienRegistrationNumber || null,
      passportNumber: employeeForm.value.passportNumber || null,
      visaType: employeeForm.value.visaType || null,
      visaIssueDate: employeeForm.value.visaIssueDate || null,
      visaExpiryDate: employeeForm.value.visaExpiryDate || null,
      entryDate: employeeForm.value.entryDate || null,
      expectedExitDate: employeeForm.value.expectedExitDate || null,
      hireDate: employeeForm.value.hireDate,
      position: employeeForm.value.position,
      department: employeeForm.value.department,
      workType: employeeForm.value.workType || null,
      baseSalary: parseInt(employeeForm.value.baseSalary) || 0,
      salaryDay: parseInt(employeeForm.value.salaryDay) || null
    }
    
    // 필수 필드 검증
    if (!employeeData.name || !employeeData.nationality || !employeeData.phone || 
        !employeeData.hireDate || !employeeData.position || !employeeData.department) {
      alert('필수 필드를 모두 입력해주세요.')
      return
    }
    
    if (showEditModal.value) {
      const response = await apiClient.put(`/employees/${editingEmployee.value.id}`, employeeData)
      if (response.data.success) {
        alert('직원이 성공적으로 수정되었습니다.')
      } else {
        alert('직원 수정에 실패했습니다: ' + (response.data.message || response.data.error))
      }
    } else {
      const response = await apiClient.post('/employees', employeeData)
      if (response.data.success) {
        alert('직원이 성공적으로 등록되었습니다.')
      } else {
        alert('직원 등록에 실패했습니다: ' + (response.data.message || response.data.error))
      }
    }
    
    closeModal()
    await loadEmployees()
  } catch (err) {
    const errorMsg = err.response?.data?.error || err.response?.data?.message || err.message
    alert('직원 저장에 실패했습니다: ' + errorMsg)
  } finally {
    saving.value = false
  }
}

const toggleEmployeeStatus = async (employee) => {
  try {
    await apiClient.delete(`/employees/${employee.id}`)
    await loadEmployees()
  } catch (err) {
    alert('직원 상태 변경에 실패했습니다.')
  }
}

onMounted(async () => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/staff')
  }
  
  await loadEmployees()
})
</script>

<style scoped>
.employee-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.employee-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
  align-items: center;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.department-select,
.nationality-select {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 120px;
  cursor: pointer;
}

.visa-btn {
  padding: 12px 15px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.visa-btn:hover {
  background: #c0392b;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-container {
  text-align: center;
  padding: 40px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin: 20px 0;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.error-text {
  color: #856404;
  margin-bottom: 15px;
  font-size: 1.1rem;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e67e22;
}

.employee-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.employee-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.employee-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.employee-card.inactive {
  opacity: 0.6;
  background: #f8f9fa;
}

.employee-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.employee-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.employee-number {
  font-size: 0.9rem;
  color: #7f8c8d;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
}

.employee-info {
  margin-bottom: 15px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.info-label {
  color: #7f8c8d;
  font-weight: 500;
}

.info-value {
  color: #2c3e50;
  font-weight: 500;
}

.info-value.expired {
  color: #e74c3c;
  font-weight: bold;
}

.info-value.urgent {
  color: #f39c12;
  font-weight: bold;
}

.info-value.warning {
  color: #f39c12;
}

.employee-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.edit-btn,
.salary-btn,
.toggle-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.edit-btn {
  background: #3498db;
  color: white;
}

.salary-btn {
  background: #9b59b6;
  color: white;
}

.toggle-btn {
  background: #95a5a6;
  color: white;
}

.edit-btn:hover,
.salary-btn:hover,
.toggle-btn:hover {
  transform: translateY(-1px);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.add-first-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.cancel-btn:hover,
.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .employee-grid {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .search-box {
    min-width: auto;
  }
}
</style> 