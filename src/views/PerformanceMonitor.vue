<template>
  <Layout ref="layoutRef">
    <div class="performance-monitor-container">
      <!-- 페이지 헤더 -->
      <div class="main-title">
        <h1>📊 성능 모니터링 대시보드</h1>
        <p class="subtitle">API 성능과 시스템 리소스를 실시간으로 모니터링합니다</p>
      </div>

      <!-- 모니터링 컨트롤 -->
      <div class="monitoring-controls">
        <div class="control-buttons">
          <button 
            @click="startMonitoring" 
            :disabled="isMonitoring" 
            class="btn btn-success"
          >
            <span v-if="isMonitoring" class="loading-spinner">🔄</span>
            {{ isMonitoring ? '모니터링 중...' : '실시간 모니터링 시작' }}
          </button>
          
          <button 
            @click="stopMonitoring" 
            :disabled="!isMonitoring" 
            class="btn btn-warning"
          >
            모니터링 중지
          </button>
          
          <button @click="exportPerformanceData" class="btn btn-info">
            📥 성능 데이터 내보내기
          </button>
          
          <button @click="clearHistory" class="btn btn-danger">
            🗑️ 히스토리 초기화
          </button>
        </div>
      </div>

      <!-- 실시간 성능 지표 -->
      <div class="performance-metrics">
        <div class="metrics-grid">
          <!-- 평균 응답 시간 -->
          <div class="metric-card">
            <div class="metric-icon">⏱️</div>
            <div class="metric-content">
              <div class="metric-title">평균 응답 시간</div>
              <div class="metric-value">{{ averageResponseTime }}ms</div>
              <div class="metric-trend" :class="getTrendClass(averageResponseTime, 3000)">
                {{ getTrendText(averageResponseTime, 3000) }}
              </div>
            </div>
          </div>

          <!-- 성공률 -->
          <div class="metric-card">
            <div class="metric-icon">✅</div>
            <div class="metric-content">
              <div class="metric-title">API 성공률</div>
              <div class="metric-value">{{ successRate }}%</div>
              <div class="metric-trend" :class="getTrendClass(successRate, 95, true)">
                {{ getTrendText(successRate, 95, true) }}
              </div>
            </div>
          </div>

          <!-- 총 요청 수 -->
          <div class="metric-card">
            <div class="metric-icon">📈</div>
            <div class="metric-content">
              <div class="metric-title">총 요청 수</div>
              <div class="metric-value">{{ totalRequests }}</div>
              <div class="metric-subtitle">이번 세션</div>
            </div>
          </div>

          <!-- 처리된 데이터 -->
          <div class="metric-card">
            <div class="metric-icon">💾</div>
            <div class="metric-content">
              <div class="metric-title">처리된 데이터</div>
              <div class="metric-value">{{ formatDataSize(totalDataProcessed) }}</div>
              <div class="metric-subtitle">총 크기</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 성능 히스토리 -->
      <div class="performance-history">
        <div class="section-header">
          <h2>📋 최근 성능 히스토리</h2>
          <div class="history-controls">
            <select v-model="selectedFilter" class="filter-select">
              <option value="all">모든 요청</option>
              <option value="slow">느린 요청 (>3초)</option>
              <option value="large">큰 데이터 (>1MB)</option>
              <option value="failed">실패한 요청</option>
            </select>
          </div>
        </div>

        <div class="history-table">
          <table>
            <thead>
              <tr>
                <th>시간</th>
                <th>API 엔드포인트</th>
                <th>메서드</th>
                <th>응답 시간</th>
                <th>데이터 크기</th>
                <th>상태</th>
                <th>상세</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="metric in filteredHistory" :key="metric.timestamp" :class="getRowClass(metric)">
                <td>{{ formatTime(metric.timestamp) }}</td>
                <td>{{ metric.apiEndpoint || 'N/A' }}</td>
                <td>{{ metric.method || 'N/A' }}</td>
                <td :class="getResponseTimeClass(metric.responseTime)">
                  {{ metric.responseTime.toFixed(2) }}ms
                </td>
                <td>{{ formatDataSize(metric.dataSize) }}</td>
                <td>
                  <span :class="getStatusClass(metric.success)">
                    {{ metric.success ? '✅ 성공' : '❌ 실패' }}
                  </span>
                </td>
                <td>
                  <button @click="showMetricDetails(metric)" class="btn-details">
                    🔍
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 성능 리포트 -->
      <div class="performance-report">
        <div class="section-header">
          <h2>📊 성능 리포트</h2>
          <button @click="generateReport" class="btn btn-primary">
            리포트 생성
          </button>
        </div>

        <div v-if="currentReport" class="report-content">
          <div class="report-summary">
            <h3>📈 요약</h3>
            <div class="summary-grid">
              <div class="summary-item">
                <div class="summary-label">총 요청 수</div>
                <div class="summary-value">{{ currentReport.summary.totalRequests }}</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">평균 응답 시간</div>
                <div class="summary-value">{{ currentReport.summary.averageResponseTime.toFixed(2) }}ms</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">성공률</div>
                <div class="summary-value">{{ currentReport.summary.successRate.toFixed(1) }}%</div>
              </div>
              <div class="summary-item">
                <div class="summary-label">느린 요청</div>
                <div class="summary-value">{{ currentReport.summary.slowRequests }}건</div>
              </div>
            </div>
          </div>

          <div class="report-recommendations">
            <h3>💡 개선 권장사항</h3>
            <ul v-if="currentReport.recommendations.length > 0">
              <li v-for="(recommendation, index) in currentReport.recommendations" :key="index">
                {{ recommendation }}
              </li>
            </ul>
            <p v-else class="no-recommendations">
              현재 성능이 양호합니다! 🎉
            </p>
          </div>
        </div>
      </div>

      <!-- 메트릭 상세 모달 -->
      <div v-if="selectedMetric" class="modal-overlay" @click="closeModal">
        <div class="modal-content" @click.stop>
          <div class="modal-header">
            <h3>🔍 메트릭 상세 정보</h3>
            <button @click="closeModal" class="modal-close">×</button>
          </div>
          <div class="modal-body">
            <div class="metric-details">
              <div class="detail-item">
                <label>API 엔드포인트:</label>
                <span>{{ selectedMetric.apiEndpoint || 'N/A' }}</span>
              </div>
              <div class="detail-item">
                <label>HTTP 메서드:</label>
                <span>{{ selectedMetric.method || 'N/A' }}</span>
              </div>
              <div class="detail-item">
                <label>응답 시간:</label>
                <span>{{ selectedMetric.responseTime.toFixed(2) }}ms</span>
              </div>
              <div class="detail-item">
                <label>데이터 크기:</label>
                <span>{{ formatDataSize(selectedMetric.dataSize) }}</span>
              </div>
              <div class="detail-item">
                <label>데이터 개수:</label>
                <span>{{ selectedMetric.dataCount }}건</span>
              </div>
              <div class="detail-item">
                <label>상태:</label>
                <span :class="getStatusClass(selectedMetric.success)">
                  {{ selectedMetric.success ? '성공' : '실패' }}
                </span>
              </div>
              <div v-if="selectedMetric.error" class="detail-item">
                <label>오류:</label>
                <span class="error-text">{{ selectedMetric.error }}</span>
              </div>
              <div class="detail-item">
                <label>타임스탬프:</label>
                <span>{{ new Date(selectedMetric.timestamp).toLocaleString() }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Layout from '../components/Layout.vue'
import { usePerformanceMonitor } from '../composables/usePerformanceMonitor'

const layoutRef = ref(null)

// 성능 모니터링
const {
  performanceMetrics,
  performanceHistory,
  isMonitoring,
  averageResponseTime,
  totalDataProcessed,
  successRate,
  slowRequests,
  largeDataRequests,
  startMonitoring,
  stopMonitoring,
  generatePerformanceReport,
  exportPerformanceData,
  clearHistory
} = usePerformanceMonitor()

// 로컬 상태
const selectedFilter = ref('all')
const currentReport = ref(null)
const selectedMetric = ref(null)

// 필터링된 히스토리
const filteredHistory = computed(() => {
  let filtered = performanceHistory.value

  switch (selectedFilter.value) {
    case 'slow':
      filtered = filtered.filter(metric => metric.responseTime > 3000)
      break
    case 'large':
      filtered = filtered.filter(metric => metric.dataSize > 1024 * 1024)
      break
    case 'failed':
      filtered = filtered.filter(metric => !metric.success)
      break
  }

  return filtered.slice(-20) // 최근 20개만 표시
})

// 총 요청 수
const totalRequests = computed(() => performanceHistory.value.length)

// 유틸리티 함수들
const formatDataSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getTrendClass = (value, threshold, isHigherBetter = false) => {
  if (isHigherBetter) {
    return value >= threshold ? 'trend-good' : 'trend-bad'
  }
  return value <= threshold ? 'trend-good' : 'trend-bad'
}

const getTrendText = (value, threshold, isHigherBetter = false) => {
  if (isHigherBetter) {
    return value >= threshold ? '양호' : '개선 필요'
  }
  return value <= threshold ? '양호' : '느림'
}

const getRowClass = (metric) => {
  if (!metric.success) return 'row-failed'
  if (metric.responseTime > 3000) return 'row-slow'
  if (metric.dataSize > 1024 * 1024) return 'row-large'
  return ''
}

const getResponseTimeClass = (responseTime) => {
  if (responseTime > 5000) return 'response-time-slow'
  if (responseTime > 3000) return 'response-time-warning'
  return 'response-time-good'
}

const getStatusClass = (success) => {
  return success ? 'status-success' : 'status-failed'
}

const showMetricDetails = (metric) => {
  selectedMetric.value = metric
}

const closeModal = () => {
  selectedMetric.value = null
}

const generateReport = () => {
  currentReport.value = generatePerformanceReport()
}

// 초기화
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/performance-monitor')
  }
  
  // 자동으로 모니터링 시작
  startMonitoring(10000) // 10초마다 체크
})

onUnmounted(() => {
  stopMonitoring()
})
</script>

<style scoped>
.performance-monitor-container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
}

.main-title h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

/* 모니터링 컨트롤 */
.monitoring-controls {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.control-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

/* 성능 지표 */
.performance-metrics {
  margin-bottom: 30px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.metric-icon {
  font-size: 2.5rem;
}

.metric-content {
  flex: 1;
}

.metric-title {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.metric-value {
  font-size: 1.8rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
}

.metric-subtitle {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.metric-trend {
  font-size: 0.8rem;
  font-weight: bold;
}

.trend-good {
  color: #27ae60;
}

.trend-bad {
  color: #e74c3c;
}

/* 성능 히스토리 */
.performance-history {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.history-controls {
  display: flex;
  gap: 10px;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
}

.history-table {
  overflow-x: auto;
}

.history-table table {
  width: 100%;
  border-collapse: collapse;
}

.history-table th,
.history-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.history-table th {
  background: #f8f9fa;
  font-weight: bold;
  color: #2c3e50;
}

.history-table tr:hover {
  background: #f8f9fa;
}

.row-failed {
  background: #fff5f5;
}

.row-slow {
  background: #fff8e1;
}

.row-large {
  background: #f0f8ff;
}

.response-time-good {
  color: #27ae60;
  font-weight: bold;
}

.response-time-warning {
  color: #f39c12;
  font-weight: bold;
}

.response-time-slow {
  color: #e74c3c;
  font-weight: bold;
}

.status-success {
  color: #27ae60;
  font-weight: bold;
}

.status-failed {
  color: #e74c3c;
  font-weight: bold;
}

.btn-details {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1rem;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.btn-details:hover {
  background: #f0f0f0;
}

/* 성능 리포트 */
.performance-report {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.report-content {
  margin-top: 20px;
}

.report-summary {
  margin-bottom: 30px;
}

.report-summary h3 {
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.summary-item {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.summary-label {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 5px;
}

.summary-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
}

.report-recommendations h3 {
  color: #2c3e50;
  margin: 0 0 15px 0;
}

.report-recommendations ul {
  margin: 0;
  padding-left: 20px;
}

.report-recommendations li {
  margin-bottom: 8px;
  color: #2c3e50;
  line-height: 1.4;
}

.no-recommendations {
  color: #27ae60;
  font-style: italic;
  margin: 0;
}

/* 모달 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-close:hover {
  color: #2c3e50;
}

.modal-body {
  padding: 20px;
}

.metric-details {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-item label {
  font-weight: bold;
  color: #2c3e50;
  min-width: 120px;
}

.detail-item span {
  color: #7f8c8d;
}

.error-text {
  color: #e74c3c !important;
  font-weight: bold;
}

/* 버튼 스타일 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #229954;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #e67e22;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: #138496;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

@media (max-width: 768px) {
  .control-buttons {
    flex-direction: column;
  }
  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .summary-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .history-table {
    font-size: 0.8rem;
  }
  
  .history-table th,
  .history-table td {
    padding: 8px;
  }
}
</style> 