<template>
  <Layout ref="layoutRef">
    <div class="product-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>🌾 상품 관리</h1>
        <p class="page-subtitle">농산물 상품 정보를 관리합니다</p>
      </div>

      <!-- 시스템 상태 및 초기화 -->
      <div class="system-section">
        <div class="system-status">
          <h3>📊 시스템 상태</h3>
          <div class="status-cards">
            <div class="status-card">
              <div class="status-icon">📦</div>
              <div class="status-info">
                <div class="status-label">총 상품 수</div>
                <div class="status-value">{{ statistics?.totalProducts || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">✅</div>
              <div class="status-info">
                <div class="status-label">활성 상품</div>
                <div class="status-value">{{ statistics?.activeProducts || 0 }}개</div>
              </div>
            </div>
            <div class="status-card">
              <div class="status-icon">📈</div>
              <div class="status-info">
                <div class="status-label">경매 데이터</div>
                <div class="status-value">{{ auctionDataCount || 0 }}건</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 상품 관리 섹션 -->
      <div class="product-section">
        <div class="section-header">
          <h3>📋 상품 목록</h3>
          <button @click="showAddModal = true" class="add-btn">
            <span>➕</span>
            새 상품 등록
          </button>
        </div>

        <!-- 필터 및 검색 -->
        <div class="filters">
          <div class="search-box">
            <input 
              v-model="searchKeyword" 
              type="text" 
              placeholder="상품명 검색..."
              class="search-input"
            >
            <span class="search-icon">🔍</span>
          </div>
          <select v-model="selectedCategory" class="category-select">
            <option value="">전체 카테고리</option>
            <option value="채소">채소</option>
            <option value="구황작물">구황작물</option>
            <option value="과일">과일</option>
            <option value="곡물">곡물</option>
            <option value="기타">기타</option>
          </select>
        </div>

        <!-- 에러 처리 컴포넌트 -->
        <ErrorHandler 
          v-if="hasError"
          :error="error"
          :can-retry="canRetry"
          :error-stats="errorStats"
          @retry="() => retry(loadProducts, 1000)"
          @close="clearError"
        />

        <!-- 로딩 상태 -->
        <div v-else-if="isLoading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <p>상품 목록을 불러오는 중...</p>
        </div>

        <!-- 상품 목록 -->
        <div v-if="!isLoading && !hasError && filteredProducts.length > 0" class="product-grid">
          <div 
            v-for="product in filteredProducts" 
            :key="product.id"
            class="product-card"
            :class="{ inactive: !product.isActive }"
          >
            <div class="product-header">
              <div class="product-name">{{ product.productName }}</div>
              <div class="product-status" :class="{ active: product.isActive, inactive: !product.isActive }">
                {{ product.isActive ? '활성' : '비활성' }}
              </div>
            </div>
            <div class="product-category">{{ product.category || '카테고리 없음' }}</div>
            <div class="product-description">{{ product.description || '설명 없음' }}</div>
            <div class="product-actions">
              <button @click="editProduct(product)" class="edit-btn">✏️ 수정</button>
              <button @click="toggleProductStatus(product)" class="toggle-btn">
                {{ product.isActive ? '⏸️ 비활성화' : '▶️ 활성화' }}
              </button>
            </div>
          </div>
        </div>

        <!-- 상품이 없을 때 -->
        <div v-if="!isLoading && !hasError && filteredProducts.length === 0" class="empty-state">
          <div class="empty-icon">📦</div>
          <p>등록된 상품이 없습니다.</p>
          <button @click="showAddModal = true" class="add-first-btn">첫 번째 상품 등록하기</button>
        </div>
      </div>
    </div>

    <!-- 상품 등록/수정 모달 -->
    <div v-if="showAddModal || showEditModal" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ showEditModal ? '상품 수정' : '새 상품 등록' }}</h3>
          <button @click="closeModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveProduct">
            <div class="form-group">
              <label>상품명 *</label>
              <input 
                v-model="productForm.productName" 
                type="text" 
                required
                placeholder="상품명을 입력하세요"
                class="form-input"
              >
            </div>
            <div class="form-group">
              <label>카테고리</label>
              <select v-model="productForm.category" class="form-select">
                <option value="">카테고리 선택</option>
                <option value="채소">채소</option>
                <option value="구황작물">구황작물</option>
                <option value="과일">과일</option>
                <option value="곡물">곡물</option>
                <option value="기타">기타</option>
              </select>
            </div>
            <div class="form-group">
              <label>설명</label>
              <textarea 
                v-model="productForm.description" 
                placeholder="상품에 대한 설명을 입력하세요"
                class="form-textarea"
                rows="3"
              ></textarea>
            </div>
            <div class="form-actions">
              <button type="button" @click="closeModal" class="cancel-btn">취소</button>
              <button type="submit" class="save-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>{{ showEditModal ? '수정' : '등록' }}</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import Layout from '../components/Layout.vue'
import apiClient from '@/utils/axios'
import { useApiError } from '@/composables/useApiError'
import ErrorHandler from '@/components/ErrorHandler.vue'

const layoutRef = ref(null)

// API 에러 처리
const { 
  error, 
  isLoading, 
  hasError, 
  canRetry, 
  errorStats,
  handleApiError, 
  retry, 
  clearError 
} = useApiError()

// 상태 관리
const products = ref([])
const statistics = ref({
  totalProducts: 0,
  activeProducts: 0
})
const saving = ref(false)
const auctionDataCount = ref(0)

// 필터 상태
const searchKeyword = ref('')
const selectedCategory = ref('')

// 모달 상태
const showAddModal = ref(false)
const showEditModal = ref(false)
const editingProduct = ref(null)

// 폼 데이터
const productForm = ref({
  productName: '',
  category: '',
  description: ''
})

// 계산된 속성들
const filteredProducts = computed(() => {
  let filtered = products.value

  if (selectedCategory.value) {
    filtered = filtered.filter(product => product.category === selectedCategory.value)
  }

  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(product => 
      product.productName.toLowerCase().includes(keyword) ||
      (product.description && product.description.toLowerCase().includes(keyword))
    )
  }

  return filtered
})

// API 호출 함수들
const loadProducts = async () => {
  try {
    const response = await apiClient.get('/products')
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success && Array.isArray(response.data.data)) {
      products.value = response.data.data
    } else {
      products.value = []
    }
    calculateStatistics()
  } catch (err) {
    handleApiError(err, '상품 목록 로드')
    products.value = []
  }
}

const loadAuctionDataCount = async () => {
  try {
    const response = await apiClient.get('/auction/count')
    // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
    if (response.data.success) {
      auctionDataCount.value = response.data.data?.count || response.data.count || 0
    } else {
      auctionDataCount.value = 0
    }
  } catch (err) {
    console.error('경매 데이터 수 로드 실패:', err)
    auctionDataCount.value = 0
  }
}

const calculateStatistics = () => {
  const totalProducts = products.value.length
  const activeProducts = products.value.filter(product => product.isActive).length
  
  statistics.value = {
    totalProducts,
    activeProducts
  }
}

// 모달 관련 함수들
const editProduct = (product) => {
  editingProduct.value = product
  productForm.value = {
    productName: product.productName,
    category: product.category || '',
    description: product.description || ''
  }
  showEditModal.value = true
}

const closeModal = () => {
  showAddModal.value = false
  showEditModal.value = false
  editingProduct.value = null
  productForm.value = {
    productName: '',
    category: '',
    description: ''
  }
}

// 저장 함수들
const saveProduct = async () => {
  saving.value = true
  
  try {
    const productData = { ...productForm.value }
    
    if (showEditModal.value) {
      await apiClient.put(`/products/${editingProduct.value.id}`, productData)
      alert('상품이 성공적으로 수정되었습니다.')
    } else {
      await apiClient.post('/products', productData)
      alert('상품이 성공적으로 등록되었습니다.')
    }
    
    closeModal()
    await loadProducts()
  } catch (err) {
    console.error('상품 저장 실패:', err)
    const errorMsg = err.response?.data?.error || err.message
    alert('상품 저장에 실패했습니다: ' + errorMsg)
  } finally {
    saving.value = false
  }
}

const toggleProductStatus = async (product) => {
  try {
    await apiClient.delete(`/products/${product.id}`)
    await loadProducts()
  } catch (err) {
    console.error('상품 상태 변경 실패:', err)
    alert('상품 상태 변경에 실패했습니다.')
  }
}

// 컴포넌트 마운트 시 데이터 로드
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/product')
  }
  
  loadProducts()
  loadAuctionDataCount()
})
</script>

<style scoped>
.product-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.system-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.system-status h3 {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.status-card {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.status-icon { font-size: 2rem; }
.status-info { flex: 1; }
.status-label { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.status-value { font-size: 1.5rem; font-weight: bold; color: #2c3e50; }

.product-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.section-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.add-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-btn:hover {
  background: #229954;
  transform: translateY(-2px);
}

.filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;
  flex-wrap: wrap;
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.search-input {
  width: 100%;
  padding: 12px 40px 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.category-select {
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 1rem;
  min-width: 150px;
  cursor: pointer;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.product-card {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.product-card.inactive {
  opacity: 0.6;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.product-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.product-status {
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: bold;
}

.product-status.active {
  background: #d4edda;
  color: #155724;
}

.product-status.inactive {
  background: #f8d7da;
  color: #721c24;
}

.product-category {
  font-size: 0.9rem;
  color: #7f8c8d;
  margin-bottom: 10px;
}

.product-description {
  font-size: 0.9rem;
  color: #2c3e50;
  margin-bottom: 15px;
  line-height: 1.4;
}

.product-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.edit-btn,
.toggle-btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.edit-btn { background: #3498db; color: white; }
.toggle-btn { background: #e74c3c; color: white; }

.edit-btn:hover,
.toggle-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
}

.add-first-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 15px 25px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  margin-top: 20px;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.save-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.save-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.cancel-btn:hover,
.save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .product-grid {
    grid-template-columns: 1fr;
  }
  
  .filters {
    flex-direction: column;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .product-actions {
    flex-direction: column;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style> 