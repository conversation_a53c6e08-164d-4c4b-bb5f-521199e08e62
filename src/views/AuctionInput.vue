<template>
  <Layout ref="layoutRef">
    <div class="auction-input-container">
      <!-- 페이지 헤더 -->
      <div class="main-title">
        <h1>📝 경매 데이터 입력</h1>
        <p class="subtitle">가락시장 경매 데이터를 수동으로 입력하거나 자동으로 불러올 수 있습니다</p>
        <div class="header-actions">
          <button @click="checkServerStatus" class="btn btn-info">
            🔍 서버 상태 확인
          </button>
          <button @click="initializeProducts" class="btn btn-secondary">
            🔄 상품 초기화
          </button>
        </div>
      </div>

      <!-- 알림 메시지 -->
      <div v-if="error" class="alert alert-error" @click="clearError">
        <div class="alert-icon">⚠️</div>
        <div class="alert-content">
          <h4>오류 발생</h4>
          <p>{{ error }}</p>
        </div>
        <button class="alert-close">×</button>
      </div>

      <div v-if="success" class="alert alert-success" @click="clearSuccess">
        <div class="alert-icon">✅</div>
        <div class="alert-content">
          <h4>성공</h4>
          <p>{{ success }}</p>
        </div>
        <button class="alert-close">×</button>
      </div>

      <!-- 성능 모니터링 -->
      <div v-if="getPerformanceStats.responseTime > 0" class="performance-section">
        <div class="section-header">
          <h2>🚀 성능 모니터링</h2>
        </div>
        <div class="performance-cards">
          <div class="performance-card">
            <div class="card-icon">⚡</div>
            <div class="card-content">
              <div class="card-title">응답 시간</div>
              <div class="card-value">{{ getPerformanceStats.averageResponseTime }}</div>
            </div>
          </div>
          <div class="performance-card">
            <div class="card-icon">📦</div>
            <div class="card-content">
              <div class="card-title">데이터 크기</div>
              <div class="card-value">{{ getPerformanceStats.dataSizeKB }}</div>
            </div>
          </div>
          <div class="performance-card">
            <div class="card-icon">📊</div>
            <div class="card-content">
              <div class="card-title">처리 건수</div>
              <div class="card-value">{{ getPerformanceStats.dataCount }}건</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 메인 컨텐츠 -->
      <div class="content-grid">
        <!-- 수동 입력 섹션 -->
        <div class="input-section">
          <div class="section-header">
            <h2>📝 수동 데이터 입력</h2>
            <p>개별 경매 데이터를 수동으로 입력합니다</p>
          </div>

          <form @submit.prevent="submitAuctionData" class="input-form">
            <div class="form-row">
              <div class="form-group">
                <label for="pumName">품목명 *</label>
                <input
                  id="pumName"
                  v-model="formData.PUM_NM"
                  type="text"
                  placeholder="예: 상추, 양배추, 토마토"
                  required
                />
              </div>
              <div class="form-group">
                <label for="adjDate">거래일자 *</label>
                <input
                  id="adjDate"
                  v-model="formData.ADJ_DT"
                  type="text"
                  placeholder="YYYYMMDD (예: 20241201)"
                  pattern="[0-9]{8}"
                  required
                />
                <small v-if="formattedDate">변환된 날짜: {{ formattedDate }}</small>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="avgPrice">평균가 *</label>
                <input
                  id="avgPrice"
                  v-model="formData.AVG_PRC"
                  type="number"
                  placeholder="0"
                  min="0"
                  required
                />
              </div>
              <div class="form-group">
                <label for="maxPrice">최고가</label>
                <input
                  id="maxPrice"
                  v-model="formData.MAX_PRC"
                  type="number"
                  placeholder="0"
                  min="0"
                />
              </div>
              <div class="form-group">
                <label for="minPrice">최저가</label>
                <input
                  id="minPrice"
                  v-model="formData.MIN_PRC"
                  type="number"
                  placeholder="0"
                  min="0"
                />
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label for="saleQty">판매수량</label>
                <input
                  id="saleQty"
                  v-model="formData.SALE_QTY"
                  type="number"
                  placeholder="0"
                  min="0"
                />
              </div>
              <div class="form-group">
                <label for="saleAmt">판매금액</label>
                <input
                  id="saleAmt"
                  v-model="formData.SALE_AMT"
                  type="number"
                  placeholder="0"
                  min="0"
                />
              </div>
            </div>

            <div class="form-actions">
              <button type="submit" :disabled="!isFormValid || loading" class="btn btn-primary">
                <span v-if="loading" class="loading-spinner">🔄</span>
                {{ loading ? '입력 중...' : '데이터 입력' }}
              </button>
              <button type="button" @click="resetForm" class="btn btn-secondary">초기화</button>
            </div>
          </form>
        </div>

        <!-- 자동 데이터 불러오기 섹션 -->
        <div class="auto-load-section">
          <div class="section-header">
            <h2>🔄 자동 데이터 불러오기</h2>
            <p>외부 API에서 경매 데이터를 자동으로 불러옵니다</p>
          </div>

          <div class="auto-load-options">
            <!-- 실제 데이터 불러오기 -->
            <div class="load-option">
              <div class="option-header">
                <h3>📊 실제 데이터 불러오기</h3>
                <p>가락시장 API에서 실제 경매 데이터를 불러옵니다</p>
              </div>
              <div class="option-actions">
                <button 
                  @click="loadTodayData" 
                  :disabled="isLoading" 
                  class="btn btn-success"
                >
                  <span v-if="isLoading" class="loading-spinner">🔄</span>
                  {{ isLoading ? '로딩 중...' : '금일 데이터 불러오기' }}
                </button>
                <button 
                  @click="loadBulkData" 
                  :disabled="isLoading" 
                  class="btn btn-warning"
                >
                  <span v-if="isLoading" class="loading-spinner">🔄</span>
                  {{ isLoading ? '로딩 중...' : '3년치 대량 데이터 불러오기' }}
                </button>
              </div>
            </div>

            <!-- 대량 데이터 불러오기 -->
            <div class="load-option">
              <div class="option-header">
                <h3>📊 대량 데이터 불러오기</h3>
                <p>과거 경매 데이터를 대량으로 불러옵니다</p>
              </div>

            </div>

            <!-- 관리자 기능 -->
            <div class="load-option admin-only">
              <div class="option-header">
                <h3>🗑️ 데이터 초기화 (관리자 전용)</h3>
                <p>모든 경매 데이터를 삭제합니다 (주의: 되돌릴 수 없음)</p>
              </div>
              <div class="option-actions">
                <button 
                  @click="truncateTable" 
                  :disabled="loading" 
                  class="btn btn-danger"
                >
                  <span v-if="loading" class="loading-spinner">🔄</span>
                  {{ loading ? '초기화 중...' : '테이블 초기화' }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>



      <!-- SSE 스트리밍 진행률 표시 -->
      <div v-if="isLoading" class="streaming-section" id="realDataProgress">
        <div class="streaming-header">
          <h3>🔄 데이터를 로드하는 중...</h3>
        </div>
        
        <div class="progress-container">
          <div class="progress-bar">
            <div 
              id="realDataProgressFill"
              class="progress-fill" 
              :style="{ width: progress + '%' }"
            ></div>
          </div>
        </div>

        <div class="streaming-logs">
          <h4>진행 로그</h4>
          <div class="log-container" id="realDataLogs">
            <div 
              v-for="(log, index) in logs" 
              :key="index" 
              class="log-item"
            >
              {{ log }}
            </div>
          </div>
        </div>
      </div>

      <!-- 완료 메시지 표시 -->
      <div v-if="status" class="status" :class="status.includes('✅') ? 'success' : 'error'">
        {{ status }}
      </div>

      <!-- 도움말 섹션 -->
      <div class="help-section">
        <div class="section-header">
          <h2>💡 사용 가이드</h2>
        </div>
        
        <div class="help-content">
          <div class="help-item">
            <h4>📝 수동 입력</h4>
            <ul>
              <li><strong>품목명:</strong> 농산물의 이름을 입력하세요 (예: 상추, 양배추)</li>
              <li><strong>거래일자:</strong> YYYYMMDD 형식으로 입력하세요 (예: 20241201)</li>
              <li><strong>평균가:</strong> 필수 입력 항목입니다</li>
              <li><strong>최고가/최저가:</strong> 선택 입력 항목입니다</li>
            </ul>
          </div>
          
          <div class="help-item">
            <h4>🔄 자동 불러오기</h4>
            <ul>
              <li><strong>금일 데이터:</strong> 오늘 날짜의 경매 데이터를 실시간으로 불러옵니다</li>
              <li><strong>3년치 대량 데이터:</strong> 금일부터 3년 전까지의 모든 경매 데이터를 실시간으로 불러옵니다 (약 30분 소요)</li>
              <li><strong>실시간 진행률:</strong> 진행 상황을 실시간으로 확인할 수 있습니다</li>
              <li><strong>실시간 로그:</strong> 처리 과정을 실시간으로 확인할 수 있습니다</li>
            </ul>
          </div>
          
          <div class="help-item">
            <h4>⚠️ 주의사항</h4>
            <ul>
              <li>데이터 초기화는 되돌릴 수 없으므로 신중하게 사용하세요</li>
              <li>중복 데이터는 자동으로 필터링됩니다</li>
              <li>네트워크 상태에 따라 불러오기 시간이 달라질 수 있습니다</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import Layout from '../components/Layout.vue'
import { useAuctionInput } from '../composables/useAuctionInput'
import { useOptimizedAuction } from '../composables/useOptimizedAuction'

const layoutRef = ref(null)

// Composables
const {
  loading,
  error,
  success,
  formData,
  bulkData,
  bulkLoading,
  bulkProgress,
  progress,
  logs,
  status,
  isLoading,
  eventSource,
  isFormValid,
  formattedDate,
  submitAuctionData,
  loadTodayData,
  loadBulkData,
  initializeProducts,
  truncateTable,
  resetForm,
  clearError,
  clearSuccess
} = useAuctionInput()

// 최적화된 API
const {
  checkServerStatus,
  getPerformanceStats
} = useOptimizedAuction()

// 초기화
onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/auction-input')
  }
  
  // 오늘 날짜를 기본값으로 설정
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  formData.value.ADJ_DT = `${year}${month}${day}`
})

// 컴포넌트 언마운트 시 EventSource 정리
onBeforeUnmount(() => {
  if (eventSource.value) {
    eventSource.value.close()
  }
})


</script>

<style scoped>
.auction-input-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
}

.main-title h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0 0 15px 0;
}

.header-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

/* 알림 메시지 */
.alert {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.alert:hover {
  transform: translateY(-2px);
}

.alert-error {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert-success {
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
}

.alert-icon {
  font-size: 1.5rem;
}

.alert-content {
  flex: 1;
}

.alert-content h4 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
}

.alert-content p {
  margin: 0;
  font-size: 0.9rem;
}

.alert-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.alert-close:hover {
  opacity: 1;
}

/* 메인 컨텐츠 */
.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

@media (max-width: 768px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}

/* 섹션 공통 스타일 */
.input-section,
.auto-load-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  margin-bottom: 25px;
}

.section-header h2 {
  font-size: 1.5rem;
  color: #2c3e50;
  margin: 0 0 8px 0;
}

.section-header p {
  color: #7f8c8d;
  margin: 0;
  font-size: 0.9rem;
}

/* 입력 폼 */
.input-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 500;
}

.form-group input {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  transition: all 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-group small {
  font-size: 0.8rem;
  color: #7f8c8d;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-start;
}

/* 버튼 스타일 */
.btn {
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #2980b9;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #7f8c8d;
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #229954;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover:not(:disabled) {
  background: #e67e22;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover:not(:disabled) {
  background: #138496;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c0392b;
}

.btn-sm {
  padding: 8px 12px;
  font-size: 0.8rem;
  min-width: 80px;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 자동 불러오기 옵션 */
.auto-load-options {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.load-option {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  background: #f8f9fa;
}

.option-header h3 {
  font-size: 1.1rem;
  color: #2c3e50;
  margin: 0 0 5px 0;
}

.option-header p {
  color: #7f8c8d;
  margin: 0 0 15px 0;
  font-size: 0.9rem;
}

.option-actions {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.admin-only {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.admin-only .option-header h3 {
  color: #e74c3c;
}



.status {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  font-weight: bold;
}

.status.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* SSE 스트리밍 섹션 */
.streaming-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.streaming-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.streaming-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.3rem;
}

.progress-container {
  margin-top: 20px;
}

.progress-bar {
  width: 100%;
  height: 20px;
  background-color: #e9ecef;
  border-radius: 10px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: width 0.3s ease;
}

.streaming-logs h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  font-family: monospace;
  font-size: 12px;
}

.log-item {
  margin: 2px 0;
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.log-item:last-child {
  border-bottom: none;
}



/* 도움말 섹션 */
.help-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.help-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.help-item h4 {
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-size: 1.1rem;
}

.help-item ul {
  margin: 0;
  padding-left: 20px;
  color: #7f8c8d;
}

.help-item li {
  margin-bottom: 5px;
  font-size: 0.9rem;
  line-height: 1.4;
}

.help-item strong {
  color: #2c3e50;
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .option-actions {
    flex-direction: column;
  }
  
  .help-content {
    grid-template-columns: 1fr;
  }
}

/* 성능 모니터링 섹션 */
.performance-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.performance-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.performance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.performance-card .card-icon {
  font-size: 1.5rem;
}

.performance-card .card-content {
  flex: 1;
}

.performance-card .card-title {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-bottom: 3px;
}

.performance-card .card-value {
  font-size: 1.1rem;
  font-weight: bold;
}
</style> 