<template>
  <Layout ref="layoutRef">
    <div class="auction-container">
      <!-- 페이지 헤더 -->
      <div class="main-title">
        <h1>📊 오늘의 경매 정보</h1>
        <p class="subtitle">가락시장에서 거래되는 농산물 가격을 확인하세요</p>
      </div>

      <!-- 상품 버튼 선택 -->
      <ProductButtons
        :major-products="majorProducts"
        :products-loading="productsLoading"
        :selected-product="searchForm.pumName"
        @select-product="handleProductSelect"
        @clear-selection="handleClearSelection"
        @order-changed="handleOrderChanged"
      />

      <!-- 기간 검색 -->
      <div class="date-search-section">
        <div class="search-form">
          <div class="form-group">
            <label>시작일</label>
            <input 
              v-model="searchForm.startDate" 
              type="date" 
              class="date-input"
              placeholder="YYYY-MM-DD"
            />
          </div>
          <div class="form-group">
            <label>종료일</label>
            <input 
              v-model="searchForm.endDate" 
              type="date" 
              class="date-input"
              placeholder="YYYY-MM-DD"
              @change="handleEndDateChange"
            />
          </div>
          <button @click="resetDateSearch" class="reset-btn">🔄 초기화</button>
        </div>
      </div>

      <!-- 성능 모니터링 섹션 -->
      <div v-if="performanceMonitor && performanceMonitor.performanceHistory.value.length > 0" class="performance-section">
        <div class="performance-header">
          <h3>🚀 성능 모니터링</h3>
          <button @click="checkServerStatus" class="btn btn-sm btn-info">서버 상태 확인</button>
        </div>
        <div class="performance-cards">
          <div class="performance-card">
            <div class="card-icon">⚡</div>
            <div class="card-content">
              <div class="card-title">응답 시간</div>
              <div class="card-value">{{ getPerformanceStats?.averageResponseTime || 'N/A' }}</div>
            </div>
          </div>
          <div class="performance-card">
            <div class="card-icon">📦</div>
            <div class="card-content">
              <div class="card-title">데이터 크기</div>
              <div class="card-value">{{ getPerformanceStats?.dataSizeKB || 'N/A' }}</div>
            </div>
          </div>
          <div class="performance-card">
            <div class="card-icon">📊</div>
            <div class="card-content">
              <div class="card-title">조회 건수</div>
              <div class="card-value">{{ getPerformanceStats?.dataCount || 0 }}건</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 요약 카드 -->
      <div class="summary-cards">
        <div class="summary-card">
          <div class="card-icon">📊</div>
          <div class="card-content">
            <div class="card-title">총 거래 건수</div>
            <div class="card-value">{{ totalCount }}</div>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">💰</div>
          <div class="card-content">
            <div class="card-title">평균 가격</div>
            <div class="card-value">{{ averagePrice }}</div>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">🏆</div>
          <div class="card-content">
            <div class="card-title">최고 가격</div>
            <div class="card-value">{{ maxPrice }}</div>
          </div>
        </div>
      </div>

      <!-- 성공 메시지 표시 -->
      <div v-if="success" class="success-message">
        <div class="success-icon">✅</div>
        <p>{{ success }}</p>
      </div>

      <!-- 페이지 에러 표시 -->
      <div v-if="pageError" class="page-error-container">
        <div class="error-card">
          <div class="error-icon">⚠️</div>
          <div class="error-content">
            <h3>페이지 로드 중 문제가 발생했습니다</h3>
            <p>{{ pageError }}</p>
            <button @click="retryPageLoad" class="retry-btn">🔄 다시 시도</button>
          </div>
        </div>
      </div>

      <!-- 데이터 표시 -->
      <div v-else-if="loading" class="loading-container">
        <div class="loading-spinner">🔄</div>
        <p>경매 데이터를 불러오는 중...</p>
      </div>

      <div v-else-if="error" class="error-message">
        <div class="error-icon">⚠️</div>
        <p>{{ error }}</p>
      </div>

      <div v-else-if="!Array.isArray(auctionData) || auctionData.length === 0" class="no-data">
        <div class="no-data-icon">📭</div>
        <h3>데이터가 없습니다</h3>
        <p>검색 조건을 변경해보세요</p>
        

      </div>

      <div v-else class="auction-scroll-container" ref="scrollContainer" @scroll="handleScroll">
        <div class="auction-grid">
          <div 
            v-for="(item, index) in auctionData" 
            :key="index" 
            class="auction-card"
            :class="getCardColorClass(item)"
          >
            <div class="card-header">
              <div class="product-name">{{ item.PUM_NM || '-' }}</div>
              <div class="date-badge">{{ formatDate(item.ADJ_DT) }}</div>
            </div>
            <div class="card-body">
              <div class="price-section">
                <div class="price" :style="{ color: getItemColor(item) }">
                  {{ formatPrice(item.AVG_PRC) || '가격 정보 없음' }}
                </div>
                <div class="price-details">
                  <span class="max-price">최고: {{ formatPrice(item.MAX_PRC) || '-' }}</span>
                  <span class="min-price">최저: {{ formatPrice(item.MIN_PRC) || '-' }}</span>
                </div>
              </div>
              <div class="sale-section">
                <div class="sale-info">
                  <span class="sale-qty">수량: {{ item.SALE_QTY || '-' }}</span>
                  <span class="sale-amt">금액: {{ formatPrice(item.SALE_AMT) || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 로딩 인디케이터 -->
          <div v-if="isLoadingMore" class="loading-more-indicator">
            <div class="loading-spinner">🔄</div>
            <span>더 많은 데이터를 불러오는 중...</span>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, nextTick } from 'vue'
import Layout from '../components/Layout.vue'
import ProductButtons from '../components/ProductButtons.vue'
import { useOptimizedAuction } from '../composables/useOptimizedAuction'
import { useProductManagement } from '../composables/useProductManagement'
import { formatPrice, formatDate, getCardColorClass, getItemColor } from '../utils/auctionUtils'

const layoutRef = ref(null)

// Composables
const { 
  auctionData, 
  loading, 
  error, 
  success,
  totalCount, 
  currentPage, 
  hasMore, 
  getPerformanceStats,
  fetchAuctionDataOptimized, 
  searchAuction, 
  loadMore, 
  resetState,
  checkServerStatus,
  performanceMonitor
} = useOptimizedAuction()

// 성능 메트릭 (performanceMonitor에서 가져오기)
const performanceMetrics = computed(() => performanceMonitor.performanceMetrics.value)
const { majorProducts, productsLoading, fetchProductList, saveProductOrder } = useProductManagement()

// 검색 폼
const searchForm = ref({ 
  pumName: '',
  startDate: '',
  endDate: ''
})

// 에러 상태
const pageError = ref('')

// 무한 스크롤 관련
const isLoadingMore = ref(false)
const scrollContainer = ref(null)

// 계산된 속성
const averagePrice = computed(() => {
  if (!Array.isArray(auctionData.value) || auctionData.value.length === 0) return '-'
  const total = auctionData.value.reduce((sum, item) => sum + parseInt(item.AVG_PRC || 0), 0)
  return formatPrice(Math.round(total / auctionData.value.length))
})

const maxPrice = computed(() => {
  if (!Array.isArray(auctionData.value) || auctionData.value.length === 0) return '-'
  const prices = auctionData.value.map(item => parseInt(item.MAX_PRC || 0))
  return formatPrice(Math.max(...prices))
})



// 이벤트 핸들러
const handleProductSelect = async (productName) => {
  searchForm.value.pumName = productName
  searchAuction({ pumName: productName })
  console.log('🔄 상품 선택 완료')
}

const handleClearSelection = () => {
  searchForm.value.pumName = ''
  resetState()
}

const handleOrderChanged = ({ oldIndex, newIndex }) => {
  const movedProduct = majorProducts.value.splice(oldIndex, 1)[0]
  majorProducts.value.splice(newIndex, 0, movedProduct)
  saveProductOrder()
  
  if (majorProducts.value.length > 0) {
    searchForm.value.pumName = majorProducts.value[0]
    searchAuction({ pumName: majorProducts.value[0] })
  }
}

// 종료일 변경 시 자동 검색
const handleEndDateChange = async () => {
  const searchParams = {
    pumName: searchForm.value.pumName
  }
  
  // 시작일이 있으면 추가
  if (searchForm.value.startDate) {
    searchParams.startDate = searchForm.value.startDate.replace(/-/g, '')
  }
  
  // 종료일이 있으면 추가
  if (searchForm.value.endDate) {
    searchParams.endDate = searchForm.value.endDate.replace(/-/g, '')
  }
  
  // 검색 조건 검증
  if (!searchParams.pumName && !searchParams.startDate && !searchParams.endDate) {
    return
  }
  
  console.log('종료일 변경 자동 검색 파라미터:', searchParams)
  searchAuction(searchParams)
  console.log('🔄 종료일 변경 자동 검색 완료')
}

// 날짜 검색 초기화
const resetDateSearch = () => {
  searchForm.value.startDate = ''
  searchForm.value.endDate = ''
  searchAuction({ pumName: searchForm.value.pumName })
}

// 페이지 다시 로드
const retryPageLoad = async () => {
  pageError.value = ''
  try {
    await fetchProductList()
    
    if (majorProducts.value.length > 0) {
      searchForm.value.pumName = majorProducts.value[0]
      await searchAuction({ pumName: majorProducts.value[0] })
    }
  } catch (err) {
    pageError.value = err.message
  }
}

// 테스트 함수들
const testSearch = () => {
  console.log('🧪 테스트: 양배추 검색 시작')
  searchAuction({ pumName: '양배추' })
}

const testSearchEmpty = () => {
  console.log('🧪 테스트: 빈 검색 시작')
  searchAuction({})
}

  // 스크롤 이벤트 핸들러
  const handleScroll = () => {
    if (!scrollContainer.value || isLoadingMore.value || !hasMore.value) {
      return
    }
    
    const container = scrollContainer.value
    const scrollTop = container.scrollTop
    const scrollHeight = container.scrollHeight
    const clientHeight = container.clientHeight
    
    // 스크롤이 끝에 가까워지면 (100px 남았을 때) 더 보기
    if (scrollTop + clientHeight >= scrollHeight - 100) {
      console.log('🔄 스크롤 끝 감지:', {
        scrollTop,
        scrollHeight,
        clientHeight,
        currentPage: currentPage.value,
        hasMore: hasMore.value
      })
      loadMoreData()
    }
  }

  // 더 보기 함수
  const loadMoreData = () => {
    if (isLoadingMore.value || !hasMore.value) {
      console.log('🚫 더 보기 차단:', {
        isLoadingMore: isLoadingMore.value,
        hasMore: hasMore.value
      })
      return
    }
    
    console.log('📥 더 보기 시작:', {
      currentPage: currentPage.value,
      hasMore: hasMore.value
    })
    
    isLoadingMore.value = true
    
    // 데이터 로드
    loadMore()
    
    console.log('✅ 더 보기 완료:', {
      newCurrentPage: currentPage.value,
      hasMore: hasMore.value,
      totalDisplayed: auctionData.length
    })
    
    // 로딩 상태 해제
    setTimeout(() => {
      isLoadingMore.value = false
    }, 500)
  }

  // 초기화
  onMounted(async () => {
    if (layoutRef.value) {
      layoutRef.value.addToHistory('/auction')
    }
    
    try {
      await fetchProductList()
      console.log('상품 목록 로드 완료:', majorProducts.value.length, '개')
      
      // 서버 상태 확인
      const status = await checkServerStatus()
      if (status) {
        console.log('서버 상태:', status)
      }
      
      // 초기 데이터 로드
      if (majorProducts.value.length > 0) {
        searchForm.value.pumName = majorProducts.value[0]
        await searchAuction({ pumName: majorProducts.value[0] })
      } else {
        // 상품 목록이 없으면 전체 데이터 로드
        await fetchAuctionDataOptimized()
      }
      
      console.log('🎯 경매 페이지 초기화 완료')
      console.log('📊 현재 데이터 상태:', {
        auctionDataLength: auctionData.value.length,
        loading: loading.value,
        error: error.value,
        success: success.value
      })
    } catch (err) {
      console.error('경매 페이지 초기화 실패:', err)
      pageError.value = err.message
    }
  })
</script>

<style scoped>
.auction-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
}

.main-title h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.page-error-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.error-card {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 100%;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 10px;
}

.error-content {
  width: 100%;
  text-align: center;
}

.error-content h3 {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 1.3rem;
}

.error-content p {
  margin: 0 0 15px 0;
  color: #856404;
  font-size: 1rem;
  line-height: 1.5;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e67e22;
  transform: translateY(-2px);
}

.date-search-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  display: flex;
  align-items: flex-end;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-size: 0.9rem;
  color: #2c3e50;
  font-weight: 500;
}

.date-input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 0.9rem;
  background: white;
  min-width: 140px;
  height: 42px;
  box-sizing: border-box;
}

.date-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.search-btn, .reset-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
  height: 42px;
  box-sizing: border-box;
}

.search-btn {
  background: #3498db;
  color: white;
}

.search-btn:hover {
  background: #2980b9;
}

.reset-btn {
  background: #95a5a6;
  color: white;
}

.reset-btn:hover {
  background: #7f8c8d;
}

/* 성능 모니터링 섹션 */
.performance-section {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.performance-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.performance-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.2rem;
}

.performance-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.performance-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
  color: white;
}

.performance-card .card-icon {
  font-size: 1.5rem;
}

.performance-card .card-content {
  flex: 1;
}

.performance-card .card-title {
  font-size: 0.8rem;
  opacity: 0.9;
  margin-bottom: 3px;
}

.performance-card .card-value {
  font-size: 1.1rem;
  font-weight: bold;
}

/* 성공 메시지 */
.success-message {
  display: flex;
  align-items: center;
  gap: 10px;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  color: #155724;
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.success-icon {
  font-size: 1.2rem;
}

.success-message p {
  margin: 0;
  font-size: 0.9rem;
}

/* 버튼 스타일 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
}

.btn-info {
  background: #17a2b8;
  color: white;
}

.btn-info:hover {
  background: #138496;
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.card-icon { font-size: 2.5rem; }
.card-content { flex: 1; }
.card-title { font-size: 0.9rem; color: #7f8c8d; margin-bottom: 5px; }
.card-value { font-size: 1.8rem; font-weight: bold; color: #2c3e50; }

.loading-container, .error-message, .no-data {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  margin: 0 auto;
}

.no-data h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.no-data p {
  color: #7f8c8d;
  font-size: 1rem;
}

.loading-spinner {
  font-size: 3rem;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon, .no-data-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
  display: block;
}

.auction-scroll-container {
  height: 600px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  background: #f8f9fa;
}

.auction-scroll-container::-webkit-scrollbar {
  display: none;
}

.auction-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  padding: 20px;
}

.load-more-container {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
}

.load-more-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.load-complete {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.load-complete p {
  margin: 0;
  font-size: 1rem;
}

.loading-more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #7f8c8d;
  font-size: 0.9rem;
  padding: 20px;
  margin: 20px 0;
}

.loading-more-indicator .loading-spinner {
  font-size: 1.2rem;
  animation: spin 1s linear infinite;
}

.auction-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.auction-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.product-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.date-badge {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  color: #7f8c8d;
}

.price-section { margin-bottom: 15px; }
.price { font-size: 1.5rem; font-weight: bold; margin-bottom: 8px; }
.price-details {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.sale-section {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
}

.sale-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.high-price { border-left: 4px solid #d32f2f; }
.medium-price { border-left: 4px solid #f57c00; }
.low-price { border-left: 4px solid #388e3c; }
.very-low-price { border-left: 4px solid #757575; }

@media (max-width: 1200px) {
  .auction-grid { grid-template-columns: repeat(3, 1fr); }
}

@media (max-width: 900px) {
  .auction-grid { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
  .auction-grid { grid-template-columns: 1fr; }
  .summary-cards { grid-template-columns: 1fr; }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .form-group {
    width: 100%;
  }
  
  .date-input {
    width: 100%;
    height: 42px;
  }
  
  .search-btn, .reset-btn {
    width: 100%;
    height: 42px;
  }
}
</style> 