<template>
  <Layout ref="layoutRef">
    <div class="auction-container">
      <!-- 페이지 헤더 -->
      <div class="main-title">
        <h1>📊 경매 정보</h1>
        <p class="subtitle">주요 농산물의 일별 경매 가격을 확인하세요</p>
      </div>
      
      <!-- 상품 버튼 선택 -->
      <ProductButtons
        :major-products="majorProducts"
        :products-loading="productsLoading"
        :selected-product="selectedProduct"
        @select-product="handleProductSelect"
        @clear-selection="handleClearSelection"
        @order-changed="handleOrderChanged"
      />

      <!-- 날짜 선택 및 데이터 새로고침 -->
      <div class="date-controls">
        <div class="date-buttons">
            <button @click="fetchDataFor('yesterday')" :class="{ active: activeDateFilter === 'yesterday' }">어제</button>
            <button @click="fetchDataFor('today')" :class="{ active: activeDateFilter === 'today' }">오늘</button>
            <button @click="fetchDataFor('week')" :class="{ active: activeDateFilter === 'week' }">최근 일주일</button>
        </div>
        <div class="manual-fetch">
          <input type="date" v-model="selectedDateForCache" class="date-input" />
          <button @click="handleManualCache" class="manual-fetch-btn" :disabled="caching">
            <span v-if="caching" class="spinner">🔄</span>
            <span v-else>선택일 데이터 가져오기</span>
          </button>
        </div>
        <button @click="handleRefresh" class="refresh-btn" :disabled="caching">
            <span v-if="caching" class="spinner">🔄</span>
            <span v-else>🔄 오늘 데이터 새로고침</span>
        </button>
      </div>
      
      <!-- 데이터 현황 제목 -->
      <div class="data-header">
        <h2>{{ currentDataTitle }}</h2>
      </div>

      <!-- 진행 상태 알림 UI -->
      <div v-if="cacheInProgress" class="cache-status-overlay">
        <div class="cache-status-box">
          <div class="spinner-large"></div>
          <p>{{ cacheStatusMessage }}</p>
          <span>이 작업은 최대 5분까지 소요될 수 있습니다.</span>
        </div>
      </div>

      <!-- 요약 카드 -->
      <div class="summary-cards">
        <div class="summary-card">
          <div class="card-icon">📈</div>
          <div class="card-content">
            <span class="card-title">총 경매 건수</span>
            <span class="card-value">{{ totalCount }} 건</span>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">💰</div>
          <div class="card-content">
            <span class="card-title">평균가 (원/kg)</span>
            <span class="card-value">{{ averagePrice }}</span>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">🔺</div>
          <div class="card-content">
            <span class="card-title">최고가 (원/kg)</span>
            <span class="card-value">{{ maxPrice }}</span>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">🔻</div>
          <div class="card-content">
            <span class="card-title">최저가 (원/kg)</span>
            <span class="card-value">{{ minPrice }}</span>
          </div>
        </div>
      </div>

      <!-- 로딩, 에러, 데이터 없음 표시 -->
      <div v-if="loading" class="status-message">
        <div class="spinner-large"></div>
        <p>데이터를 불러오는 중입니다...</p>
      </div>
      <div v-else-if="error" class="status-message error-box">
        <p>🚫 {{ error }}</p>
        <button @click="initializePage" class="retry-btn">재시도</button>
      </div>
      <div v-else-if="!auctionData || auctionData.length === 0" class="status-message">
        <p>🤷‍♀️ 해당 조건의 경매 데이터가 없습니다.</p>
      </div>

      <!-- 실시간 로그 및 프로그레스바 UI -->
      <div v-if="cacheInProgress" class="log-panel">
        <div class="log-header">
          <h4>실시간 작업 로그</h4>
        </div>
        <div class="progress-bar-container">
          <div class="progress-bar" :style="{ width: progress + '%' }"></div>
        </div>
        <div class="log-content">
          <div v-for="(log, i) in logs" :key="i" :class="['log-item', log.type]">
            <span class="log-timestamp">{{ log.timestamp }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
      
      <!-- 데이터 그리드 -->
      <div v-else class="auction-scroll-container">
        <div class="auction-grid">
          <div v-for="(item, index) in auctionData" :key="index" class="auction-item">
              <div v-if="item" class="item-body">
                  <div class="detail-item unit-price">
                      <span class="label">가격 (원/kg)</span>
                      <span class="value primary-value">{{ formatPrice(calculateUnitPrice(item)) }}</span>
                  </div>
                  <div class="detail-item">
                      <span class="label">수량</span>
                      <span class="value">{{ item.saleQty }} kg</span>
                  </div>
                   <div class="detail-item">
                      <span class="label">산지</span>
                      <span class="value">{{ item.sanji }}</span>
                  </div>
                  <div class="detail-item">
                      <span class="label">등급</span>
                      <span class="value">{{ item.injungGubun }}</span>
                  </div>
              </div>
          </div>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import Layout from '../components/Layout.vue'
import ProductButtons from '../components/ProductButtons.vue'
import { useAuctionData } from '../composables/useAuctionData'
import { useProductManagement } from '../composables/useProductManagement'
import { formatPrice, formatDate, getCardColorClass, getItemColor } from '../utils/auctionUtils'

// 컴포넌트 내부 상태
const cacheInProgress = ref(false);
const progress = ref(0);
const logs = ref([]);
let progressInterval = null;
let logInterval = null;

const addLog = (message, type = 'info') => {
  logs.value.push({ timestamp: new Date().toLocaleTimeString(), message, type });
};

// kg당 단가 계산
const calculateUnitPrice = (item) => {
  if (!item) return 0; 

  const price = parseFloat(String(item.saleAmt).replace(/,/g, ''));
  const quantity = parseFloat(String(item.saleQty).replace(/,/g, ''));

  if (isNaN(price) || isNaN(quantity) || quantity === 0) {
    return 0;
  }
  return Math.round(price / quantity);
};

// 날짜를 YYYY-MM-DD 형식(API 요청용)으로 변환
const formatDateForApi = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 날짜를 YYYYMMDD 형식(데이터 비교용)으로 변환
const formatDateForCompare = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}${month}${day}`;
};

const layoutRef = ref(null)

// Composables
const { 
    auctionData, loading, caching, error, totalCount, 
    fetchAuctionData, cacheDataForDate, resetState 
} = useAuctionData()
const { 
    majorProducts, productsLoading, fetchProductList, saveProductOrder 
} = useProductManagement()

// UI 상태
const selectedProduct = ref('')
const selectedDateForCache = ref(new Date().toISOString().split('T')[0]); // YYYY-MM-DD 형식
const activeDateFilter = ref(''); // 'today', 'yesterday', 'week'
const displayedDate = ref(new Date());
const pageError = ref('')

// 계산된 속성
const currentDataTitle = computed(() => {
  if (loading.value) return "데이터를 불러오는 중...";
  const product = selectedProduct.value || "전체 품목";
  let dateText = '';

  if (activeDateFilter.value === 'today') dateText = '오늘';
  else if (activeDateFilter.value === 'yesterday') dateText = '어제';
  else if (activeDateFilter.value === 'week') dateText = '최근 일주일';
  else dateText = `${displayedDate.value.toLocaleDateString()} 기준`;

  return `🥬 ${product} - ${dateText} 경매 현황`;
});

const validPrices = computed(() => {
    if (!auctionData.value || auctionData.value.length === 0) return [];
    return auctionData.value.map(item => calculateUnitPrice(item)).filter(price => price > 0);
});

const averagePrice = computed(() => {
  if (validPrices.value.length === 0) return '-';
  const total = validPrices.value.reduce((sum, price) => sum + price, 0);
  return formatPrice(Math.round(total / validPrices.value.length));
});

const maxPrice = computed(() => {
  if (validPrices.value.length === 0) return '-';
  return formatPrice(Math.round(Math.max(...validPrices.value)));
});

const minPrice = computed(() => {
    if (validPrices.value.length === 0) return '-';
    return formatPrice(Math.round(Math.min(...validPrices.value)));
});


// 데이터 조회 로직 (검증 로직 복원)
const fetchDataFor = async (filter) => {
    activeDateFilter.value = filter;
    let params = { productName: selectedProduct.value };

    const today = new Date();
    const yesterday = new Date();
    yesterday.setDate(today.getDate() - 1);

    let expectedDateToCompare = null;

    if (filter === 'today') {
        params.date = formatDateForApi(today);
        displayedDate.value = today;
        expectedDateToCompare = formatDateForCompare(today);
    } else if (filter === 'yesterday') {
        params.date = formatDateForApi(yesterday);
        displayedDate.value = yesterday;
        expectedDateToCompare = formatDateForCompare(yesterday);
    } else if (filter === 'week') {
        const weekAgo = new Date();
        weekAgo.setDate(today.getDate() - 6);
        params.startDate = formatDateForApi(weekAgo);
        params.endDate = formatDateForApi(today);
    }
    
    await fetchAuctionData(params);

    // [수정] 백엔드가 다른 날짜 데이터를 보내주는 현상에 대한 방어 로직 (camelCase 사용)
    if (expectedDateToCompare && auctionData.value.length > 0) {
        auctionData.value = auctionData.value.filter(item => item.adjDt === expectedDateToCompare);
    }
};

// 이벤트 핸들러
const handleProductSelect = (productName) => {
  selectedProduct.value = productName;
  // 현재 활성화된 날짜 필터 기준으로 다시 조회
  if(activeDateFilter.value) {
      fetchDataFor(activeDateFilter.value);
  }
}

const handleClearSelection = () => {
  selectedProduct.value = '';
  resetState();
}

const handleOrderChanged = ({ oldIndex, newIndex }) => {
  const movedProduct = majorProducts.value.splice(oldIndex, 1)[0];
  majorProducts.value.splice(newIndex, 0, movedProduct);
  saveProductOrder();
  
  if (majorProducts.value.length > 0) {
    handleProductSelect(majorProducts.value[0]);
  }
}

const handleRefresh = async () => {
    const today = new Date();
    await performCache(today);
}

const handleManualCache = async () => {
  if (!selectedDateForCache.value) {
    alert('날짜를 선택해주세요.');
    return;
  }
  const dateToCache = new Date(selectedDateForCache.value);
  await performCache(dateToCache);
}

const performCache = async (date) => {
    logs.value = []; // 로그 초기화
    progress.value = 0;
    cacheInProgress.value = true;
    addLog(`[${date.toLocaleDateString()}] 데이터 가져오기 작업을 시작합니다.`, 'start');

    // 예상 시간에 기반한 프로그레스바 및 로그 시뮬레이션
    const totalDuration = 180000; // 3분
    const totalSteps = 10; // 10단계로 나누어 로그 표시
    const progressIntervalTime = 1000; // 1초
    const logIntervalTime = totalDuration / totalSteps;
    const progressIncrement = (progressIntervalTime / totalDuration) * 100;
    
    let currentStep = 1;
    addLog(`서버에서 데이터를 처리하고 있습니다... (예상 소요 시간: 약 3분)`, 'info');

    progressInterval = setInterval(() => {
      progress.value = Math.min(progress.value + progressIncrement, 99);
    }, progressIntervalTime);

    logInterval = setInterval(() => {
      if (currentStep <= totalSteps) {
        addLog(`데이터 처리 중... (${currentStep++}/${totalSteps})`, 'info');
      }
    }, logIntervalTime);

    const result = await cacheDataForDate(date);
    
    clearInterval(progressInterval);
    clearInterval(logInterval);
    progress.value = 100;

    if (result) {
        if (result.saved > 0) {
            addLog(`✅ 성공: 신규 데이터 ${result.saved}건을 포함하여 총 ${result.saved + result.duplicate}건을 처리했습니다.`, 'success');
            const apiDate = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            activeDateFilter.value = '';
            displayedDate.value = date;
            await fetchAuctionData({ productName: selectedProduct.value, date: apiDate });
        } else if (result.duplicate > 0) {
            addLog(`ℹ️ 정보: 이미 최신 데이터입니다. (중복 ${result.duplicate}건)`, 'info');
        } else {
            addLog(`🤷‍♀️ 정보: 해당 날짜의 경매 데이터가 집계되지 않았습니다.`, 'info');
        }
    } else {
        addLog(`❌ 실패: ${error.value?.message || '알 수 없는 오류'}`, 'error');
    }

    setTimeout(() => {
      cacheInProgress.value = false;
    }, 3000); // 3초 후 로그 패널 자동 닫기
}

// 초기화 로직
const initializePage = async () => {
    pageError.value = '';
    try {
        await fetchProductList();
        if (majorProducts.value.length > 0) {
            selectedProduct.value = majorProducts.value[0];
            
            // "똑똑한 기본 화면" 로직
            const now = new Date();
            if (now.getHours() < 20) {
                fetchDataFor('yesterday');
            } else {
                fetchDataFor('today');
            }
        }
    } catch(err) {
        pageError.value = "페이지 초기화에 실패했습니다.";
    }
}

onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/auction');
  }
  initializePage();
})
</script>

<style scoped>
.auction-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.main-title {
  text-align: center;
  margin-bottom: 30px;
}

.main-title h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.page-error-container {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.error-card {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 12px;
  padding: 25px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
  width: 100%;
}

.error-icon {
  font-size: 3rem;
  margin-bottom: 10px;
}

.error-content {
  width: 100%;
  text-align: center;
}

.error-content h3 {
  margin: 0 0 10px 0;
  color: #856404;
  font-size: 1.3rem;
}

.error-content p {
  margin: 0 0 15px 0;
  color: #856404;
  font-size: 1rem;
  line-height: 1.5;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.retry-btn:hover {
  background: #e67e22;
  transform: translateY(-2px);
}

.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: #fff;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.06);
  transition: transform 0.2s, box-shadow 0.2s;
}

.summary-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.1);
}

.card-icon {
  font-size: 2rem;
  padding: 10px;
  border-radius: 50%;
  background-color: #f0f4f8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-content {
  display: flex;
  flex-direction: column;
}

.card-title {
  color: #64748b;
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.card-value {
  color: #0f172a;
  font-size: 1.4rem;
  font-weight: 600;
}

.loading-container, .error-message, .no-data {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  margin: 0 auto;
}

.no-data h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 10px;
}

.no-data p {
  color: #7f8c8d;
  font-size: 1rem;
}

.loading-spinner {
  font-size: 3rem;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.error-icon, .no-data-icon {
  font-size: 4rem;
  margin-bottom: 20px;
  opacity: 0.5;
  display: block;
}

.auction-scroll-container {
  height: 600px;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  background: #f8f9fa;
}

.auction-scroll-container::-webkit-scrollbar { 
  display: none;  /* Safari and Chrome */
}

.auction-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.load-more-container {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
}

.load-more-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-btn:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.load-complete {
  text-align: center;
  margin: 30px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #6c757d;
}

.load-complete p {
  margin: 0;
  font-size: 1rem;
}

.loading-more-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  color: #7f8c8d;
  font-size: 0.9rem;
  padding: 20px;
  margin: 20px 0;
}

.loading-more-indicator .loading-spinner {
  font-size: 1.2rem;
  animation: spin 1s linear infinite;
}

.auction-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.auction-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.product-name {
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
}

.date-badge {
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  color: #7f8c8d;
}

.price-section { margin-bottom: 15px; }
.price { font-size: 1.5rem; font-weight: bold; margin-bottom: 8px; }
.price-details {
  display: flex;
  gap: 15px;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.sale-section {
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
}

.sale-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: #7f8c8d;
}

.high-price { border-left: 4px solid #d32f2f; }
.medium-price { border-left: 4px solid #f57c00; }
.low-price { border-left: 4px solid #388e3c; }
.very-low-price { border-left: 4px solid #757575; }

.date-controls {
  background: white;
  border-radius: 12px;
  padding: 15px 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.date-buttons {
  display: flex;
  gap: 10px;
}

.date-buttons button {
  padding: 10px 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #f8f9fa;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.date-buttons button:hover {
  background-color: #e9ecef;
  border-color: #ccc;
}

.date-buttons button.active {
  background-color: #27ae60;
  color: white;
  border-color: #27ae60;
  font-weight: bold;
}

.refresh-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  background-color: #3498db;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
}

.refresh-btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

.spinner {
    display: inline-block;
    animation: spin 1s linear infinite;
}

.data-header {
    text-align: center;
    margin: 20px 0;
}

.data-header h2 {
    font-size: 1.8rem;
    color: #2c3e50;
    font-weight: 600;
}

.cache-status-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.cache-status-box {
  background-color: white;
  padding: 40px;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.cache-status-box p {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 20px 0 10px;
}

.cache-status-box span {
  font-size: 0.9rem;
  color: #6c757d;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.status-message {
  text-align: center;
  padding: 50px 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  margin-top: 20px;
}

.status-message p {
  font-size: 1.2rem;
  color: #6c757d;
  margin: 0;
}

.spinner-large {
  width: 50px;
  height: 50px;
  border: 5px solid #e9ecef;
  border-top-color: #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.error-box p {
    color: #e74c3c;
}

.retry-btn {
    margin-top: 20px;
    padding: 10px 25px;
    font-size: 1rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

.auction-item {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    overflow: hidden;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    border-top: 4px solid #3498db;
}

.auction-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
}

.item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.item-body {
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: baseline;
    font-size: 0.95rem;
}

.label {
    color: #718096;
}

.value {
    font-weight: 500;
    color: #2d3748;
}

.unit-price {
    margin-top: 10px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
}

.primary-value {
    font-size: 1.6rem;
    font-weight: 700;
    color: #e74c3c;
}

.manual-fetch {
  display: flex;
  align-items: center;
  gap: 10px;
}

.date-input {
  padding: 8px 12px;
  border-radius: 8px;
  border: 1px solid #ddd;
  font-size: 1rem;
}

.manual-fetch-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  background-color: #f39c12;
  color: white;
  font-size: 0.9rem;
  cursor: pointer;
}

.manual-fetch-btn:disabled {
    background-color: #bdc3c7;
    cursor: not-allowed;
}

.log-panel {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  background-color: #2c3e50;
  color: #ecf0f1;
  border-radius: 8px;
  box-shadow: 0 10px 20px rgba(0,0,0,0.2);
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.log-header {
  padding: 15px;
  background-color: #34495e;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}

.log-header h4 {
  margin: 0;
  font-size: 1.1rem;
}

.progress-bar-container {
  height: 8px;
  background-color: #4a6278;
}

.progress-bar {
  height: 100%;
  background-color: #27ae60;
  transition: width 0.5s ease-in-out;
}

.log-content {
  padding: 15px;
  max-height: 200px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.log-timestamp {
  color: #95a5a6;
  margin-right: 10px;
}

.log-item.success .log-message { color: #2ecc71; }
.log-item.error .log-message { color: #e74c3c; }
.log-item.info .log-message { color: #3498db; }
.log-item.start .log-message { font-weight: bold; }

@media (max-width: 1200px) {
  .auction-grid { grid-template-columns: repeat(3, 1fr); }
}

@media (max-width: 900px) {
  .auction-grid { grid-template-columns: repeat(2, 1fr); }
}

@media (max-width: 768px) {
  .auction-grid { grid-template-columns: 1fr; }
  .summary-cards { grid-template-columns: 1fr; }
  
  .search-form {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .form-group {
    width: 100%;
  }
  
  .date-input {
    width: 100%;
    height: 42px;
  }
  
  .search-btn, .reset-btn {
    width: 100%;
    height: 42px;
  }
}
</style> 