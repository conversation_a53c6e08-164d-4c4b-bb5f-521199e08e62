<template>
  <Layout ref="layoutRef">
    <div class="calendar-container">
      <!-- 페이지 헤더 -->
      <div class="page-header">
        <h1>📅 농장 일정 관리</h1>
        <p class="page-subtitle">재배, 수확, 출하 일정을 한눈에 관리합니다</p>
      </div>

      <!-- 캘린더 컨트롤 -->
      <div class="calendar-controls">
        <div class="control-buttons">
          <button @click="previousMonth" class="control-btn">◀</button>
          <button @click="goToToday" class="control-btn">오늘</button>
          <button @click="nextMonth" class="control-btn">▶</button>
        </div>
        <div class="current-month">{{ currentMonthText }}</div>
        <div class="view-buttons">
          <button @click="currentView = 'month'" :class="['view-btn', { active: currentView === 'month' }]">월</button>
          <button @click="currentView = 'week'" :class="['view-btn', { active: currentView === 'week' }]">주</button>
        </div>
      </div>

      <!-- 필터 및 검색 -->
      <div class="calendar-filters">
        <button @click="showAddEventModal = true" class="add-event-btn">
          ➕ 새 일정
        </button>
      </div>

      <!-- 로딩 상태 -->
      <div v-if="loading" class="loading-container">
        <div class="loading-spinner">🔄</div>
        <p>일정을 불러오는 중...</p>
      </div>

      <!-- 에러 상태 -->
      <div v-else-if="error" class="error-container">
        <div class="error-icon">⚠️</div>
        <p class="error-text">{{ error }}</p>
        <button @click="loadEvents" class="retry-btn">다시 시도</button>
      </div>

      <!-- 캘린더 뷰 -->
      <div v-else class="calendar-view">
        <!-- 월간 뷰 -->
        <div v-if="currentView === 'month'" class="month-view">
          <div class="calendar-header">
            <div v-for="day in weekDays" :key="day" class="weekday-header">
              {{ day }}
            </div>
          </div>
          <div class="calendar-grid">
            <div 
              v-for="date in calendarDates" 
              :key="date.key"
              class="calendar-day"
              :class="{ 
                'other-month': !date.isCurrentMonth,
                'today': date.isToday,
                'has-events': date.events.length > 0
              }"
              @click="selectDate(date)"
            >
              <div class="day-number">{{ date.day }}</div>
              <div class="day-events">
                <div 
                  v-for="event in date.events.slice(0, 3)" 
                  :key="event.id"
                  class="day-event"
                  :style="{ backgroundColor: getEventColor(event.type) }"
                  @click.stop="selectEvent(event)"
                >
                  {{ event.title }}
                </div>
                <div v-if="date.events.length > 3" class="more-events" @click.stop="showAllEvents(date)">
                  +{{ date.events.length - 3 }}개 더
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 주간 뷰 -->
        <div v-else-if="currentView === 'week'" class="week-view">
          <div class="week-header">
            <div class="time-column">시간</div>
            <div 
              v-for="day in weekDates" 
              :key="day.key" 
              class="week-day-header"
            >
              <div class="weekday-name">{{ weekDays[day.date.getDay()] }}</div>
              <div class="weekday-date">{{ day.date.getDate() }}</div>
            </div>
          </div>
          
          <div class="week-body">
            <div class="time-column-side">
              <div 
                v-for="hour in 24" 
                :key="hour" 
                class="time-slot"
                @click="selectTimeSlot(hour)"
                :title="`${hour.toString().padStart(2, '0')}:00에 일정 추가`"
              >
                {{ hour.toString().padStart(2, '0') }}:00
              </div>
            </div>
            
            <div class="week-days-container">
              <div 
                v-for="day in weekDates" 
                :key="day.key"
                class="week-day-column"
                :class="{ 'today-week': day.isToday }"
              >
                <div 
                  v-for="hour in 24" 
                  :key="hour"
                  class="day-time-slot"
                  @click="selectDayTimeSlot(day, hour)"
                  :title="`${formatDate(day.date)} ${hour.toString().padStart(2, '0')}:00에 일정 추가`"
                >
                  <div 
                    v-for="event in getEventsForDayAndTime(day, hour)" 
                    :key="event.id"
                    class="week-event"
                    :style="{ backgroundColor: getEventColor(event.type) }"
                    @click.stop="selectEvent(event)"
                  >
                    {{ event.title }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 일정 상세 모달 -->
    <div v-if="showEventModal" class="modal-overlay" @click="closeEventModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>일정 상세</h3>
          <button @click="closeEventModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedEvent" class="event-details">
            <div class="event-info">
              <h4>{{ selectedEvent.title }}</h4>
              <p><strong>날짜:</strong> {{ formatEventDate(selectedEvent.date) }}</p>
              <p v-if="selectedEvent.time"><strong>시간:</strong> {{ selectedEvent.time }}</p>
              <p><strong>타입:</strong> {{ getEventTypeText(selectedEvent.type) }}</p>
              <p v-if="selectedEvent.description"><strong>설명:</strong> {{ selectedEvent.description }}</p>
            </div>
            <div class="event-actions">
              <button @click="editEvent(selectedEvent)" class="edit-btn">✏️ 수정</button>
              <button @click="deleteEvent(selectedEvent)" class="delete-btn">🗑️ 삭제</button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 모든 일정 보기 모달 -->
    <div v-if="showAllEventsModal" class="modal-overlay" @click="closeAllEventsModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ selectedDate ? formatDate(selectedDate.date) + ' 일정' : '일정 목록' }}</h3>
          <button @click="closeAllEventsModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <div v-if="selectedDate && selectedDate.events.length > 0" class="all-events-list">
            <div 
              v-for="event in selectedDate.events" 
              :key="event.id"
              class="event-item"
              @click="selectEvent(event)"
            >
              <div class="event-color" :style="{ backgroundColor: getEventColor(event.type) }"></div>
              <div class="event-info">
                <h4>{{ event.title }}</h4>
                <p>{{ getEventTypeText(event.type) }}</p>
                <p v-if="event.description" class="event-description">{{ event.description }}</p>
              </div>
            </div>
          </div>
          <div v-else class="no-events">
            <p>이 날짜에는 일정이 없습니다.</p>
            <button @click="addEventForDate" class="add-event-btn">새 일정 추가</button>
          </div>
        </div>
      </div>
    </div>

    <!-- 일정 추가/수정 모달 -->
    <div v-if="showAddEventModal" class="modal-overlay" @click="closeAddEventModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ editingEvent ? '일정 수정' : '새 일정 추가' }}</h3>
          <button @click="closeAddEventModal" class="close-btn">✕</button>
        </div>
        <div class="modal-body">
          <form @submit.prevent="saveEvent">
            <div class="form-group">
              <label>제목 *</label>
              <input v-model="eventForm.title" type="text" required class="form-input">
            </div>
            
            <div class="form-group">
              <label>타입 *</label>
              <select v-model="eventForm.type" required class="form-select">
                <option value="">타입 선택</option>
                <option value="planting">재배 시작</option>
                <option value="harvest">수확 예정</option>
                <option value="shipment">출하</option>
                <option value="maintenance">정비</option>
              </select>
            </div>
            
            <div class="form-group">
              <label>날짜 *</label>
              <input v-model="eventForm.date" type="date" required class="form-input">
            </div>
            
            <div class="form-group">
              <label>시간</label>
              <input v-model="eventForm.time" type="time" class="form-input">
            </div>
            
            <div class="form-group">
              <label>설명</label>
              <textarea v-model="eventForm.description" class="form-textarea" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
              <button type="button" @click="closeAddEventModal" class="cancel-btn">취소</button>
              <button type="submit" class="save-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>{{ editingEvent ? '수정' : '추가' }}</span>
              </button>
              <button v-if="!editingEvent" type="button" @click="saveAndContinue" class="continue-btn" :disabled="saving">
                <span v-if="saving" class="loading-spinner">🔄</span>
                <span v-else>추가 후 계속</span>
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import Layout from '../components/Layout.vue'

const layoutRef = ref(null)

// 상태 관리
const currentView = ref('month')
const currentDate = ref(new Date())
const loading = ref(false)
const error = ref('')
const saving = ref(false)

// 모달 상태
const showEventModal = ref(false)
const showAddEventModal = ref(false)
const showAllEventsModal = ref(false)
const selectedEvent = ref(null)
const selectedDate = ref(null)
const editingEvent = ref(null)

// 폼 데이터
const eventForm = ref({
  title: '',
  type: '',
  date: '',
  time: '',
  description: ''
})

const events = ref([])

const calendarApi = {
  async getEventsByMonth(year, month) {
    try {
      const response = await fetch(`/api/calendar/events/month?year=${year}&month=${month}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const result = await response.json()
      if (result.success) {
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('월별 일정 조회 실패:', error)
      if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
        return []
      }
      throw error
    }
  },
  
  async getEventsByWeek(weekStart) {
    try {
      const response = await fetch(`/api/calendar/events/week?weekStart=${weekStart}`)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const result = await response.json()
      if (result.success) {
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('주별 일정 조회 실패:', error)
      if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
        return []
      }
      throw error
    }
  },
  
  async saveEvent(eventData) {
    try {
      const response = await fetch('/api/calendar/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: eventData.title,
          type: eventData.type,
          eventDate: eventData.date,
          eventTime: eventData.time || null,
          description: eventData.description || ''
        })
      })
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const result = await response.json()
      if (result.success) {
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('일정 저장 실패:', error)
      if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
        return {
          id: Date.now(),
          title: eventData.title,
          type: eventData.type,
          eventDate: eventData.date,
          eventTime: eventData.time,
          description: eventData.description,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
      throw error
    }
  },
  
  async updateEvent(id, eventData) {
    try {
      const response = await fetch(`/api/calendar/events/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: eventData.title,
          type: eventData.type,
          eventDate: eventData.date,
          eventTime: eventData.time || null,
          description: eventData.description || ''
        })
      })
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const result = await response.json()
      if (result.success) {
        return result.data
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('일정 수정 실패:', error)
      if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
        return {
          id: id,
          title: eventData.title,
          type: eventData.type,
          eventDate: eventData.date,
          eventTime: eventData.time,
          description: eventData.description,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      }
      throw error
    }
  },
  
  async deleteEvent(id) {
    try {
      const response = await fetch(`/api/calendar/events/${id}`, {
        method: 'DELETE'
      })
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      const result = await response.json()
      if (result.success) {
        return true
      } else {
        throw new Error(result.message)
      }
    } catch (error) {
      console.error('일정 삭제 실패:', error)
      if (error.message.includes('Failed to fetch') || error.message.includes('HTTP 404')) {
        return true
      }
      throw error
    }
  }
}

const currentMonthText = computed(() => {
  return currentDate.value.toLocaleDateString('ko-KR', { 
    year: 'numeric', 
    month: 'long' 
  })
})

const weekDays = computed(() => ['일', '월', '화', '수', '목', '금', '토'])

const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  
  const dates = []
  const today = new Date()
  
  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    
    const dayEvents = events.value.filter(event => {
      const eventDate = new Date(event.eventDate || event.date)
      return eventDate.getFullYear() === date.getFullYear() &&
             eventDate.getMonth() === date.getMonth() &&
             eventDate.getDate() === date.getDate()
    })
    
    dates.push({
      key: date.toISOString(),
      date: date,
      day: date.getDate(),
      isCurrentMonth: date.getMonth() === month,
      isToday: date.getFullYear() === today.getFullYear() &&
               date.getMonth() === today.getMonth() &&
               date.getDate() === today.getDate(),
      events: dayEvents
    })
  }
  
  return dates
})

const weekDates = computed(() => {
  const today = new Date()
  const weekStart = new Date(currentDate.value)
  weekStart.setDate(currentDate.value.getDate() - currentDate.value.getDay())
  
  const weekDates = []
  for (let i = 0; i < 7; i++) {
    const date = new Date(weekStart)
    date.setDate(weekStart.getDate() + i)
    
    const dayEvents = events.value.filter(event => {
      const eventDate = new Date(event.eventDate || event.date)
      return eventDate.getFullYear() === date.getFullYear() &&
             eventDate.getMonth() === date.getMonth() &&
             eventDate.getDate() === date.getDate()
    })
    
    weekDates.push({
      key: date.toISOString(),
      date: date,
      day: date.getDate(),
      isToday: date.getFullYear() === today.getFullYear() &&
               date.getMonth() === today.getMonth() &&
               date.getDate() === today.getDate(),
      events: dayEvents
    })
  }
  
  return weekDates
})

const previousMonth = async () => {
  if (currentView.value === 'month') {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
  } else if (currentView.value === 'week') {
    currentDate.value = new Date(currentDate.value.getTime() - 7 * 24 * 60 * 60 * 1000)
  }
  await loadEvents()
}

const nextMonth = async () => {
  if (currentView.value === 'month') {
    currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
  } else if (currentView.value === 'week') {
    currentDate.value = new Date(currentDate.value.getTime() + 7 * 24 * 60 * 60 * 1000)
  }
  await loadEvents()
}

const goToToday = async () => {
  currentDate.value = new Date()
  await loadEvents()
}

const selectDate = (date) => {
  // 선택된 날짜를 설정하고 새 일정 추가 모달 열기
  const year = date.date.getFullYear()
  const month = String(date.date.getMonth() + 1).padStart(2, '0')
  const day = String(date.date.getDate()).padStart(2, '0')
  eventForm.value.date = `${year}-${month}-${day}`
  showAddEventModal.value = true
}

const selectEvent = (event) => {
  selectedEvent.value = event
  showEventModal.value = true
}

const closeEventModal = () => {
  showEventModal.value = false
  selectedEvent.value = null
}

const closeAddEventModal = () => {
  showAddEventModal.value = false
  editingEvent.value = null
  // 날짜와 시간은 유지하고 나머지만 초기화
  const currentDate = eventForm.value.date
  const currentTime = eventForm.value.time
  eventForm.value = {
    title: '',
    type: '',
    date: currentDate || new Date().toISOString().split('T')[0],
    time: currentTime || '',
    description: ''
  }
}

const showAllEvents = (date) => {
  selectedDate.value = date
  showAllEventsModal.value = true
}

const closeAllEventsModal = () => {
  showAllEventsModal.value = false
  selectedDate.value = null
}

const addEventForDate = () => {
  if (selectedDate.value) {
    const year = selectedDate.value.date.getFullYear()
    const month = String(selectedDate.value.date.getMonth() + 1).padStart(2, '0')
    const day = String(selectedDate.value.date.getDate()).padStart(2, '0')
    eventForm.value.date = `${year}-${month}-${day}`
  }
  closeAllEventsModal()
  showAddEventModal.value = true
}

const selectTimeSlot = (hour) => {
  // 현재 표시 중인 주의 첫 번째 날짜를 기준으로 설정
  const weekStart = new Date(currentDate.value)
  weekStart.setDate(currentDate.value.getDate() - currentDate.value.getDay())
  
  const year = weekStart.getFullYear()
  const month = String(weekStart.getMonth() + 1).padStart(2, '0')
  const day = String(weekStart.getDate()).padStart(2, '0')
  
  eventForm.value.date = `${year}-${month}-${day}`
  eventForm.value.time = `${hour.toString().padStart(2, '0')}:00`
  showAddEventModal.value = true
}



const selectDayTimeSlot = (day, hour) => {
  const year = day.date.getFullYear()
  const month = String(day.date.getMonth() + 1).padStart(2, '0')
  const dayNum = String(day.date.getDate()).padStart(2, '0')
  
  eventForm.value.date = `${year}-${month}-${dayNum}`
  eventForm.value.time = `${hour.toString().padStart(2, '0')}:00`
  showAddEventModal.value = true
}

const getEventsForDayAndTime = (day, hour) => {
  return events.value.filter(event => {
    const eventDate = new Date(event.eventDate || event.date)
    const isSameDay = eventDate.getFullYear() === day.date.getFullYear() &&
                     eventDate.getMonth() === day.date.getMonth() &&
                     eventDate.getDate() === day.date.getDate()
    
    if (!isSameDay) return false
    
    const eventTime = event.eventTime || event.time
    if (eventTime) {
      const eventHour = parseInt(eventTime.split(':')[0])
      return eventHour === hour
    }
    
    return false
  })
}

const editEvent = (event) => {
  editingEvent.value = event
  eventForm.value = {
    title: event.title,
    type: event.type,
    date: event.eventDate || event.date,
    time: event.eventTime || event.time || '',
    description: event.description || ''
  }
  showAddEventModal.value = true
  closeEventModal()
}

const deleteEvent = async (event) => {
  if (!confirm('이 일정을 삭제하시겠습니까?')) return
  
  try {
    await calendarApi.deleteEvent(event.id)
    events.value = events.value.filter(e => e.id !== event.id)
    alert('일정이 삭제되었습니다.')
    closeEventModal()
    await loadEvents() // 데이터 다시 로드
  } catch (error) {
    alert('일정 삭제에 실패했습니다: ' + error.message)
  }
}

const saveAndContinue = () => {
  saveEvent()
}

const saveEvent = async () => {
  saving.value = true
  
  try {
    if (editingEvent.value) {
      // 수정
      const updatedEvent = await calendarApi.updateEvent(editingEvent.value.id, eventForm.value)
      const index = events.value.findIndex(e => e.id === editingEvent.value.id)
      if (index !== -1) {
        events.value[index] = updatedEvent
      }
      alert('일정이 수정되었습니다.')
      closeAddEventModal()
      await loadEvents() // 데이터 다시 로드
    } else {
      // 추가
      const newEvent = await calendarApi.saveEvent(eventForm.value)
      events.value.push(newEvent)
      
      // 폼 초기화 (날짜와 시간은 유지)
      const currentDate = eventForm.value.date
      const currentTime = eventForm.value.time
      eventForm.value = {
        title: '',
        type: '',
        date: currentDate,
        time: currentTime,
        description: ''
      }
      
      alert('일정이 추가되었습니다!')
      closeAddEventModal() // 추가 후 모달 닫기
    }
  } catch (err) {
    console.error('일정 저장 실패:', err)
    alert('일정 저장에 실패했습니다: ' + err.message)
  } finally {
    saving.value = false
  }
}

const getEventColor = (type) => {
  const colors = {
    planting: '#27ae60',
    harvest: '#f39c12',
    shipment: '#3498db',
    maintenance: '#e74c3c'
  }
  return colors[type] || '#95a5a6'
}

const getEventTypeText = (type) => {
  const types = {
    planting: '재배',
    harvest: '수확',
    shipment: '출하',
    maintenance: '정비'
  }
  return types[type] || '기타'
}

const formatEventDate = (dateString) => {
  return new Date(dateString).toLocaleDateString('ko-KR')
}

const formatDate = (date) => {
  return date.toLocaleDateString('ko-KR')
}

const loadEvents = async () => {
  loading.value = true
  error.value = ''
  
  try {
    if (currentView.value === 'month') {
      const year = currentDate.value.getFullYear()
      const month = currentDate.value.getMonth() + 1
      const monthEvents = await calendarApi.getEventsByMonth(year, month)
      events.value = monthEvents
    } else if (currentView.value === 'week') {
      const weekStart = new Date(currentDate.value)
      weekStart.setDate(currentDate.value.getDate() - currentDate.value.getDay())
      const weekStartStr = weekStart.toISOString().split('T')[0]
      const weekEvents = await calendarApi.getEventsByWeek(weekStartStr)
      events.value = weekEvents
    }
  } catch (err) {
    console.error('일정 로드 실패:', err)
    error.value = '일정을 불러오는데 실패했습니다: ' + err.message
  } finally {
    loading.value = false
  }
}

watch(currentView, () => {
  loadEvents()
})

onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/calendar')
  }
  
  loadEvents()
})
</script>

<style scoped>
.calendar-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: bold;
}

.page-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 0;
}

.calendar-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background: white;
  padding: 15px 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-buttons {
  display: flex;
  gap: 10px;
}

.control-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.control-btn:hover {
  background: #e9ecef;
}

.current-month {
  font-size: 1.3rem;
  font-weight: bold;
  color: #2c3e50;
}

.view-buttons {
  display: flex;
  gap: 5px;
}

.view-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.view-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
}

.calendar-filters {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.add-event-btn {
  background: #27ae60;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.add-event-btn:hover {
  background: #229954;
}

.loading-container {
  text-align: center;
  padding: 40px;
}

.loading-spinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}



.error-container {
  text-align: center;
  padding: 25px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  margin: 20px 0;
}

.error-icon {
  font-size: 2rem;
  margin-bottom: 10px;
}

.error-text {
  color: #856404;
  margin-bottom: 15px;
}

.retry-btn {
  background: #f39c12;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.calendar-view {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 월간 뷰 */
.calendar-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 1px;
}

.weekday-header {
  background: #f8f9fa;
  padding: 15px;
  text-align: center;
  font-weight: bold;
  color: #2c3e50;
  border-bottom: 2px solid #dee2e6;
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
}

.calendar-day {
  min-height: 120px;
  padding: 10px;
  border: 1px solid #e9ecef;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
  color: #2c3e50;
  font-weight: 500;
}

.calendar-day:hover {
  background: #f8f9fa;
}

.calendar-day.other-month {
  background: #f8f9fa;
  color: #adb5bd;
  opacity: 0.6;
}

.calendar-day.today {
  background: #e3f2fd;
  border-color: #2196f3;
  font-weight: bold;
  color: #1976d2;
  box-shadow: 0 2px 4px rgba(33, 150, 243, 0.2);
}

.calendar-day.has-events {
  background: #f0f8ff;
  border-color: #3498db;
  box-shadow: 0 1px 3px rgba(52, 152, 219, 0.1);
}

.day-number {
  font-weight: bold;
  margin-bottom: 5px;
}

.day-events {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.day-event {
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 0.8rem;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.more-events {
  font-size: 0.7rem;
  color: #6c757d;
  text-align: center;
  padding: 2px;
  cursor: pointer;
  background: #f8f9fa;
  border-radius: 3px;
  transition: background-color 0.2s;
}

.more-events:hover {
  background: #e9ecef;
  color: #495057;
}

/* 주간 뷰 */
.week-header {
  display: flex;
  margin-bottom: 0;
}

.week-body {
  display: flex;
  min-height: 1440px;
}

.week-day-header {
  background: #f8f9fa;
  padding: 10px;
  text-align: center;
  font-weight: bold;
  color: #2c3e50;
  border-bottom: 2px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.week-day-header:last-child {
  border-right: none;
}

.weekday-name {
  font-size: 0.9rem;
  color: #6c757d;
}

.weekday-date {
  font-size: 1.1rem;
  color: #2c3e50;
}

.time-column {
  background: #f8f9fa;
  padding: 15px;
  text-align: center;
  font-weight: bold;
  color: #2c3e50;
  border-bottom: 2px solid #dee2e6;
  border-right: 1px solid #dee2e6;
  width: 80px;
  flex-shrink: 0;
}

.time-column-side {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #dee2e6;
  width: 80px;
  flex-shrink: 0;
}

.week-days-container {
  display: flex;
  flex: 1;
}

.time-slot {
  height: 60px;
  padding: 5px;
  border: 1px solid #e9ecef;
  border-bottom: 1px solid #dee2e6;
  background: #f8f9fa;
  font-size: 0.9rem;
  font-weight: 500;
  color: #2c3e50;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.time-slot:hover {
  background: #e3f2fd;
  color: #1976d2;
  border-color: #2196f3;
}

.week-day-column {
  display: flex;
  flex-direction: column;
  border-right: 1px solid #dee2e6;
  flex: 1;
}

.week-day-column:last-child {
  border-right: none;
}

.day-time-slot {
  height: 60px;
  border: 1px solid #e9ecef;
  border-bottom: 1px solid #dee2e6;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  padding: 2px;
}

.day-time-slot:hover {
  background: #f8f9fa;
  border-color: #3498db;
}

.today-week .day-time-slot {
  background: #e3f2fd;
  border-color: #2196f3;
}

.today-week .day-time-slot:hover {
  background: #bbdefb;
  border-color: #1976d2;
}

.week-event {
  margin: 1px;
  padding: 2px 4px;
  border-radius: 3px;
  color: white;
  font-size: 0.7rem;
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

/* 모달 스타일 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #7f8c8d;
}

.modal-body {
  padding: 25px;
}

.event-details {
  margin-bottom: 20px;
}

.event-info h4 {
  margin: 0 0 15px 0;
  color: #2c3e50;
}

.event-info p {
  margin: 8px 0;
  color: #2c3e50;
}

.event-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.edit-btn,
.delete-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.edit-btn {
  background: #3498db;
  color: white;
}

.delete-btn {
  background: #e74c3c;
  color: white;
}

.edit-btn:hover,
.delete-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* 모든 일정 목록 스타일 */
.all-events-list {
  max-height: 400px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.event-item:hover {
  background: #f8f9fa;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.event-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 15px;
  margin-top: 4px;
  flex-shrink: 0;
}

.event-info h4 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.1rem;
}

.event-info p {
  margin: 5px 0;
  color: #6c757d;
  font-size: 0.9rem;
}

.event-description {
  color: #495057 !important;
  font-size: 0.85rem !important;
  line-height: 1.4;
}

.no-events {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.no-events p {
  margin-bottom: 20px;
  font-size: 1.1rem;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: #2c3e50;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  box-sizing: border-box;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.cancel-btn,
.save-btn,
.continue-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.cancel-btn {
  background: #95a5a6;
  color: white;
}

.save-btn {
  background: #27ae60;
  color: white;
}

.continue-btn {
  background: #f39c12;
  color: white;
}

.save-btn:disabled,
.continue-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
}

.cancel-btn:hover,
.save-btn:hover:not(:disabled),
.continue-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

@media (max-width: 768px) {
  .calendar-controls {
    flex-direction: column;
    gap: 15px;
  }
  
  .week-header,
  .week-grid {
    grid-template-columns: 60px repeat(7, 1fr);
  }
  
  .time-column,
  .time-slot {
    width: 60px;
  }
  
  .modal-content {
    width: 95%;
    margin: 20px;
  }
}
</style> 