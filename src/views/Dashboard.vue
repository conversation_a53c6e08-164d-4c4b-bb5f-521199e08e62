<template>
  <Layout ref="layoutRef">
    <div class="dashboard-container">
      <!-- 카드 그리드 -->
            <div class="card-grid-header">
        <h2 class="section-title">📊 대시보드</h2>
        <div class="debug-buttons">
          <button @click="resetCardOrder" class="reset-order-btn" title="카드 순서 초기화">
            🔄 순서 초기화
          </button>
        </div>
      </div>
      
      <div class="card-grid" ref="cardGrid">
        <div
          v-for="(card, index) in cards"
          :key="card.name"
          class="card"
          :style="{ backgroundColor: card.color }"
          :class="{ 'not-implemented': !card.implemented }"
          :data-card-name="card.name"
        >
          <div class="card-content" @click="goTo(card.route)">
            <div class="card-icon">{{ card.icon }}</div>
            <div class="card-label">{{ card.label }}</div>
            <div class="card-desc">{{ card.description }}</div>
            <div v-if="!card.implemented" class="coming-soon">🚧 개발 중</div>
          </div>
        </div>
      </div>

      <!-- 최근 활동 -->
      <div class="recent-activity">
        <div class="section-header">
          <h2 class="section-title">📋 최근 활동</h2>
        </div>
        <div v-if="activitiesLoading" class="activity-loading">
          <div class="loading-spinner">⏳</div>
          <span>활동 로그 로딩 중...</span>
        </div>

        <div v-else class="activity-list">
          <div v-if="recentActivities.length === 0" class="no-activities">
            <div class="no-activity-icon">📋</div>
            <div class="no-activity-text">최근 활동이 없습니다</div>
            <button class="retry-activity-btn" @click="fetchRecentActivities">다시 시도</button>
          </div>
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item" @click="goToActivityLogs">
            <div class="activity-icon">{{ getActivityIcon(activity.type) }}</div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title || getActivityTitle(activity.type) }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
            <div class="activity-click-hint">클릭하여 상세보기</div>
          </div>
        </div>
      </div>


    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import Sortable from 'sortablejs'

const layoutRef = ref(null)
const router = useRouter()
const cardGrid = ref(null)
let sortable = null

// 카드 데이터
const cards = ref([
  { 
    name: 'crop', 
    label: '작물관리', 
    icon: '🌾', 
    color: '#27ae60', 
    route: '/crop',
    description: '작물 재배 및 관리',
    implemented: true
  },
  { 
    name: 'equipment', 
    label: '장비관리', 
    icon: '🛠️', 
    color: '#2980b9', 
    route: '/equipment',
    description: '농기계 및 장비 관리',
    implemented: false
  },
  { 
    name: 'greenhouse', 
    label: '비닐하우스', 
    icon: '🏠', 
    color: '#16a085', 
    route: '/greenhouse',
    description: '온실 환경 관리',
    implemented: true
  },
  { 
    name: 'shipment', 
    label: '출하관리', 
    icon: '📦', 
    color: '#d35400', 
    route: '/shipment',
    description: '작물 출하 및 판매',
    implemented: true
  },
  { 
    name: 'auction', 
    label: '경매조회', 
    icon: '📊', 
    color: '#e67e22', 
    route: '/auction',
    description: '도매시장 경매 정보',
    implemented: true
  },
  { 
    name: 'auction-input', 
    label: '경매데이터입력', 
    icon: '📝', 
    color: '#e74c3c', 
    route: '/auction-input',
    description: '경매 데이터 수동 입력',
    implemented: true
  },
  { 
    name: 'data-quality', 
    label: '데이터품질관리', 
    icon: '🔍', 
    color: '#9b59b6', 
    route: '/data-quality',
    description: '데이터 품질 모니터링 및 정리',
    implemented: true
  },
  { 
    name: 'performance-monitor', 
    label: '성능모니터링', 
    icon: '📊', 
    color: '#1abc9c', 
    route: '/performance-monitor',
    description: 'API 성능 및 시스템 리소스 모니터링',
    implemented: true
  },
  { 
    name: 'products', 
    label: '상품관리', 
    icon: '🌾', 
    color: '#f39c12', 
    route: '/products',
    description: '농산물 상품 관리',
    implemented: true
  },
  { 
    name: 'salary', 
    label: '급여관리', 
    icon: '💰', 
    color: '#8e44ad', 
    route: '/salary',
    description: '직원 급여 계산 및 관리',
    implemented: true
  },
  { 
    name: 'staff', 
    label: '직원관리', 
    icon: '👷', 
    color: '#7f8c8d', 
    route: '/staff',
    description: '직원 정보 및 관리',
    implemented: true
  },
  { 
    name: 'calendar', 
    label: '일정관리', 
    icon: '📅', 
    color: '#f39c12', 
    route: '/calendar',
    description: '재배, 수확, 출하 일정 관리',
    implemented: true
  },
  { 
    name: 'harvest-dashboard', 
    label: '수확관리', 
    icon: '🌾', 
    color: '#e74c3c', 
    route: '/harvest-dashboard',
    description: '스마트 수확 상태 관리',
    implemented: true
  },
  { 
    name: 'harvest-test', 
    label: '수확테스트', 
    icon: '🧪', 
    color: '#9b59b6', 
    route: '/harvest-test',
    description: '수확 관리 시스템 테스트',
    implemented: true
  },
  { 
    name: 'stats', 
    label: '통계', 
    icon: '📈', 
    color: '#2c3e50', 
    route: '/stats',
    description: '농장 운영 통계',
    implemented: true
  },
  { 
    name: 'activity-logs', 
    label: '활동 내역', 
    icon: '📊', 
    color: '#667eea', 
    route: '/activity-logs',
    description: '전체 활동 로그 조회 및 검색',
    implemented: true
  }
])

// 최근 활동 데이터
const recentActivities = ref([])
const activitiesLoading = ref(false)

// 이전 카드 순서 저장
let previousCardOrder = []

// 카드 순서 저장
const saveCardOrder = () => {
  try {
    const currentOrder = cards.value.map(card => card.name)
    const orderJson = JSON.stringify(currentOrder)
    
    // 저장 전 유효성 검사
    if (currentOrder.length === 0) {
      return
    }
    
    // 중복 제거
    const uniqueOrder = [...new Set(currentOrder)]
    if (uniqueOrder.length !== currentOrder.length) {
      currentOrder.splice(0, currentOrder.length, ...uniqueOrder)
    }
    
    // 저장
    localStorage.setItem('dashboardCardOrder', orderJson)
  } catch (error) {
    console.error('카드 순서 저장 중 오류:', error)
  }
}

const fetchRecentActivities = async () => {
  activitiesLoading.value = true
  
  try {
    const response = await fetch('http://localhost:8080/api/activity-logs/recent', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('최근 활동 로그 API 응답:', data)
      
      // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
      if (data.success && data.data && Array.isArray(data.data) && data.data.length > 0) {
        recentActivities.value = data.data.slice(0, 5).map(log => ({
          id: log.id,
          title: log.errorMessage || log.action || log.category || '활동',
          time: formatTimeAgo(log.createdAt),
          type: log.category || 'default'
        }))
        console.log('최근 활동 로그 로드 완료:', recentActivities.value.length, '개')
      } else {
        console.warn('최근 활동 로그 데이터 없음')
        recentActivities.value = []
      }
    } else {
      console.error('최근 활동 로그 API 호출 실패:', response.status)
      recentActivities.value = []
    }
  } catch (error) {
    console.error('최근 활동 로그 API 호출 오류:', error)
    recentActivities.value = []
  } finally {
    activitiesLoading.value = false
  }
}

// 시간 포맷팅 (상대적 시간)
const formatTimeAgo = (timestamp) => {
  if (!timestamp) return '시간 정보 없음'
  
  const now = new Date()
  const logTime = new Date(timestamp)
  const diffMs = now - logTime
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 1) return '방금 전'
  if (diffMinutes < 60) return `${diffMinutes}분 전`
  if (diffHours < 24) return `${diffHours}시간 전`
  if (diffDays < 7) return `${diffDays}일 전`
  
  return logTime.toLocaleDateString('ko-KR')
}



const getActivityIcon = (type) => {
  const iconMap = {
    'AUTH': '🔐',
    'CROP': '🌾',
    'PRODUCT': '📦',
    'HARVEST': '🌾',
    'ERROR': '⚠️',
    'harvest': '🌾',
    'planting': '🌱',
    'shipment': '📦',
    'equipment': '🛠️',
    'salary': '💰',
    'auction': '📊',
    'weather': '🌤️',
    'alert': '⚠️',
    'maintenance': '🔧',
    'default': '📋'
  }
  return iconMap[type] || iconMap.default
}

const getActivityTitle = (type) => {
  const titleMap = {
    'AUTH': '인증 활동',
    'CROP': '작물 관리',
    'PRODUCT': '상품 관리',
    'HARVEST': '수확 활동',
    'ERROR': '오류 메시지',
    'WARN': '경고 메시지',
    'INFO': '정보 메시지',
    'harvest': '수확 활동',
    'planting': '재배 활동',
    'shipment': '출하 활동',
    'equipment': '장비 관리',
    'salary': '급여 관리',
    'auction': '경매 활동',
    'weather': '날씨 정보',
    'alert': '알림 확인',
    'maintenance': '정비 활동',
    'default': '시스템 활동'
  }
  return titleMap[type] || titleMap.default
}

const loadCardOrder = () => {
  try {
    const savedOrder = localStorage.getItem('dashboardCardOrder')
    
    if (savedOrder) {
      const order = JSON.parse(savedOrder)

      // 저장된 순서와 현재 카드 목록 비교
      const currentCardNames = cards.value.map(c => c.name)
      const validOrder = order.filter(name => currentCardNames.includes(name))
      const missingCards = currentCardNames.filter(name => !order.includes(name))

      // 저장된 순서대로 카드 재정렬
      const orderedCards = []
      
      // 1. 저장된 순서대로 기존 카드들 추가
      validOrder.forEach(name => {
        const card = cards.value.find(c => c.name === name)
        if (card) {
          orderedCards.push(card)
        }
      })
      
      // 2. 새로운 카드들 추가 (저장되지 않은 카드들)
      missingCards.forEach(name => {
        const card = cards.value.find(c => c.name === name)
        if (card) {
          orderedCards.push(card)
        }
      })
      
      // 3. 순서가 실제로 변경되었는지 확인
      const originalOrder = cards.value.map(c => c.name)
      const newOrder = orderedCards.map(c => c.name)
      const hasChanged = JSON.stringify(originalOrder) !== JSON.stringify(newOrder)
      
      if (hasChanged) {
        cards.value = orderedCards
      }
      
      previousCardOrder = cards.value.map(card => card.name)
    } else {
      previousCardOrder = cards.value.map(card => card.name)
    }
  } catch (error) {
    console.error('카드 순서 로드 실패:', error)
    previousCardOrder = cards.value.map(card => card.name)
  }
}

const goTo = (route) => {
  router.push(route)
}

const goToActivityLogs = () => {
  router.push('/activity-logs')
}

// 카드 순서 초기화
const resetCardOrder = () => {
  localStorage.removeItem('dashboardCardOrder')
  location.reload() // 페이지 새로고침으로 기본 순서 복원
}





const initSortable = () => {
  if (cardGrid.value) {
    // 기존 Sortable 인스턴스 제거
    if (sortable) {
      sortable.destroy()
    }
    
    sortable = Sortable.create(cardGrid.value, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      onEnd: function(evt) {
        // DOM에서 실제 순서 확인
        const cardElements = cardGrid.value.children
        const newOrder = []
        for (let i = 0; i < cardElements.length; i++) {
          const cardName = cardElements[i].getAttribute('data-card-name')
          if (cardName) {
            newOrder.push(cardName)
          }
        }
        
        // 카드 배열 업데이트
        const reorderedCards = []
        newOrder.forEach(name => {
          const card = cards.value.find(c => c.name === name)
          if (card) {
            reorderedCards.push(card)
          }
        })
        
        cards.value = reorderedCards
        
        // 저장
        saveCardOrder()
      }
    })
  }
}

onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/')
  }
  
  loadCardOrder()
  
  // Sortable 초기화 전에 약간의 지연
  setTimeout(() => {
    initSortable()
  }, 100)
  
  fetchRecentActivities()
})

onUnmounted(() => {
  if (sortable) {
    sortable.destroy()
  }
})
</script>

<style scoped>
.dashboard-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  cursor: grab;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  color: white;
  text-align: center;
}

.card-content {
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.card.not-implemented {
  opacity: 0.6;
  cursor: not-allowed;
}

.card.not-implemented:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 드래그 중일 때 커서 변경 */
.card:active {
  cursor: grabbing;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.card-label {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.card-desc {
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.4;
}

.coming-soon {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

/* Sortable.js 스타일 */
.sortable-ghost {
  opacity: 0.5;
  transform: rotate(5deg);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
}

.sortable-chosen {
  transform: scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
}



.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  margin: 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.card-grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 0 10px;
}

.reset-order-btn {
  background: #95a5a6;
  border: none;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: background-color 0.3s ease;
}

.reset-order-btn:hover {
  background: #7f8c8d;
}

.debug-buttons {
  display: flex;
  gap: 10px;
}

.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



.retry-activity-btn {
  background: #3498db;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  margin-top: 15px;
  transition: background-color 0.3s ease;
}

.retry-activity-btn:hover {
  background: #2980b9;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
  cursor: pointer;
}

.activity-click-hint {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-top: 8px;
  opacity: 0.7;
}

.activity-icon {
  font-size: 1.5rem;
  width: 40px;
  text-align: center;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.activity-time {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.activity-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 40px 20px;
  color: #7f8c8d;
}

.loading-spinner {
  font-size: 1.5rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.no-activities {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.no-activity-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.no-activity-text {
  font-size: 1rem;
}





.sortable-ghost {
  opacity: 0.5;
  background: #f8f9fa !important;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    justify-content: center;
  }
}
</style>
