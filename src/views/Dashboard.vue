<template>
  <Layout ref="layoutRef">
    <div class="dashboard-container">
      <!-- 카드 그리드 -->
      <div class="card-grid" ref="cardGrid">
        <div
          v-for="(card, index) in cards"
          :key="card.name"
          class="card"
          :style="{ backgroundColor: card.color }"
          @click="goTo(card.route)"
          :class="{ 'not-implemented': !card.implemented }"
        >
          <div class="drag-handle">⋮⋮</div>
          <div class="card-icon">{{ card.icon }}</div>
          <div class="card-label">{{ card.label }}</div>
          <div class="card-desc">{{ card.description }}</div>
          <div v-if="!card.implemented" class="coming-soon">🚧 개발 중</div>
        </div>
      </div>



      <!-- 최근 활동 -->
      <div class="recent-activity">
        <h2 class="section-title">📋 최근 활동</h2>
        <div v-if="activitiesLoading" class="activity-loading">
          <div class="loading-spinner">⏳</div>
          <span>활동 로그 로딩 중...</span>
        </div>

        <div v-else class="activity-list">
          <div v-if="recentActivities.length === 0" class="no-activities">
            <div class="no-activity-icon">📋</div>
            <div class="no-activity-text">최근 활동이 없습니다</div>
            <button class="retry-activity-btn" @click="fetchRecentActivities">다시 시도</button>
          </div>
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item" @click="goToActivityLogs">
            <div class="activity-icon">{{ getActivityIcon(activity.type) }}</div>
            <div class="activity-content">
              <div class="activity-title">{{ activity.title || getActivityTitle(activity.type) }}</div>
              <div class="activity-time">{{ activity.time }}</div>
            </div>
            <div class="activity-click-hint">클릭하여 상세보기</div>
          </div>
        </div>
      </div>


    </div>
  </Layout>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import Layout from '../components/Layout.vue'
import Sortable from 'sortablejs'

const layoutRef = ref(null)
const router = useRouter()
const cardGrid = ref(null)
let sortable = null

// 카드 데이터
const cards = ref([
  { 
    name: 'crop', 
    label: '작물관리', 
    icon: '🌾', 
    color: '#27ae60', 
    route: '/crop',
    description: '작물 재배 및 관리',
    implemented: true
  },
  { 
    name: 'equipment', 
    label: '장비관리', 
    icon: '🛠️', 
    color: '#2980b9', 
    route: '/equipment',
    description: '농기계 및 장비 관리',
    implemented: false
  },
  { 
    name: 'greenhouse', 
    label: '비닐하우스', 
    icon: '🏠', 
    color: '#16a085', 
    route: '/greenhouse',
    description: '온실 환경 관리',
    implemented: true
  },
  { 
    name: 'shipment', 
    label: '출하관리', 
    icon: '📦', 
    color: '#d35400', 
    route: '/shipment',
    description: '작물 출하 및 판매',
    implemented: true
  },
  { 
    name: 'auction', 
    label: '경매조회', 
    icon: '📊', 
    color: '#e67e22', 
    route: '/auction',
    description: '도매시장 경매 정보',
    implemented: true
  },
  { 
    name: 'products', 
    label: '상품관리', 
    icon: '🌾', 
    color: '#f39c12', 
    route: '/products',
    description: '농산물 상품 관리',
    implemented: true
  },
  { 
    name: 'salary', 
    label: '급여관리', 
    icon: '💰', 
    color: '#8e44ad', 
    route: '/salary',
    description: '직원 급여 계산 및 관리',
    implemented: true
  },
  { 
    name: 'staff', 
    label: '직원관리', 
    icon: '👷', 
    color: '#7f8c8d', 
    route: '/staff',
    description: '직원 정보 및 관리',
    implemented: true
  },
  { 
    name: 'calendar', 
    label: '일정관리', 
    icon: '📅', 
    color: '#f39c12', 
    route: '/calendar',
    description: '재배, 수확, 출하 일정 관리',
    implemented: true
  },
  { 
    name: 'harvest-dashboard', 
    label: '수확관리', 
    icon: '🌾', 
    color: '#e74c3c', 
    route: '/harvest-dashboard',
    description: '스마트 수확 상태 관리',
    implemented: true
  },
  { 
    name: 'harvest-test', 
    label: '수확테스트', 
    icon: '🧪', 
    color: '#9b59b6', 
    route: '/harvest-test',
    description: '수확 관리 시스템 테스트',
    implemented: true
  },
  { 
    name: 'stats', 
    label: '통계', 
    icon: '📈', 
    color: '#2c3e50', 
    route: '/stats',
    description: '농장 운영 통계',
    implemented: true
  },
  { 
    name: 'activity-logs', 
    label: '활동 내역', 
    icon: '📊', 
    color: '#667eea', 
    route: '/activity-logs',
    description: '전체 활동 로그 조회 및 검색',
    implemented: true
  }
])

// 최근 활동 데이터
const recentActivities = ref([])
const activitiesLoading = ref(false)

// 카드 순서 저장
const saveCardOrder = () => {
  const order = cards.value.map(card => card.name).join(',')
  document.cookie = `dashboardCardOrder=${order}; path=/; max-age=31536000`
}

const fetchRecentActivities = async () => {
  activitiesLoading.value = true
  
  try {
    const response = await fetch('http://localhost:8080/api/activity-logs/recent', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.ok) {
      const data = await response.json()
      console.log('최근 활동 로그 API 응답:', data)
      
      // 통합 API 가이드에 따른 ApiResponse<T> 형식 처리
      if (data.success && data.data && Array.isArray(data.data) && data.data.length > 0) {
        recentActivities.value = data.data.slice(0, 5).map(log => ({
          id: log.id,
          title: log.errorMessage || log.action || log.category || '활동',
          time: formatTimeAgo(log.createdAt),
          type: log.category || 'default'
        }))
        console.log('최근 활동 로그 로드 완료:', recentActivities.value.length, '개')
      } else {
        console.warn('최근 활동 로그 데이터 없음')
        recentActivities.value = []
      }
    } else {
      console.error('최근 활동 로그 API 호출 실패:', response.status)
      recentActivities.value = []
    }
  } catch (error) {
    console.error('최근 활동 로그 API 호출 오류:', error)
    recentActivities.value = []
  } finally {
    activitiesLoading.value = false
  }
}

// 시간 포맷팅 (상대적 시간)
const formatTimeAgo = (timestamp) => {
  if (!timestamp) return '시간 정보 없음'
  
  const now = new Date()
  const logTime = new Date(timestamp)
  const diffMs = now - logTime
  const diffMinutes = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
  
  if (diffMinutes < 1) return '방금 전'
  if (diffMinutes < 60) return `${diffMinutes}분 전`
  if (diffHours < 24) return `${diffHours}시간 전`
  if (diffDays < 7) return `${diffDays}일 전`
  
  return logTime.toLocaleDateString('ko-KR')
}



const getActivityIcon = (type) => {
  const iconMap = {
    'AUTH': '🔐',
    'CROP': '🌾',
    'PRODUCT': '📦',
    'HARVEST': '🌾',
    'ERROR': '⚠️',
    'harvest': '🌾',
    'planting': '🌱',
    'shipment': '📦',
    'equipment': '🛠️',
    'salary': '💰',
    'auction': '📊',
    'weather': '🌤️',
    'alert': '⚠️',
    'maintenance': '🔧',
    'default': '📋'
  }
  return iconMap[type] || iconMap.default
}

const getActivityTitle = (type) => {
  const titleMap = {
    'AUTH': '인증 활동',
    'CROP': '작물 관리',
    'PRODUCT': '상품 관리',
    'HARVEST': '수확 활동',
    'ERROR': '오류 메시지',
    'WARN': '경고 메시지',
    'INFO': '정보 메시지',
    'harvest': '수확 활동',
    'planting': '재배 활동',
    'shipment': '출하 활동',
    'equipment': '장비 관리',
    'salary': '급여 관리',
    'auction': '경매 활동',
    'weather': '날씨 정보',
    'alert': '알림 확인',
    'maintenance': '정비 활동',
    'default': '시스템 활동'
  }
  return titleMap[type] || titleMap.default
}

const loadCardOrder = () => {
  const cookies = document.cookie.split(';')
  const cardOrderCookie = cookies.find(cookie => cookie.trim().startsWith('dashboardCardOrder='))
  
  if (cardOrderCookie) {
    const order = cardOrderCookie.split('=')[1].split(',')
    const orderedCards = []
    
    order.forEach(name => {
      const card = cards.value.find(c => c.name === name)
      if (card) orderedCards.push(card)
    })
    
    cards.value.forEach(card => {
      if (!orderedCards.find(oc => oc.name === card.name)) {
        orderedCards.push(card)
      }
    })
    
    cards.value = orderedCards
  }
}

const goTo = (route) => {
  router.push(route)
}

const goToActivityLogs = () => {
  router.push('/activity-logs')
}



const initSortable = () => {
  if (cardGrid.value) {
    sortable = Sortable.create(cardGrid.value, {
      animation: 150,
      ghostClass: 'sortable-ghost',
      onEnd: () => {
        saveCardOrder()
      }
    })
  }
}

onMounted(() => {
  if (layoutRef.value) {
    layoutRef.value.addToHistory('/')
  }
  
  loadCardOrder()
  initSortable()
  fetchRecentActivities()
})

onUnmounted(() => {
  if (sortable) {
    sortable.destroy()
  }
})
</script>

<style scoped>
.dashboard-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  color: white;
  text-align: center;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
}

.card.not-implemented {
  opacity: 0.6;
  cursor: not-allowed;
}

.card.not-implemented:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.drag-handle {
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.2rem;
  opacity: 0.7;
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.card-label {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 10px;
}

.card-desc {
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.4;
}

.coming-soon {
  position: absolute;
  top: 10px;
  left: 10px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}



.section-title {
  margin: 0 0 20px 0;
  color: #2c3e50;
  font-size: 1.5rem;
}

.recent-activity {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}



.retry-activity-btn {
  background: #3498db;
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  margin-top: 15px;
  transition: background-color 0.3s ease;
}

.retry-activity-btn:hover {
  background: #2980b9;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
  cursor: pointer;
}

.activity-click-hint {
  font-size: 0.8rem;
  color: #7f8c8d;
  margin-top: 8px;
  opacity: 0.7;
}

.activity-icon {
  font-size: 1.5rem;
  width: 40px;
  text-align: center;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 5px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.activity-time {
  font-size: 0.9rem;
  color: #7f8c8d;
}

.activity-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 40px 20px;
  color: #7f8c8d;
}

.loading-spinner {
  font-size: 1.5rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.no-activities {
  text-align: center;
  padding: 40px 20px;
  color: #7f8c8d;
}

.no-activity-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.no-activity-text {
  font-size: 1rem;
}





.sortable-ghost {
  opacity: 0.5;
  background: #f8f9fa !important;
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .action-btn {
    justify-content: center;
  }
}
</style>
