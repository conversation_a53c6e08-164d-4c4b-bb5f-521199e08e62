// Service Worker for Web Push Notifications
const CACHE_NAME = 'farm-management-v1';
const urlsToCache = [
  '/',
  '/harvest-dashboard',
  '/harvest-test'
];

// Install event
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then((cache) => {
        console.log('Opened cache');
        return cache.addAll(urlsToCache);
      })
  );
});

// Fetch event
self.addEventListener('fetch', (event) => {
  event.respondWith(
    caches.match(event.request)
      .then((response) => {
        // Return cached version or fetch from network
        return response || fetch(event.request);
      })
  );
});

// Push event - 알림 수신 처리
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);
  
  let notificationData = {
    title: '스마트팜 알림',
    body: '새로운 알림이 있습니다.',
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    tag: 'farm-notification',
    requireInteraction: true,
    actions: [
      {
        action: 'view',
        title: '확인하기',
        icon: '/favicon.ico'
      },
      {
        action: 'dismiss',
        title: '닫기',
        icon: '/favicon.ico'
      }
    ]
  };

  // 서버에서 전송된 데이터가 있으면 사용
  if (event.data) {
    try {
      const data = event.data.json();
      notificationData = {
        ...notificationData,
        ...data
      };
    } catch (error) {
      console.error('Failed to parse push data:', error);
    }
  }

  const promiseChain = self.registration.showNotification(
    notificationData.title,
    notificationData
  );

  event.waitUntil(promiseChain);
});

// Notification click event - 알림 클릭 처리
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  event.notification.close();

  if (event.action === 'view') {
    // 알림 확인 시 수확 관리 페이지로 이동
    event.waitUntil(
      clients.openWindow('/harvest-dashboard')
    );
  } else if (event.action === 'dismiss') {
    // 알림 닫기
    event.notification.close();
  } else {
    // 기본 클릭 시 수확 관리 페이지로 이동
    event.waitUntil(
      clients.openWindow('/harvest-dashboard')
    );
  }
});

// Background sync - 백그라운드 동기화
self.addEventListener('sync', (event) => {
  console.log('Background sync event:', event);
  
  if (event.tag === 'harvest-sync') {
    event.waitUntil(syncHarvestData());
  }
});

// 알림 권한 요청
self.addEventListener('pushsubscriptionchange', (event) => {
  console.log('Push subscription changed:', event);
  
  event.waitUntil(
    self.registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: urlBase64ToUint8Array('YOUR_VAPID_PUBLIC_KEY')
    })
    .then((subscription) => {
      console.log('New subscription:', subscription);
      // 서버에 새로운 구독 정보 전송
      return fetch('/api/notifications/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(subscription)
      });
    })
  );
});

// VAPID 키 변환 함수
function urlBase64ToUint8Array(base64String) {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

// 수확 데이터 동기화
async function syncHarvestData() {
  try {
    const response = await fetch('/api/crops/reminder-targets');
    const data = await response.json();
    
    if (data.success && data.data.length > 0) {
      // 미완료 수확이 있으면 알림 표시
      await self.registration.showNotification('수확 완료 알림', {
        body: `${data.data.length}개의 작물이 수확 완료를 기다리고 있습니다.`,
        icon: '/favicon.ico',
        tag: 'harvest-reminder',
        requireInteraction: true,
        actions: [
          {
            action: 'view',
            title: '확인하기'
          }
        ]
      });
    }
  } catch (error) {
    console.error('Failed to sync harvest data:', error);
  }
} 