# 🌾 스마트 팜 관리 시스템

농장 관리 시스템의 프론트엔드 애플리케이션입니다.

## 🚀 주요 기능

- 📊 **실시간 대시보드**: 농장 현황 모니터링
- 🌾 **수확 관리**: 상태 추적, 자동 완료, 실패 관리
- 📈 **통계 및 차트**: Chart.js 기반 데이터 시각화
- 🔔 **웹 푸시 알림**: Service Worker 기반 실시간 알림
- 🧪 **API 테스트**: 수확 관리 API 통합 테스트
- 🏗️ **종합 관리**: 작물, 온실, 제품, 경매, 코드 관리

## 🛠️ 기술 스택

- **프레임워크**: Vue 3 (Composition API)
- **빌드 도구**: Vite
- **HTTP 클라이언트**: Axios
- **차트 라이브러리**: Chart.js
- **스타일링**: CSS Grid & Flexbox
- **알림**: Web Push API + Service Worker

## 📦 설치 및 실행

```bash
# 의존성 설치
npm install

# 개발 서버 실행
npm run dev
```

## 🌐 개발 서버

- **URL**: http://localhost:3001
- **Vue DevTools**: http://localhost:3001/__devtools__/

## 📚 문서

- **프로젝트 개요**: [PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md)
- **API 가이드**: [docs/FRONTEND_API_INTEGRATED_GUIDE.md](./docs/FRONTEND_API_INTEGRATED_GUIDE.md)

## 🔗 API 연동

백엔드 API와 연동하여 실시간 데이터를 처리합니다.

### 주요 API 엔드포인트
- 수확 상태 관리: `/api/harvest/*`
- 작물 관리: `/api/crops/*`
- 온실 관리: `/api/greenhouses/*`
- 통계 조회: `/api/statistics/*`

## 📝 ToDo

자세한 ToDo 리스트는 [PROJECT_OVERVIEW.md](./PROJECT_OVERVIEW.md)를 참조하세요.

---

*마지막 업데이트: 2024년 12월* 