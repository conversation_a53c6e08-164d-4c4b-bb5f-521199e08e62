# 🚜 농장 관리 시스템 - 프론트엔드 API 가이드

## 📋 목차
- [시스템 개요](#시스템-개요)
- [API 기본 정보](#api-기본-정보)
- [경매 데이터 API](#경매-데이터-api)
- [작물 관리 API](#작물-관리-api)
- [직원 관리 API](#직원-관리-api)
- [실시간 통신 (SSE)](#실시간-통신-sse)
- [에러 처리](#에러-처리)
- [예제 코드](#예제-코드)

---

## 🎯 시스템 개요

농장 관리 시스템의 백엔드 API입니다. 경매 데이터 수집, 작물 관리, 직원 관리 기능을 제공합니다.

### 🌐 기본 정보
- **Base URL**: `http://localhost:8080`
- **Content-Type**: `application/json`
- **인코딩**: UTF-8

---

## 🔌 API 기본 정보

### 응답 형식
모든 API는 다음과 같은 통일된 응답 형식을 사용합니다:

```json
{
  "success": true,
  "message": "성공 메시지",
  "data": {
    // 실제 데이터
  }
}
```

### 에러 응답
```json
{
  "success": false,
  "message": "에러 메시지",
  "error": "상세 에러 정보"
}
```

---

## 📊 경매 데이터 API

### 1. 특정일 데이터 수동 캐싱 (SSE)

특정 날짜의 경매 데이터를 가져와 DB에 저장하는 작업을 시작하고, 그 진행 과정을 실시간으로 스트리밍합니다.

- **엔드포인트**: `GET /api/auction/cache-stream/{date}`
- **메서드**: `GET`
- **Path Variable**: 
  - `date`: `YYYYMMDD` 형식의 날짜 (예: `20240726`)
- **응답 타입**: `text/event-stream`

#### SSE 이벤트 상세

- **`onmessage`**: 이벤트 수신 시 호출됩니다. 데이터는 JSON 형식입니다.
- **`onerror`**: 오류 발생 또는 연결 종료 시 호출됩니다.

#### 이벤트 데이터 구조

| 필드명 | 타입 | 설명 |
|---|---|---|
| `log` | `String` | 실시간 로그 메시지 |
| `progress` | `Number` | 작업 진행률 (0 ~ 100) |

#### 예시 이벤트 데이터
- 로그 메시지 수신 시:
  ```json
  {"log":"데이터 총 개수 확인 중..."}
  ```
- 진행률 업데이트 시:
  ```json
  {"progress":50.5}
  ```

#### 프론트엔드 연동 예제 (JavaScript)

```javascript
const date = '20240726'; // 예시 날짜
const eventSource = new EventSource(`/api/auction/cache-stream/${date}`);

// 메시지 수신 리스너
eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);

    // 로그 메시지 처리
    if (data.log) {
        console.log('Log:', data.log);
        // UI에 로그를 추가하는 로직
    }

    // 진행률 처리
    if (data.progress) {
        console.log('Progress:', data.progress);
        // UI의 프로그레스 바를 업데이트하는 로직
    }
};

// 에러 및 연결 종료 처리
eventSource.onerror = function(error) {
    console.error('SSE Error:', error);
    // 연결 종료 또는 오류 상황에 대한 UI 처리
    eventSource.close();
};
```

### 2. 캐시된 경매 데이터 조회

DB에 저장된 경매 데이터를 조회합니다.

- **엔드포인트**: `GET /api/auction/data`
- **메서드**: `GET`
- **설명**: 모든 경매 데이터를 조회합니다.

---

- **엔드포인트**: `GET /api/auction/data/{productName}`
- **메서드**: `GET`
- **설명**: 특정 품목의 경매 데이터를 조회합니다.
- **Path Variable**:
  - `productName`: 조회할 품목명 (예: `상추`)

#### 응답 데이터 구조 (공통)

```json
{
  "success": true,
  "message": "조회 완료 메시지",
  "data": [
    {
      "pumNm": "상추",
      "adjDt": "20240725",
      "saleQty": 1500,
      "saleAmt": 45000,
      "sanji": "경기 광주시",
      "injungGubun": "인증"
    },
    // ... 추가 데이터
  ]
}
```

---

## 🌱 작물 관리 API

### 1. 작물 목록 조회

**엔드포인트**: `GET /api/crops`

**응답 예시**:
```json
{
  "success": true,
  "message": "작물 목록 조회 완료",
  "data": [
    {
      "id": 1,
      "name": "상추",
      "category": "채소",
      "plantingDate": "2024-01-01",
      "harvestDate": "2024-03-01",
      "status": "재배중"
    }
  ]
}
```

### 2. 작물 등록

**엔드포인트**: `POST /api/crops`

**요청 본문**:
```json
{
  "name": "상추",
  "category": "채소",
  "plantingDate": "2024-01-01",
  "expectedHarvestDate": "2024-03-01"
}
```

### 3. 작물 수정

**엔드포인트**: `PUT /api/crops/{id}`

**요청 본문**:
```json
{
  "name": "상추",
  "category": "채소",
  "plantingDate": "2024-01-01",
  "harvestDate": "2024-03-01",
  "status": "수확완료"
}
```

### 4. 작물 삭제

**엔드포인트**: `DELETE /api/crops/{id}`

---

## 👥 직원 관리 API

### 1. 직원 목록 조회

**엔드포인트**: `GET /api/employees`

**응답 예시**:
```json
{
  "success": true,
  "message": "직원 목록 조회 완료",
  "data": [
    {
      "id": 1,
      "name": "홍길동",
      "position": "농부",
      "phone": "010-1234-5678",
      "hireDate": "2024-01-01",
      "status": "재직중"
    }
  ]
}
```

### 2. 직원 등록

**엔드포인트**: `POST /api/employees`

**요청 본문**:
```json
{
  "name": "홍길동",
  "position": "농부",
  "phone": "010-1234-5678",
  "hireDate": "2024-01-01"
}
```

### 3. 직원 수정

**엔드포인트**: `PUT /api/employees/{id}`

### 4. 직원 삭제

**엔드포인트**: `DELETE /api/employees/{id}`

---

## 📡 실시간 통신 (SSE)

### SSE 연결 방법

```javascript
// SSE 연결
const eventSource = new EventSource('/api/auction/garak/load-date-data/stream?targetDate=20241201');

// 메시지 수신
eventSource.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    switch (data.type) {
        case 'log':
            console.log('로그:', data.message);
            break;
            
        case 'progress':
            updateProgressBar(data.progress);
            break;
            
        case 'complete':
            handleComplete(data);
            eventSource.close();
            break;
    }
};

// 에러 처리
eventSource.onerror = function(error) {
    console.error('SSE 에러:', error);
    eventSource.close();
};
```

### SSE 이벤트 타입

| 타입 | 설명 | 데이터 구조 |
|------|------|-------------|
| `log` | 로그 메시지 | `{type: "log", message: "메시지", timestamp: 1234567890}` |
| `progress` | 진행률 업데이트 | `{type: "progress", progress: 50, message: "진행률: 50%", timestamp: 1234567890}` |
| `complete` | 작업 완료 | `{type: "complete", success: true, message: "완료", savedCount: 45, targetDate: "20241201"}` |

---

## ⚠️ 에러 처리

### HTTP 상태 코드

| 코드 | 설명 |
|------|------|
| 200 | 성공 |
| 400 | 잘못된 요청 |
| 404 | 리소스 없음 |
| 500 | 서버 내부 오류 |

### 에러 응답 예시

```json
{
  "success": false,
  "message": "날짜 형식이 올바르지 않습니다",
  "error": "targetDate must be in YYYYMMDD format"
}
```

---

## 💻 예제 코드

### JavaScript (Fetch API)

```javascript
// 특정일 데이터 가져오기
async function loadDateData(targetDate) {
    try {
        const response = await fetch('/api/auction/garak/load-date-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ targetDate })
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('성공:', result.data);
            return result.data;
        } else {
            console.error('실패:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('네트워크 오류:', error);
        throw error;
    }
}

// 어제 데이터 가져오기
async function loadYesterdayData() {
    try {
        const response = await fetch('/api/auction/garak/load-yesterday-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            console.log('어제 데이터 로드 완료:', result.data);
            return result.data;
        } else {
            console.error('실패:', result.message);
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('네트워크 오류:', error);
        throw error;
    }
}

// 실시간 진행률 확인
function loadDateDataWithProgress(targetDate) {
    const eventSource = new EventSource(`/api/auction/garak/load-date-data/stream?targetDate=${targetDate}`);
    
    eventSource.onmessage = function(event) {
        const data = JSON.parse(event.data);
        
        switch (data.type) {
            case 'log':
                addLog(data.message);
                break;
                
            case 'progress':
                updateProgress(data.progress);
                break;
                
            case 'complete':
                handleComplete(data);
                eventSource.close();
                break;
        }
    };
    
    eventSource.onerror = function(error) {
        console.error('SSE 에러:', error);
        eventSource.close();
    };
}

// 데이터 상태 확인
async function getDataStatus() {
    try {
        const response = await fetch('/api/auction/garak/status');
        const result = await response.json();
        
        if (result.success) {
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        console.error('상태 확인 실패:', error);
        throw error;
    }
}
```

### React Hook 예제

```javascript
import { useState, useEffect } from 'react';

// 데이터 로드 Hook
export function useAuctionData() {
    const [data, setData] = useState(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState(null);

    const loadDateData = async (targetDate) => {
        setLoading(true);
        setError(null);
        
        try {
            const response = await fetch('/api/auction/garak/load-date-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ targetDate })
            });
            
            const result = await response.json();
            
            if (result.success) {
                setData(result.data);
            } else {
                setError(result.message);
            }
        } catch (err) {
            setError(err.message);
        } finally {
            setLoading(false);
        }
    };

    return { data, loading, error, loadDateData };
}

// SSE Hook
export function useSSEProgress(targetDate) {
    const [progress, setProgress] = useState(0);
    const [logs, setLogs] = useState([]);
    const [isComplete, setIsComplete] = useState(false);

    useEffect(() => {
        if (!targetDate) return;

        const eventSource = new EventSource(`/api/auction/garak/load-date-data/stream?targetDate=${targetDate}`);
        
        eventSource.onmessage = (event) => {
            const data = JSON.parse(event.data);
            
            switch (data.type) {
                case 'log':
                    setLogs(prev => [...prev, data.message]);
                    break;
                    
                case 'progress':
                    setProgress(data.progress);
                    break;
                    
                case 'complete':
                    setIsComplete(true);
                    eventSource.close();
                    break;
            }
        };
        
        eventSource.onerror = (error) => {
            console.error('SSE 에러:', error);
            eventSource.close();
        };
        
        return () => eventSource.close();
    }, [targetDate]);

    return { progress, logs, isComplete };
}
```

### Vue.js 예제

```javascript
// Vue 3 Composition API
import { ref, reactive } from 'vue';

export function useAuctionAPI() {
    const data = ref(null);
    const loading = ref(false);
    const error = ref(null);

    const loadDateData = async (targetDate) => {
        loading.value = true;
        error.value = null;
        
        try {
            const response = await fetch('/api/auction/garak/load-date-data', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ targetDate })
            });
            
            const result = await response.json();
            
            if (result.success) {
                data.value = result.data;
            } else {
                error.value = result.message;
            }
        } catch (err) {
            error.value = err.message;
        } finally {
            loading.value = false;
        }
    };

    return {
        data,
        loading,
        error,
        loadDateData
    };
}
```

---

## 📝 주의사항

1. **날짜 형식**: 모든 날짜는 `YYYYMMDD` 형식으로 전송
2. **SSE 연결**: 실시간 진행률 확인 시 반드시 연결 해제 필요
3. **에러 처리**: 모든 API 호출에 적절한 에러 처리 구현
4. **타임아웃**: SSE 연결은 30분 후 자동 타임아웃
5. **중복 방지**: 동일한 날짜/품목 데이터는 중복 저장되지 않음

---

## 🔗 전체 API 엔드포인트 목록

| 메서드 | 엔드포인트 | 설명 |
|--------|------------|------|
| POST | `/api/auction/garak/load-date-data` | 특정일 데이터 가져오기 |
| POST | `/api/auction/garak/load-yesterday-data` | 어제 데이터 전체 가져오기 |
| GET | `/api/auction/garak/load-date-data/stream` | 실시간 진행률 (SSE) |
| GET | `/api/auction/garak/status` | 데이터 상태 확인 |
| GET | `/api/auction/garak/quality-status` | 데이터 품질 확인 |
| POST | `/api/auction/garak/cleanup` | 공백 데이터 정리 |
| GET | `/api/auction/garak` | 캐시된 데이터 조회 |
| GET | `/api/crops` | 작물 목록 조회 |
| POST | `/api/crops` | 작물 등록 |
| PUT | `/api/crops/{id}` | 작물 수정 |
| DELETE | `/api/crops/{id}` | 작물 삭제 |
| GET | `/api/employees` | 직원 목록 조회 |
| POST | `/api/employees` | 직원 등록 |
| PUT | `/api/employees/{id}` | 직원 수정 |
| DELETE | `/api/employees/{id}` | 직원 삭제 |

---

**버전**: 1.0.0  
**최종 업데이트**: 2024년 12월  
**문서 작성자**: 백엔드 개발팀 