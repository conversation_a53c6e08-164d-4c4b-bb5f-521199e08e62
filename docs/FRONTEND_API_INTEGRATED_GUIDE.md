# 🔗 **프론트엔드 API 연동 완전 가이드**

> 스마트팜 관리 시스템의 프론트엔드-백엔드 연동을 위한 공식 문서입니다.

## 📋 **목차**
1. [개요](#개요)
2. [API 응답 형식](#api-응답-형식)
3. [농작물 관리 API](#농작물-관리-api)
4. [자동 수확 관리 API](#자동-수확-관리-api)
5. [비닐하우스 관리 API](#비닐하우스-관리-api)
6. [직원 관리 API](#직원-관리-api)
7. [경매 데이터 API](#경매-데이터-api)
8. [날씨 정보 API](#날씨-정보-api)
9. [JavaScript 서비스 클래스](#javascript-서비스-클래스)
10. [Vue 컴포넌트 예시](#vue-컴포넌트-예시)

---

## 🎯 **개요**

이 가이드는 스마트팜 관리 시스템의 모든 API 엔드포인트와 프론트엔드 연동 방법을 제공합니다.
모든 API는 RESTful 설계 원칙을 따르며, 일관된 응답 형식을 사용합니다.

---

## 📊 **API 응답 형식**

### **성공 응답**
```json
{
  "success": true,
  "message": "작업이 성공적으로 완료되었습니다.",
  "data": {
    // 실제 데이터
  },
  "error": null,
  "timestamp": "2024-12-01T10:30:00.000Z"
}
```

### **오류 응답**
```json
{
  "success": false,
  "message": "오류가 발생했습니다.",
  "data": null,
  "error": "VALIDATION_ERROR",
  "timestamp": "2024-12-01T10:30:00.000Z"
}
```

---

## 🌱 **농작물 관리 API**

### **1. 작물 목록 조회**
```http
GET /api/crops
```

**응답 예시:**
```json
{
  "success": true,
  "message": "작물 목록 조회 완료",
    "data": [
      {
      "id": 28,
      "cropName": "토마토",
      "productCode": "토마토",
      "category": "채소",
      "description": "토마토 재배",
      "plantingDate": "2024-01-01T00:00:00",
      "expectedHarvestDate": "2024-04-01T00:00:00",
      "harvestCycleDays": 90,
      "harvestStatus": "COMPLETED",
      "harvestProgress": 100,
      "actualHarvestDate": "2024-04-01T10:30:00",
      "actualYield": 150.5,
      "isActive": true
    }
  ]
}
```

### **2. 작물 등록**
```http
POST /api/crops
Content-Type: application/json

{
  "cropName": "토마토",
  "productCode": "토마토",
  "category": "채소",
  "description": "토마토 재배",
  "plantingDate": "2024-01-01T00:00:00",
  "expectedHarvestDate": "2024-04-01T00:00:00",
  "harvestCycleDays": 90
}
```

### **3. 작물 상세 조회**
```http
GET /api/crops/{id}
```

### **4. 작물 수정**
```http
PUT /api/crops/{id}
Content-Type: application/json

{
  "cropName": "토마토",
  "productCode": "토마토",
      "category": "채소",
  "description": "토마토 재배 (수정됨)",
  "plantingDate": "2024-01-01T00:00:00",
  "expectedHarvestDate": "2024-04-01T00:00:00",
  "harvestCycleDays": 90
}
```

### **5. 작물 삭제**
```http
DELETE /api/crops/{id}
```

---

## 🌾 **수확 상태 관리 API**

### **6. 수확 상태 업데이트**
```http
PATCH /api/crops/{id}/harvest-status
Content-Type: application/json

{
  "harvestStatus": "IN_PROGRESS",
  "harvestProgress": 50,
  "failureReason": null,
  "failureNotes": null
}
```

**상태 값:**
- `NOT_STARTED`: 수확 시작 전
- `IN_PROGRESS`: 수확 진행중
- `COMPLETED`: 수확 완료
- `FAILED`: 수확 실패

### **7. 수확 완료 처리**
```http
POST /api/crops/{id}/complete
Content-Type: application/json

{
  "actualYield": 150.5
}
```

### **8. 수확 실패 처리**
```http
POST /api/crops/{id}/harvest-failed
Content-Type: application/json

{
  "failureReason": "DISEASE",
  "failureNotes": "흰가루병 발생으로 수확 불가"
}
```

**실패 사유:**
- `DISEASE`: 질병
- `PEST`: 해충
- `WEATHER`: 기상 악화
- `NUTRIENT_DEFICIENCY`: 영양 부족
- `WATER_ISSUE`: 수분 문제
- `EQUIPMENT_FAILURE`: 장비 고장
- `HUMAN_ERROR`: 인적 오류
- `OTHER`: 기타

### **9. 수확 진행률 업데이트**
```http
PATCH /api/crops/{id}/progress
Content-Type: application/json

{
  "progress": 75
}
```

### **10. 상태별 작물 조회**
```http
GET /api/crops/harvest-status/{status}
```

**예시:**
- `GET /api/crops/harvest-status/COMPLETED`
- `GET /api/crops/harvest-status/FAILED`
- `GET /api/crops/harvest-status/IN_PROGRESS`

### **11. 수확 실패 사유 목록**
```http
GET /api/crops/failure-reasons
```

---

## 🤖 **자동 수확 관리 API**

### **12. 자동 수확 완료 처리**
```http
POST /api/crops/{id}/auto-complete
```

**응답 예시:**
```json
{
  "success": true,
  "message": "자동 수확 완료 처리 완료",
  "data": {
    "id": 30,
    "cropName": "상추",
    "harvestStatus": "COMPLETED",
    "harvestProgress": 100,
    "actualHarvestDate": "2024-12-01T10:30:00",
    "actualYield": 0.0,
    "failureNotes": "자동 완료 처리됨"
  }
}
```

### **13. 작물 아카이브 처리**
```http
POST /api/crops/{id}/archive
```

### **14. 미완료 수확 통계**
```http
GET /api/crops/incomplete-statistics
```

**응답 예시:**
```json
{
  "success": true,
  "message": "미완료 수확 통계 조회 완료",
  "data": {
    "totalIncomplete": 5,
    "inProgressCount": 0,
    "notStartedCount": 5,
    "veryOldCount": 2,
    "inProgressCrops": [],
    "notStartedCrops": [
      {
        "id": 31,
      "cropName": "양배추",
        "harvestStatus": "NOT_STARTED"
      }
    ],
    "veryOldCrops": [
      {
        "id": 32,
        "cropName": "고추",
        "harvestStatus": "NOT_STARTED"
      }
    ]
  }
}
```

### **15. 주간 수확 현황 리포트**
```http
GET /api/crops/weekly-report
```

**응답 예시:**
```json
{
  "success": true,
  "message": "주간 수확 현황 리포트 생성 완료",
  "data": {
    "period": "주간 리포트 (2024-11-24 ~ 2024-12-01)",
    "completedCount": 2,
    "failedCount": 1,
    "inProgressCount": 0,
    "totalYield": 150.5,
    "successRate": 66.67,
    "completedCrops": [
      {
        "id": 28,
        "cropName": "토마토",
        "actualYield": 150.5
      }
    ],
    "failedCrops": [
      {
        "id": 29,
        "cropName": "오이",
        "failureReason": "DISEASE"
      }
    ]
  }
}
```

### **16. 수확 완료 알림 대상**
```http
GET /api/crops/reminder-targets
```

---

## 🏠 **비닐하우스 관리 API**

### **17. 하우스 목록 조회**
```http
GET /api/greenhouses
```

### **18. 하우스 등록**
```http
POST /api/greenhouses
Content-Type: application/json

{
  "name": "A동 하우스",
  "location": "농장 북쪽",
  "size": 1000,
  "status": "EMPTY"
}
```
**상태 (`status`) 값:**
- `IN_USE`: 사용 중
- `EMPTY`: 비어있음
- `MAINTENANCE`: 유지보수 중

### **19. 하우스 상세 조회**
```http
GET /api/greenhouses/{id}
```

### **20. 하우스 수정**
```http
PUT /api/greenhouses/{id}
Content-Type: application/json

{
  "name": "A동 하우스 (수정됨)",
  "location": "농장 북쪽",
  "size": 1000,
  "status": "IN_USE"
}
```

### **21. 하우스 삭제**
```http
DELETE /api/greenhouses/{id}
```

---

## 👥 **직원 관리 API**

### **22. 직원 목록 조회**
```http
GET /api/employees
```

### **23. 활성 직원 조회**
```http
GET /api/employees/active
```

### **24. 직원 등록**
```http
POST /api/employees
Content-Type: application/json

{
  "name": "김철수",
  "nationality": "KOREA",
  "position": "FARM_WORKER",
  "hourlyWage": 15000,
  "startDate": "2024-01-01"
}
```
**국적 (`nationality`) 값:**
- `KOREA`, `USA`, `CHINA`, `VIETNAM`, `OTHER` 등 (전체 목록은 백엔드 Enum 참조)

**직책 (`position`) 값:**
- `FARM_WORKER`: 농장 직원
- `MANAGER`: 관리자
- `ADMINISTRATOR`: 최고 관리자

### **25. 직원 상세 조회**
```http
GET /api/employees/{id}
```

### **26. 직원 수정**
```http
PUT /api/employees/{id}
Content-Type: application/json

{
  "name": "김철수",
  "nationality": "KOREA",
  "position": "FARM_WORKER",
  "hourlyWage": 16000,
  "startDate": "2024-01-01"
}
```

### **27. 직원 삭제**
```http
DELETE /api/employees/{id}
```

---

## 📊 **경매 데이터 API**

어머니를 위한 특별 페이지의 핵심 API입니다. 매일 업데이트되는 경매 데이터를 조회하고, 필요시 수동으로 최신 데이터를 가져올 수 있습니다.

> **⚠️ 참고: 외부 데이터 소스**
> 경매 데이터의 필드명(`PUM_NM`, `ADJ_DT` 등)은 공공 데이터 포털에서 제공하는 원본 형식을 그대로 따르므로, 시스템의 다른 API(camelCase)와 명명 규칙이 다를 수 있습니다.

### **28. 특정 날짜의 경매 데이터 수동 캐싱**
관리자 또는 사용자가 특정 날짜의 경매 데이터를 수동으로 가져와 DB에 저장(캐시)합니다. 스케줄러가 매일 자동으로 전날 데이터를 가져오지만, 특정 날짜의 데이터가 필요할 때 사용할 수 있습니다.

```http
POST /api/auction/cache-day/{date}
```
**경로 파라미터:**
- `{date}`: `YYYYMMDD` 형식의 날짜 (예: `20250723`)

**쿼리 파라미터 (선택):**
- `dryRun=true`: DB에 실제 저장하지 않고 API 호출 및 데이터 처리만 테스트합니다.

**성공 응답 예시:**
```json
{
  "success": true,
  "message": "일간 데이터 캐싱 작업 완료. Dry Run: false",
  "data": {
    "saved": 1551,
    "duplicate": 5706,
    "failed": 0,
    "total": 7257
  },
  "error": null,
  "timestamp": "..."
}
```

### **29. 캐시된 전체 경매 데이터 조회**
DB에 저장된 모든 경매 데이터를 조회합니다. 데이터 양이 많을 수 있으므로 주의해야 합니다.

```http
GET /api/auction/data
```

### **30. 특정 품목의 경매 데이터 조회**
DB에 저장된 데이터 중 특정 품목에 해당하는 모든 경매 데이터를 조회합니다.

```http
GET /api/auction/data/{productName}
```
**경로 파라미터:**
- `{productName}`: 조회할 품목명 (예: `상추`)


**데이터 조회 성공 응답 예시 (`/data` 또는 `/data/{productName}`):**
```json
{
  "success": true,
  "message": "캐시된 품목별 경매 데이터 조회 완료",
  "data": {
    "success": true,
    "message": "DB 캐시에서 데이터 조회 완료",
    "dataCount": 150,
    "data": [
      {
        "PUM_NM": "상추",
        "ADJ_DT": "20250723",
        "SALE_QTY": 4,          // 거래량 (단위: kg 등)
        "SALE_AMT": 15000,      // 총 거래액 (원)
        "SANJI": "경기 하남시",   // 산지
        "INJUNG_GUBUN": "일반"  // 등급 또는 인정 구분
      }
    ]
  },
  "error": null,
  "timestamp": "..."
}
```

> **💡 제안: 응답 구조 일관성**
> 현재 경매 데이터 조회 API는 `data` 객체 내에 `data` 필드가 중첩된 구조(`"data": {"success": true, "data": [...]}`)를 가집니다. 이는 API 전체의 응답 형식 일관성을 해칠 수 있습니다. 가능하다면 백엔드에서 중첩 구조를 제거하여 아래와 같이 단순화하는 것을 권장합니다.
> ```json
> {
>   "success": true,
>   "message": "DB 캐시에서 데이터 조회 완료",
>   "data": [
>     { ... }
>   ],
>   "error": null,
>   "timestamp": "..."
> }
> ```

---

## 🌤️ **날씨 정보 API**

### **31. 현재 날씨 조회**
```http
GET /api/weather/current
```

### **32. 날씨 예보 조회**
```http
GET /api/weather/forecast
```

---

## 🛠️ **JavaScript 서비스 클래스**

> **✨ 참고: 프로젝트 아키텍처**
> 아래 예시는 `fetch` API를 사용한 기본적인 서비스 클래스입니다. 하지만 현재 프로젝트는 **Vue 3 Composition API**를 기반으로 하며, `src/composables` 디렉토리에 `use...` 형태의 커스텀 훅들이 존재합니다.
> 
> 실제 개발 시에는 `src/utils/axios.js`에 설정된 `axios` 인스턴스를 활용하는 **composable 함수**를 만들어 사용하는 것을 적극 권장합니다. 이는 코드의 재사용성을 높이고, 전역 에러 핸들링 및 인증 로직을 통합 관리하는 데 유리합니다.

### **농작물 관리 서비스**
```javascript
import api from '@/utils/axios'; // axios 인스턴스 사용 권장

class CropService {
    constructor() {
        this.baseUrl = '/api/crops';
    }

    // 작물 목록 조회
    async getCrops() {
        const response = await api.get(this.baseUrl);
        return response.data; // axios는 자동으로 .json()을 처리하고 data 객체에 응답을 담아줍니다.
    }

    // 작물 등록
    async createCrop(cropData) {
        const response = await api.post(this.baseUrl, cropData);
        return response.data;
    }

    // 작물 상세 조회
    async getCrop(id) {
        const response = await api.get(`${this.baseUrl}/${id}`);
        return response.data;
    }

    // 작물 수정
    async updateCrop(id, cropData) {
        const response = await api.put(`${this.baseUrl}/${id}`, cropData);
        return response.data;
    }

    // 작물 삭제
    async deleteCrop(id) {
        const response = await api.delete(`${this.baseUrl}/${id}`);
        return response.data;
    }

    // 수확 상태 업데이트
    async updateHarvestStatus(id, statusData) {
        const response = await api.patch(`${this.baseUrl}/${id}/harvest-status`, statusData);
        return response.data;
    }

    // 수확 완료 처리
    async completeHarvest(id, actualYield) {
        const response = await api.post(`${this.baseUrl}/${id}/complete`, { actualYield });
        return response.data;
    }
  
    // 수확 실패 처리
    async failHarvest(id, failureReason, failureNotes) {
        const response = await api.post(`${this.baseUrl}/${id}/harvest-failed`, { failureReason, failureNotes });
        return response.data;
    }

    // 수확 진행률 업데이트
    async updateProgress(id, progress) {
        const response = await api.patch(`${this.baseUrl}/${id}/progress`, { progress });
        return response.data;
    }

    // 상태별 작물 조회
    async getCropsByStatus(status) {
        const response = await api.get(`${this.baseUrl}/harvest-status/${status}`);
        return response.data;
    }

    // 수확 실패 사유 목록
    async getFailureReasons() {
        const response = await api.get(`${this.baseUrl}/failure-reasons`);
        return response.data;
    }
}
```

### **자동 수확 관리 서비스**
```javascript
import api from '@/utils/axios';

class HarvestManagementService {
    constructor() {
        this.baseUrl = '/api/crops';
    }

    // 자동 수확 완료 처리
    async autoCompleteHarvest(cropId) {
        const response = await api.post(`${this.baseUrl}/${cropId}/auto-complete`);
        return response.data;
    }

    // 작물 아카이브 처리
    async archiveCrop(cropId) {
        const response = await api.post(`${this.baseUrl}/${cropId}/archive`);
        return response.data;
    }

    // 미완료 수확 통계
    async getIncompleteStatistics() {
        const response = await api.get(`${this.baseUrl}/incomplete-statistics`);
        return response.data;
    }

    // 주간 수확 현황 리포트
    async getWeeklyReport() {
        const response = await api.get(`${this.baseUrl}/weekly-report`);
        return response.data;
    }

    // 수확 완료 알림 대상
    async getReminderTargets() {
        const response = await api.get(`${this.baseUrl}/reminder-targets`);
        return response.data;
    }
}
```

### **비닐하우스 관리 서비스**
```javascript
import api from '@/utils/axios';

class GreenhouseService {
    constructor() {
        this.baseUrl = '/api/greenhouses';
    }

    // 하우스 목록 조회
    async getGreenhouses() {
        const response = await api.get(this.baseUrl);
        return response.data;
    }

    // 하우스 등록
    async createGreenhouse(greenhouseData) {
        const response = await api.post(this.baseUrl, greenhouseData);
        return response.data;
    }

    // 하우스 상세 조회
    async getGreenhouse(id) {
        const response = await api.get(`${this.baseUrl}/${id}`);
        return response.data;
    }

    // 하우스 수정
    async updateGreenhouse(id, greenhouseData) {
        const response = await api.put(`${this.baseUrl}/${id}`, greenhouseData);
        return response.data;
    }

    // 하우스 삭제
    async deleteGreenhouse(id) {
        const response = await api.delete(`${this.baseUrl}/${id}`);
        return response.data;
    }
}
```

---

## ⚛️ **Vue 컴포넌트 예시**

### **수확 관리 대시보드 (`HarvestDashboard.vue`)**
다음은 `Composition API`와 `<script setup>` 구문을 사용한 Vue 3 컴포넌트 예시입니다. 서비스 클래스 대신 `useHarvestManagement`와 같은 composable 함수를 사용하여 데이터를 관리하는 것을 권장합니다.

```vue
<template>
  <div class="harvest-dashboard">
    <h2>🌾 수확 관리 대시보드</h2>

    <div v-if="loading" class="loading">데이터를 불러오는 중...</div>

    <div v-if="!loading && error" class="error-message">
      데이터 로드에 실패했습니다: {{ error }}
    </div>

    <template v-if="!loading && !error">
      <!-- 미완료 수확 통계 -->
      <div v-if="statistics" class="statistics-card">
        <h3>📊 미완료 수확 현황</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-number">{{ statistics.totalIncomplete }}</span>
            <span class="stat-label">총 미완료</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ statistics.inProgressCount }}</span>
            <span class="stat-label">진행중</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ statistics.notStartedCount }}</span>
            <span class="stat-label">시작 전</span>
          </div>
          <div class="stat-item warning">
            <span class="stat-number">{{ statistics.veryOldCount }}</span>
            <span class="stat-label">오래된 미완료</span>
          </div>
        </div>
      </div>

      <!-- 알림 대상 목록 -->
      <div v-if="reminderTargets.length > 0" class="reminder-card">
        <h3>🔔 수확 완료 알림 대상</h3>
        <div class="reminder-list">
          <div v-for="crop in reminderTargets" :key="crop.id" class="reminder-item">
            <div class="crop-info">
              <span class="crop-name">{{ crop.cropName }}</span>
              <span class="crop-date">
                예상 수확일: {{ formatDate(crop.expectedHarvestDate) }}
              </span>
            </div>
            <div class="crop-actions">
              <button class="btn btn-success btn-sm" @click="handleCompleteHarvest(crop.id, 0)">
                수확 완료
              </button>
              <button class="btn btn-warning btn-sm" @click="handleAutoComplete(crop.id)">
                자동 완료
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 작물 목록 -->
      <div class="crops-list">
        <h3>🌱 작물 목록</h3>
        <div class="crops-grid">
          <div v-for="crop in crops" :key="crop.id" :class="['crop-card', crop.harvestStatus.toLowerCase()]">
            <div class="crop-header">
              <h4>{{ crop.cropName }}</h4>
              <span :class="['status-badge', crop.harvestStatus.toLowerCase()]">
                {{ getStatusText(crop.harvestStatus) }}
              </span>
            </div>

            <div class="crop-details">
              <p><strong>카테고리:</strong> {{ crop.category }}</p>
              <p><strong>파종일:</strong> {{ formatDate(crop.plantingDate) }}</p>
              <p><strong>예상 수확일:</strong> {{ formatDate(crop.expectedHarvestDate) }}</p>

              <div v-if="crop.harvestStatus === 'IN_PROGRESS'" class="progress-section">
                <label>수확 진행률: {{ crop.harvestProgress }}%</label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  :value="crop.harvestProgress"
                  @change="handleProgressUpdate(crop.id, $event.target.value)"
                />
              </div>

              <p v-if="crop.harvestStatus === 'COMPLETED'">
                <strong>실제 수확량:</strong> {{ crop.actualYield }}kg
              </p>
              <p v-if="crop.harvestStatus === 'FAILED'">
                <strong>실패 사유:</strong> {{ crop.failureReason }}
              </p>
            </div>

            <div class="crop-actions">
              <template v-if="crop.harvestStatus === 'NOT_STARTED'">
                <button class="btn btn-primary btn-sm" @click="handleStartHarvest(crop.id)">
                  수확 시작
                </button>
                <button class="btn btn-warning btn-sm" @click="handleAutoComplete(crop.id)">
                  자동 완료
                </button>
              </template>
              <template v-if="crop.harvestStatus === 'IN_PROGRESS'">
                <button class="btn btn-success btn-sm" @click="handleCompleteHarvest(crop.id, 0)">
                  수확 완료
                </button>
                <button class="btn btn-danger btn-sm" @click="handleFailHarvest(crop.id, 'OTHER', '기타 사유')">
                  수확 실패
                </button>
              </template>
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
// 실제 프로젝트에서는 composable 함수를 임포트해서 사용합니다.
// import { useHarvestManagement } from '@/composables/useHarvestManagement';
// import { useNotifications } from '@/composables/useNotifications';

// 아래는 composable이 없다는 가정 하에 서비스 클래스를 직접 사용하는 예시입니다.
import { CropService } from '../services/CropService';
import { HarvestManagementService } from '../services/HarvestManagementService';


const crops = ref([]);
const statistics = ref(null);
const reminderTargets = ref([]);
const loading = ref(true);
const error = ref(null);

// 서비스 인스턴스 생성 (또는 composable에서 가져오기)
const cropService = new CropService();
const harvestService = new HarvestManagementService();
// const { showSuccess, showError } = useNotifications();

const formatDate = (dateString) => {
  if (!dateString) return '';
  return new Date(dateString).toLocaleDateString();
};

const getStatusText = (status) => {
    const statusMap = {
        'NOT_STARTED': '시작 전',
        'IN_PROGRESS': '진행중',
        'COMPLETED': '완료',
        'FAILED': '실패',
    };
    return statusMap[status] || status;
};

const loadData = async () => {
  try {
    loading.value = true;
    error.value = null;
    
    const [cropsRes, statsRes, reminderRes] = await Promise.all([
      cropService.getCrops(),
      harvestService.getIncompleteStatistics(),
      harvestService.getReminderTargets(),
    ]);

    if (cropsRes.success) crops.value = cropsRes.data;
    if (statsRes.success) statistics.value = statsRes.data;
    if (reminderRes.success) reminderTargets.value = reminderRes.data;

  } catch (e) {
    console.error('데이터 로드 실패:', e);
    error.value = e.message;
    // showError('데이터 로드 중 오류가 발생했습니다.');
  } finally {
    loading.value = false;
  }
};

onMounted(loadData);

const handleStartHarvest = async (cropId) => {
    try {
        const response = await cropService.updateHarvestStatus(cropId, { harvestStatus: 'IN_PROGRESS' });
        if (response.success) {
            // showSuccess('수확이 시작되었습니다!');
            alert('수확이 시작되었습니다!');
            loadData();
        } else {
            // showError(`작업 실패: ${response.message}`);
            alert(`작업 실패: ${response.message}`);
        }
    } catch (e) {
        console.error('수확 시작 오류:', e);
        // showError('수확 시작 중 오류 발생');
        alert('수확 시작 중 오류 발생');
    }
};

const handleProgressUpdate = async (cropId, progress) => {
    try {
        const response = await cropService.updateProgress(cropId, parseInt(progress, 10));
        if (response.success) {
            // 진행률 업데이트는 UI 피드백을 즉각적으로 주기 위해 상태를 직접 변경할 수도 있습니다.
            const crop = crops.value.find(c => c.id === cropId);
            if(crop) crop.harvestProgress = progress;
        } else {
            // showError('진행률 업데이트 실패');
            alert('진행률 업데이트 실패');
        }
    } catch (e) {
        console.error('진행률 업데이트 오류:', e);
        // showError('진행률 업데이트 중 오류 발생');
        alert('진행률 업데이트 중 오류 발생');
    }
};


const handleAutoComplete = async (cropId) => {
  try {
    const response = await harvestService.autoCompleteHarvest(cropId);
    if (response.success) {
      // showSuccess('자동 완료 처리되었습니다!');
      alert('자동 완료 처리되었습니다!');
      loadData();
    } else {
      // showError(`자동 완료 처리 실패: ${response.message}`);
      alert(`자동 완료 처리 실패: ${response.message}`);
    }
  } catch (e) {
    console.error('자동 완료 처리 오류:', e);
    // showError('자동 완료 처리 중 오류가 발생했습니다.');
    alert('자동 완료 처리 중 오류가 발생했습니다.');
  }
};

const handleCompleteHarvest = async (cropId, actualYield) => {
    const yieldValue = prompt("실제 수확량을 입력하세요(kg).", actualYield || '0');
    if (yieldValue === null) return; // 사용자가 취소한 경우

  try {
    const response = await cropService.completeHarvest(cropId, parseFloat(yieldValue));
    if (response.success) {
      // showSuccess('수확 완료 처리되었습니다!');
      alert('수확 완료 처리되었습니다!');
      loadData();
    } else {
      // showError(`수확 완료 처리 실패: ${response.message}`);
      alert(`수확 완료 처리 실패: ${response.message}`);
    }
  } catch (e) {
    console.error('수확 완료 처리 오류:', e);
    // showError('수확 완료 처리 중 오류가 발생했습니다.');
    alert('수확 완료 처리 중 오류가 발생했습니다.');
  }
};

const handleFailHarvest = async (cropId, failureReason, failureNotes) => {
    if (!confirm('정말로 이 작물의 수확을 실패 처리하시겠습니까?')) return;
  try {
    const response = await cropService.failHarvest(cropId, failureReason, failureNotes);
    if (response.success) {
      // showSuccess('수확 실패 처리되었습니다!');
      alert('수확 실패 처리되었습니다!');
      loadData();
    } else {
      // showError(`수확 실패 처리 실패: ${response.message}`);
      alert(`수확 실패 처리 실패: ${response.message}`);
    }
  } catch (e) {
    console.error('수확 실패 처리 오류:', e);
    // showError('수확 실패 처리 중 오류가 발생했습니다.');
    alert('수확 실패 처리 중 오류가 발생했습니다.');
  }
};
</script>

<style scoped>
.harvest-dashboard {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.loading, .error-message {
  text-align: center;
  padding: 40px;
  font-size: 1.2em;
  color: #6c757d;
}

.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
}

.statistics-card, .reminder-card, .crops-list {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 6px;
  transition: transform 0.2s;
}

.stat-item:hover {
    transform: translateY(-5px);
}

.stat-number {
  display: block;
  font-size: 2.5em;
  font-weight: 600;
  color: #007bff;
}

.stat-item.warning .stat-number {
    color: #ffc107;
}

.stat-label {
  display: block;
  margin-top: 5px;
  color: #6c757d;
  font-size: 0.9em;
}

.reminder-list {
  margin-top: 15px;
}

.reminder-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 15px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  margin-bottom: 10px;
  transition: background-color 0.2s;
}

.reminder-item:hover {
    background-color: #f8f9fa;
}

.crop-info .crop-name {
    font-weight: 600;
    margin-right: 15px;
}
.crop-info .crop-date {
    font-size: 0.9em;
    color: #6c757d;
}


.crops-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 15px;
}

.crop-card {
  border: 1px solid #e9ecef;
  border-left-width: 5px;
  border-radius: 8px;
  padding: 20px;
  background: white;
  transition: box-shadow 0.3s;
}

.crop-card:hover {
    box-shadow: 0 8px 15px rgba(0,0,0,0.07);
}

.crop-card.completed { border-left-color: #28a745; }
.crop-card.failed { border-left-color: #dc3545; }
.crop-card.in_progress { border-left-color: #ffc107; }
.crop-card.not_started { border-left-color: #6c757d; }

.crop-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.crop-header h4 {
    margin: 0;
}

.status-badge {
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: bold;
  text-transform: uppercase;
}

.status-badge.completed { background: #d4edda; color: #155724; }
.status-badge.failed { background: #f8d7da; color: #721c24; }
.status-badge.in_progress { background: #fff3cd; color: #856404; }
.status-badge.not_started { background: #e2e3e5; color: #383d41; }

.crop-details p {
    margin: 8px 0;
    font-size: 0.95em;
}

.crop-actions {
  margin-top: 20px;
  display: flex;
  gap: 10px;
  border-top: 1px solid #e9ecef;
  padding-top: 15px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9em;
  font-weight: 500;
  transition: background-color 0.2s, transform 0.1s;
}
.btn:hover {
    transform: scale(1.05);
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8em;
}

.btn-primary { background: #007bff; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-danger { background: #dc3545; color: white; }

.progress-section {
  margin: 15px 0;
}
.progress-section input[type="range"] {
  width: 100%;
  margin-top: 5px;
  cursor: pointer;
}
</style>
```

---

## 🎯 **사용 예시**

### **1. 수확 관리 대시보드 구현**
```javascript
// 서비스 인스턴스 생성
const cropService = new CropService();
const harvestService = new HarvestManagementService();

// 미완료 수확 통계 조회
const stats = await harvestService.getIncompleteStatistics();
console.log('미완료 수확:', stats.data.totalIncomplete);

// 자동 완료 처리
await harvestService.autoCompleteHarvest(30);
console.log('자동 완료 처리 완료');

// 주간 리포트 조회
const report = await harvestService.getWeeklyReport();
console.log('주간 성공률:', report.data.successRate + '%');
```

### **2. 실시간 수확 상태 업데이트**
```javascript
// 수확 진행률 업데이트
await cropService.updateProgress(28, 75);
console.log('진행률 75%로 업데이트');

// 수확 완료 처리
await cropService.completeHarvest(28, 150.5);
console.log('수확 완료: 150.5kg');

// 수확 실패 처리
await cropService.failHarvest(29, 'DISEASE', '흰가루병 발생');
console.log('수확 실패 처리 완료');
```

---

## 🚀 **완성된 기능**

✅ **31개 API 엔드포인트** 완전 구현  
✅ **자동 수확 관리 시스템** 완전 자동화  
✅ **실시간 상태 추적** 및 업데이트  
✅ **수확 실패 관리** 8가지 사유 분류  
✅ **알림 시스템** 자동 실행  
✅ **주간 리포트** 자동 생성  
✅ **데이터 정리** 자동 아카이브  
✅ **프론트엔드 연동** 완전 가이드  
✅ **Vue 컴포넌트** 예시 제공  
✅ **CSS 스타일링** 완전 구현  

---

**🎯 이제 프론트엔드에서 모든 기능을 완벽하게 사용할 수 있습니다! 🚀** 