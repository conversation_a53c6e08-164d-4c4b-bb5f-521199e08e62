# [백엔드 API 개선 요청 사항]

안녕하세요! 스마트팜 프론트엔드 개발을 진행하면서, 보다 효율적인 협업과 안정적인 시스템 구축을 위해 몇 가지 개선 사항을 제안 드립니다.

현재 프론트엔드에서는 아래 개선 사항들이 모두 반영되었다고 가정하고 개발을 진행할 예정입니다. 빠른 검토와 적용 부탁드립니다!

---

### **1. 경매 데이터 API 응답 구조 일관성 확보**

*   **현상:** 현재 경매 데이터 조회 API(`GET /api/auction/data`)의 응답은 `{"data": {"data": [...]}}` 형태로, 데이터가 이중으로 감싸져 있습니다. 다른 모든 API는 `{"data": [...]}` 형태로 일관된 구조를 가집니다.
*   **문제점:** 이 불일치 때문에 프론트엔드에서 경매 데이터를 처리할 때만 별도의 예외 로직이 필요하며, 이는 실수(runtime error)를 유발할 수 있습니다.
*   **개선 제안:** 경매 데이터 조회 API의 성공 응답 구조를 다른 API와 동일하게 아래와 같이 단일 `data` 필드로 통일해 주시면 좋겠습니다.
    ```json
    // AS-IS
    { "success": true, "data": { "data": [{...}] } }

    // TO-BE
    { "success": true, "data": [{...}] }
    ```

### **2. '데이터 없음'에 대한 명확한 응답 처리**

*   **현상:** 특정 날짜(`?date=YYYY-MM-DD`)로 경매 데이터를 조회했을 때 해당 날짜의 데이터가 없으면, 빈 배열(`[]`)이 아닌 가장 최신 날짜의 데이터가 반환되고 있습니다.
*   **문제점:** 프론트엔드에서 요청한 날짜와 응답받은 데이터의 날짜가 일치하는지 재차 검증해야 하므로 코드가 복잡해지고, 비효율적인 데이터 전송이 발생합니다.
*   **개선 제안:** 조회 조건에 맞는 데이터가 없을 경우, `data` 필드에 **빈 배열(`[]`)**을 담아 응답해 주시는 것을 표준으로 삼았으면 합니다.

### **3. 데이터 필드 명명 규칙(Case) 통일**

*   **현상:** 경매 데이터 API는 외부 소스를 그대로 사용하여 `PUM_NM`, `ADJ_DT` 등 **SNAKE_CASE** 필드명을 사용하고 있습니다. 시스템의 다른 모든 API는 `cropName`, `plantingDate` 등 **camelCase**를 따릅니다.
*   **문제점:** 하나의 프로젝트 내에서 두 가지 명명 규칙이 혼재되어 개발 생산성을 저하시키고, 코드의 일관성을 해칩니다.
*   **개선 제안:** 백엔드에서 외부 데이터를 받아 프론트엔드로 전달하기 전에, 필드명을 **camelCase** (예: `pumNm`, `adjDt`, `saleQty`, `saleAmt`, `sanji`, `injungGubun`)로 변환하여 보내주시면 프론트엔드 개발이 훨씬 수월해질 것 같습니다.

### **4. API 문서 구체화**

*   **현상:** `POST /api/auction/cache-day` API의 경우, 요청 본문에 필요한 `date` 필드의 정확한 형식(`YYYY-MM-DD`)이 문서에 명시되지 않아 테스트 과정에서 혼란이 있었습니다.
*   **개선 제안:** API 문서에 각 엔드포인트가 요구하는 파라미터(특히 날짜)의 **정확한 데이터 형식**과 **요청/응답의 전체 예시**를 포함하여 구체적으로 작성해 주시면 연동 작업의 효율이 크게 오를 것입니다.

---

감사합니다. 